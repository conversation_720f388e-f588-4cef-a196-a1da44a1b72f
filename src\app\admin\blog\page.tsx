"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Zap, Loader2, CheckCircle, AlertCircle, Trash2, Eye,
  Calendar, Clock, RefreshCw, Settings, BarChart3
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
}

export default function BlogAdminPage() {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch blog posts
  const fetchBlogPosts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/blog/list');
      const data = await response.json();
      
      if (data.success) {
        setBlogPosts(data.blogPosts || []);
      } else {
        setError(data.error || 'Failed to fetch blog posts');
      }
    } catch (err) {
      setError('Failed to fetch blog posts');
      console.error('Error fetching blog posts:', err);
    } finally {
      setLoading(false);
    }
  };

  // Generate new blog post
  const generateNewPost = async () => {
    try {
      setGenerating(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch('/api/blog/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (data.success) {
        setSuccess('Blog post generated successfully!');
        await fetchBlogPosts();
      } else {
        setError(data.error || 'Failed to generate blog post');
      }
    } catch (err) {
      setError('Failed to generate blog post');
      console.error('Error generating blog post:', err);
    } finally {
      setGenerating(false);
    }
  };

  // Delete blog post
  const deleteBlogPost = async (slug: string) => {
    if (!confirm('Are you sure you want to delete this blog post?')) {
      return;
    }

    try {
      const response = await fetch(`/api/blog/list?slug=${slug}`, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (data.success) {
        setSuccess('Blog post deleted successfully!');
        await fetchBlogPosts();
      } else {
        setError(data.error || 'Failed to delete blog post');
      }
    } catch (err) {
      setError('Failed to delete blog post');
      console.error('Error deleting blog post:', err);
    }
  };

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(' ').length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation spacing */}
      <div className="h-20 lg:h-24"></div>

      {/* Header */}
      <section className="py-12 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="flex items-center justify-between"
          >
            <motion.div variants={fadeInUp}>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Settings className="h-8 w-8 mr-3 text-[#1E3A8A]" />
                Blog Administration
              </h1>
              <p className="text-gray-600 mt-2">Manage blog posts and content</p>
            </motion.div>

            <motion.div variants={fadeInUp} className="flex space-x-4">
              <Button
                onClick={fetchBlogPosts}
                disabled={loading}
                variant="outline"
                className="border-gray-300"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              <Button
                onClick={generateNewPost}
                disabled={generating}
                className="bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white"
              >
                {generating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Generate New Post
                  </>
                )}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Status Messages */}
      <section className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-3"
            >
              <AlertCircle className="h-5 w-5 text-red-500" />
              <span className="text-red-700">{error}</span>
            </motion.div>
          )}

          {success && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-3"
            >
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span className="text-green-700">{success}</span>
            </motion.div>
          )}
        </div>
      </section>

      {/* Stats */}
      <section className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Posts</p>
                      <p className="text-3xl font-bold text-[#1E3A8A]">{blogPosts.length}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-[#FF6B35]" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Words</p>
                      <p className="text-3xl font-bold text-[#1E3A8A]">
                        {blogPosts.reduce((total, post) => total + post.content.split(' ').length, 0).toLocaleString()}
                      </p>
                    </div>
                    <Eye className="h-8 w-8 text-[#FF6B35]" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Avg. Reading Time</p>
                      <p className="text-3xl font-bold text-[#1E3A8A]">
                        {blogPosts.length > 0 
                          ? Math.round(blogPosts.reduce((total, post) => total + getReadingTime(post.content), 0) / blogPosts.length)
                          : 0
                        } min
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-[#FF6B35]" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts List */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="flex justify-center items-center py-20">
              <Loader2 className="h-8 w-8 animate-spin text-[#1E3A8A]" />
              <span className="ml-3 text-lg text-[#1E3A8A]">Loading blog posts...</span>
            </div>
          ) : blogPosts.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-20"
            >
              <div className="w-24 h-24 bg-gradient-to-r from-[#1E3A8A] to-[#FF6B35] rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="h-12 w-12 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-[#1E3A8A] mb-4">No Blog Posts Yet</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Generate your first blog post to get started with content creation.
              </p>
              <Button
                onClick={generateNewPost}
                disabled={generating}
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-8 py-4 text-lg font-bold rounded-xl"
              >
                {generating ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="h-5 w-5 mr-2" />
                    Generate First Post
                  </>
                )}
              </Button>
            </motion.div>
          ) : (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="space-y-6"
            >
              {blogPosts.map((post, index) => (
                <motion.div key={post.slug} variants={fadeInUp}>
                  <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-8">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4 mb-4">
                            <Badge className="bg-[#FF6B35]/10 text-[#FF6B35] border border-[#FF6B35]/20 px-3 py-1 text-xs font-semibold rounded-full">
                              Published
                            </Badge>
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Calendar className="h-4 w-4" />
                              <span>{formatDate(post.publishedAt)}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Clock className="h-4 w-4" />
                              <span>{getReadingTime(post.content)} min read</span>
                            </div>
                          </div>

                          <h3 className="text-2xl font-bold text-[#1E3A8A] mb-3 hover:text-[#FF6B35] transition-colors duration-300">
                            {post.title}
                          </h3>

                          <p className="text-gray-600 leading-relaxed mb-4 line-clamp-2">
                            {post.excerpt}
                          </p>

                          <div className="text-sm text-gray-500">
                            <strong>Slug:</strong> {post.slug}
                          </div>
                        </div>

                        <div className="flex space-x-2 ml-6">
                          <Button
                            onClick={() => window.open(`/blog/${post.slug}`, '_blank')}
                            variant="outline"
                            size="sm"
                            className="border-gray-300"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => deleteBlogPost(post.slug)}
                            variant="outline"
                            size="sm"
                            className="border-red-300 text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
      </section>
    </div>
  );
}
