import { NextRequest, NextResponse } from 'next/server';

// This endpoint will be called by Vercel Cron Jobs or external cron services
// to automatically generate blog posts 3 times daily

export async function GET(request: NextRequest) {
  try {
    console.log('🕐 CRON JOB: Starting automatic blog generation');
    console.log('⏰ Timestamp:', new Date().toISOString());
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    console.log('🌐 Base URL:', baseUrl);

    // Verify this is a legitimate cron request
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'your-secret-key-here';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      console.log('❌ CRON JOB: Unauthorized request');
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if we should generate a new post (avoid too many posts)
    const listUrl = `${baseUrl}/api/blog/list`;
    console.log('📥 Fetching blog list:', listUrl);
    const listResponse = await fetch(listUrl);
    console.log('📡 List response status:', listResponse.status, listResponse.statusText);
    const listData = await listResponse.json();
    console.log('📊 List payload summary:', {
      success: listData?.success,
      count: Array.isArray(listData?.blogPosts) ? listData.blogPosts.length : undefined,
    });
    
    if (listData.success && listData.blogPosts) {
      const recentPosts = listData.blogPosts.filter((post: { publishedAt: string }) => {
        const postDate = new Date(post.publishedAt);
        const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);
        return postDate > sixHoursAgo;
      });

      if (recentPosts.length > 0) {
        console.log('⏭️ CRON JOB: Recent post exists, skipping generation');
        return NextResponse.json({
          success: true,
          message: 'Recent post exists, skipping generation',
          recentPostsCount: recentPosts.length
        });
      }
    }

    // Generate new blog post
    console.log('🚀 CRON JOB: Generating new blog post');
    const generateUrl = `${baseUrl}/api/blog/generate`;
    console.log('📤 Calling generate endpoint:', generateUrl);
    const generateResponse = await fetch(generateUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    console.log('📡 Generate response status:', generateResponse.status, generateResponse.statusText);
    let generateData: { success?: boolean; blogPost?: { title?: string; slug?: string; publishedAt?: string }; error?: string; details?: string } = {};
    try {
      generateData = await generateResponse.json();
    } catch (e) {
      const text = await generateResponse.text();
      console.log('❌ Generate response not JSON (truncated):', text.slice(0, 1200));
      throw new Error('Generate endpoint did not return JSON');
    }

    if (generateData.success && generateData.blogPost) {
      console.log('✅ CRON JOB: Blog post generated successfully');
      console.log('📝 Title:', generateData.blogPost.title);
      console.log('🔗 Slug:', generateData.blogPost.slug);

      return NextResponse.json({
        success: true,
        message: 'Blog post generated successfully',
        blogPost: {
          title: generateData.blogPost.title,
          slug: generateData.blogPost.slug,
          publishedAt: generateData.blogPost.publishedAt
        }
      });
    } else {
      console.log('❌ CRON JOB: Failed to generate blog post');
      if (generateData?.error || generateData?.details) {
        console.log('🧾 Error details:', {
          error: generateData.error,
          details: generateData.details,
        });
      }
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to generate blog post',
          details: generateData.error
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('💥 CRON JOB: Exception occurred:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Also support POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request);
}
