"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  FileText, ArrowRight, CheckCircle, Shield, Scale, 
  AlertTriangle, Info, Clock, Mail, Phone
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const sections = [
  {
    id: "acceptance",
    title: "1. Acceptance of Terms",
    content: [
      "By accessing and using WebforLeads' services, you accept and agree to be bound by the terms and provision of this agreement.",
      "If you do not agree to abide by the above, please do not use this service.",
      "These terms apply to all visitors, users, and others who access or use our services."
    ]
  },
  {
    id: "services",
    title: "2. Description of Services",
    content: [
      "WebforLeads provides digital marketing services including but not limited to web design, SEO, Google Ads management, social media marketing, and software development.",
      "We reserve the right to modify, suspend, or discontinue any aspect of our services at any time.",
      "All services are provided on an 'as is' and 'as available' basis."
    ]
  },
  {
    id: "payment",
    title: "3. Payment Terms",
    content: [
      "Payment terms are specified in individual service agreements and invoices.",
      "All fees are non-refundable unless otherwise specified in writing.",
      "Late payments may incur additional charges as specified in your service agreement.",
      "We reserve the right to suspend services for non-payment."
    ]
  },
  {
    id: "intellectual-property",
    title: "4. Intellectual Property Rights",
    content: [
      "Upon full payment, clients retain ownership of custom-developed websites and content created specifically for them.",
      "WebforLeads retains ownership of proprietary methodologies, tools, and general business processes.",
      "Clients grant WebforLeads the right to use project work in portfolios and case studies (with anonymization if requested).",
      "Third-party tools and platforms remain the property of their respective owners."
    ]
  },
  {
    id: "confidentiality",
    title: "5. Confidentiality",
    content: [
      "WebforLeads agrees to maintain the confidentiality of all client information and data.",
      "We will not disclose client information to third parties without explicit consent, except as required by law.",
      "Clients agree to keep WebforLeads' proprietary methods and strategies confidential.",
      "This confidentiality obligation survives termination of our agreement."
    ]
  },
  {
    id: "liability",
    title: "6. Limitation of Liability",
    content: [
      "WebforLeads' liability is limited to the amount paid for services in the 12 months preceding any claim.",
      "We are not liable for indirect, incidental, special, or consequential damages.",
      "We do not guarantee specific results from marketing campaigns, though we strive for optimal performance.",
      "Clients are responsible for backing up their own data and content."
    ]
  },
  {
    id: "termination",
    title: "7. Termination",
    content: [
      "Either party may terminate services with 30 days written notice.",
      "WebforLeads may terminate services immediately for non-payment or breach of terms.",
      "Upon termination, clients retain ownership of completed work and paid-for deliverables.",
      "Ongoing service fees are prorated to the termination date."
    ]
  },
  {
    id: "governing-law",
    title: "8. Governing Law",
    content: [
      "These terms are governed by the laws of England and Wales.",
      "Any disputes will be resolved through the courts of England and Wales.",
      "If any provision is found unenforceable, the remainder of the terms remain in effect.",
      "These terms constitute the entire agreement between parties."
    ]
  }
];

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-24 lg:py-32 bg-gradient-to-b from-white to-[#F9FAFB] overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5" />
          <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-[#4CAF50]/10 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-br from-[#FF6B35]/10 to-transparent rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="text-center space-y-8"
          >
            <motion.div variants={fadeInUp}>
              <Badge 
                variant="outline" 
                className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-[#1E3A8A]/5 to-[#4CAF50]/5 border-[#1E3A8A]/20 text-[#1E3A8A] text-base font-semibold rounded-full"
              >
                <FileText className="h-5 w-5" />
                <span>Legal Information</span>
              </Badge>
            </motion.div>

            <motion.h1 
              variants={fadeInUp}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-[#111827] leading-tight"
            >
              <span className="block">Terms &</span>
              <span className="block bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] bg-clip-text text-transparent">
                Conditions
              </span>
            </motion.h1>

            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Please read these terms and conditions carefully before using our services. 
              These terms govern your relationship with WebforLeads.
            </motion.p>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-6 text-sm text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>Last updated: January 2024</span>
              </div>
              <div className="flex items-center space-x-2">
                <Scale className="h-4 w-4" />
                <span>Governed by UK Law</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-12"
          >
            {sections.map((section, index) => (
              <motion.div key={section.id} variants={fadeInUp}>
                <Card className="p-8 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl">
                  <h2 className="text-2xl font-bold text-[#111827] mb-6">{section.title}</h2>
                  <div className="space-y-4">
                    {section.content.map((paragraph, idx) => (
                      <p key={idx} className="text-[#6B7280] leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Important Notice */}
      <section className="py-16 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
          >
            <Card className="p-8 border-0 bg-gradient-to-br from-[#FF6B35]/5 to-white shadow-lg shadow-black/5 rounded-2xl border-l-4 border-l-[#FF6B35]">
              <div className="flex items-start space-x-4">
                <AlertTriangle className="h-6 w-6 text-[#FF6B35] flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-lg font-bold text-[#111827] mb-2">Important Notice</h3>
                  <p className="text-[#6B7280] leading-relaxed mb-4">
                    These terms and conditions are subject to change. We will notify clients of any material changes 
                    via email or through our website. Continued use of our services after changes constitutes acceptance 
                    of the new terms.
                  </p>
                  <p className="text-[#6B7280] leading-relaxed">
                    If you have any questions about these terms, please contact us before using our services.
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827]"
            >
              Questions About Our Terms?
            </motion.h2>
            
            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Our team is here to help clarify any questions you may have about our terms and conditions.
            </motion.p>

            <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-10 py-4 text-lg font-semibold rounded-xl transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <Mail className="h-5 w-5 mr-2" />
                Contact Legal Team
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                className="border-[#1E3A8A]/20 text-[#1E3A8A] hover:bg-[#1E3A8A] hover:text-white px-10 py-4 text-lg font-semibold rounded-xl transition-all duration-300"
              >
                <Phone className="h-5 w-5 mr-2" />
                Call: 020 8123 4567
              </Button>
            </motion.div>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-8 text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-[#4CAF50]" />
                <span>UK Law Protected</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-[#4CAF50]" />
                <span>Fair & Transparent</span>
              </div>
              <div className="flex items-center space-x-2">
                <Info className="h-5 w-5 text-[#4CAF50]" />
                <span>Regular Updates</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
