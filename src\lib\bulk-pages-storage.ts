import fs from 'fs';
import path from 'path';

// Interfaces
export interface GeneratedPage {
  id: string;
  keyword: string;
  location: string;
  slug: string;
  title: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  createdAt: string;
  filePath?: string;
  error?: string;
  progress?: number;
}

export interface BulkGenerationJob {
  id: string;
  name: string;
  templateId: string;
  templateName: string;
  keywords: string[];
  locations: string[];
  status: 'pending' | 'running' | 'completed' | 'paused' | 'error';
  progress: number;
  totalPages: number;
  completedPages: number;
  createdAt: string;
  estimatedCompletion?: string;
  pages: GeneratedPage[];
}

// Get the bulk pages storage directory
function getBulkPagesStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'bulk-pages-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Save bulk generation job to file system
export function saveBulkJob(job: BulkGenerationJob): void {
  const storageDir = getBulkPagesStorageDir();
  const filename = `job-${job.id}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    console.log('💾 Saving bulk generation job...');
    console.log('🔖 Job ID:', job.id);
    console.log('📁 File:', filename);

    fs.writeFileSync(filepath, JSON.stringify(job, null, 2));
    console.log('✅ Bulk generation job saved successfully to:', filepath);
  } catch (error) {
    console.error('❌ Error saving bulk generation job:', error);
    throw new Error('Failed to save bulk generation job');
  }
}

// Get bulk job by ID
export function getBulkJobById(jobId: string): BulkGenerationJob | null {
  const storageDir = getBulkPagesStorageDir();
  const filename = `job-${jobId}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf-8');
      return JSON.parse(content) as BulkGenerationJob;
    }
    return null;
  } catch (error) {
    console.error('❌ Error reading bulk generation job:', error);
    return null;
  }
}

// Update bulk job
export function updateBulkJob(jobId: string, updates: Partial<BulkGenerationJob>): BulkGenerationJob | null {
  const job = getBulkJobById(jobId);
  if (!job) return null;

  const updatedJob = { ...job, ...updates };
  saveBulkJob(updatedJob);
  return updatedJob;
}
