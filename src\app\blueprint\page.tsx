"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import AppointmentForm from "@/components/forms/appointment-form";
import {
  Target, ArrowRight, CheckCircle, TrendingUp, Users,
  Zap, BarChart3, Rocket, Shield, Search,
  Clock, Lightbulb, Settings, Trophy, Sparkles, Award
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const blueprintPhases = [
  {
    phase: "01",
    title: "Discovery & Strategy",
    description: "We dive deep into your business, market, and competition to create a data-driven strategy that sets the foundation for success.",
    duration: "Week 1-2",
    deliverables: [
      "Comprehensive business audit",
      "Competitor analysis report",
      "Target audience research",
      "Custom strategy roadmap",
      "KPI framework setup"
    ],
    icon: <Search className="h-8 w-8" />,
    color: "from-[#1E3A8A] to-[#1E3A8A]/80"
  },
  {
    phase: "02",
    title: "Foundation & Setup",
    description: "We build the technical foundation and implement the core systems needed to support your digital growth.",
    duration: "Week 3-4",
    deliverables: [
      "Website optimization",
      "Analytics implementation",
      "Conversion tracking setup",
      "SEO foundation work",
      "Campaign infrastructure"
    ],
    icon: <Settings className="h-8 w-8" />,
    color: "from-[#FF6B35] to-[#FF6B35]/80"
  },
  {
    phase: "03",
    title: "Launch & Execution",
    description: "We launch your campaigns and begin executing the strategy, with careful monitoring and real-time adjustments.",
    duration: "Week 5-8",
    deliverables: [
      "Campaign launches",
      "Content creation",
      "Ad creative development",
      "Initial optimization",
      "Performance monitoring"
    ],
    icon: <Rocket className="h-8 w-8" />,
    color: "from-[#4CAF50] to-[#4CAF50]/80"
  },
  {
    phase: "04",
    title: "Optimization & Scale",
    description: "We continuously optimize performance and scale successful campaigns to maximize your ROI and business growth.",
    duration: "Ongoing",
    deliverables: [
      "Performance optimization",
      "A/B testing programs",
      "Scaling successful campaigns",
      "Monthly strategy reviews",
      "Continuous improvement"
    ],
    icon: <TrendingUp className="h-8 w-8" />,
    color: "from-[#9C27B0] to-[#9C27B0]/80"
  }
];

const methodologies = [
  {
    icon: <BarChart3 className="h-6 w-6" />,
    title: "Data-Driven Decisions",
    description: "Every strategy and optimization is backed by comprehensive data analysis and proven methodologies."
  },
  {
    icon: <Target className="h-6 w-6" />,
    title: "Goal-Oriented Approach",
    description: "We align every action with your specific business objectives and key performance indicators."
  },
  {
    icon: <Lightbulb className="h-6 w-6" />,
    title: "Continuous Innovation",
    description: "We stay ahead of industry trends and continuously test new strategies to maintain your competitive edge."
  },
  {
    icon: <Users className="h-6 w-6" />,
    title: "Collaborative Partnership",
    description: "We work as an extension of your team, maintaining transparent communication throughout the process."
  }
];

const results = [
  {
    metric: "Average ROI Increase",
    value: "340%",
    description: "Within the first 6 months",
    icon: <TrendingUp className="h-6 w-6" />
  },
  {
    metric: "Lead Generation Boost",
    value: "280%",
    description: "Qualified leads increase",
    icon: <Target className="h-6 w-6" />
  },
  {
    metric: "Conversion Rate Improvement",
    value: "165%",
    description: "Website conversion optimization",
    icon: <Zap className="h-6 w-6" />
  },
  {
    metric: "Client Satisfaction",
    value: "98%",
    description: "Would recommend our services",
    icon: <Trophy className="h-6 w-6" />
  }
];

export default function BlueprintPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Target className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">OUR BLUEPRINT FOR PREDICTABLE GROWTH</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      From Strategy to
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Measurable Results.
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "280px" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Our proven 4-phase methodology has helped hundreds of UK businesses achieve{" "}
                    <span className="text-[#FF6B35] relative">
                      predictable, sustainable growth
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                    .
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Here&apos;s exactly how we transform your digital presence into a revenue-generating machine with our data-driven approach and proven strategies.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#4CAF50]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <CheckCircle className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#4CAF50] transition-colors duration-200">
                      4-Phase Methodology
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#1E3A8A]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <TrendingUp className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#1E3A8A] transition-colors duration-200">
                      Predictable Growth
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#FF6B35]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Trophy className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Proven Results
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {results.map((result, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="text-center p-8 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl">
                  <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white">
                    {result.icon}
                  </div>
                  <div className="text-4xl font-bold text-[#111827] mb-2">{result.value}</div>
                  <div className="text-lg font-semibold text-[#111827] mb-2">{result.metric}</div>
                  <div className="text-[#6B7280] text-sm">{result.description}</div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Blueprint Phases */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
            >
              The 4-Phase Blueprint
            </motion.h2>
            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] max-w-3xl mx-auto"
            >
              Our systematic approach ensures every project delivers measurable results and sustainable growth.
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-12"
          >
            {blueprintPhases.map((phase, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="p-8 lg:p-12 border-0 bg-white shadow-lg shadow-black/5 rounded-3xl">
                  <div className="grid lg:grid-cols-3 gap-8 items-center">
                    
                    {/* Phase Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center space-x-6 mb-6">
                        <div className={`w-20 h-20 bg-gradient-to-r ${phase.color} rounded-2xl flex items-center justify-center text-white`}>
                          {phase.icon}
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-[#6B7280] mb-1">Phase {phase.phase}</div>
                          <h3 className="text-3xl font-bold text-[#111827] mb-2">{phase.title}</h3>
                          <Badge variant="outline" className="bg-[#F9FAFB] border-[#1E3A8A]/20 text-[#1E3A8A]">
                            <Clock className="h-3 w-3 mr-1" />
                            {phase.duration}
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-lg text-[#6B7280] leading-relaxed mb-6">
                        {phase.description}
                      </p>
                    </div>

                    {/* Deliverables */}
                    <div>
                      <h4 className="text-lg font-bold text-[#111827] mb-4">Key Deliverables</h4>
                      <div className="space-y-3">
                        {phase.deliverables.map((deliverable, idx) => (
                          <div key={idx} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-[#4CAF50] flex-shrink-0" />
                            <span className="text-[#6B7280]">{deliverable}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Methodologies Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
            >
              Our Core Methodologies
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-[#6B7280] max-w-3xl mx-auto"
            >
              The principles and practices that ensure consistent, exceptional results for every client.
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {methodologies.map((methodology, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="h-full p-8 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl hover:shadow-xl transition-all duration-300 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white">
                    {methodology.icon}
                  </div>
                  <h3 className="text-xl font-bold text-[#111827] mb-4">{methodology.title}</h3>
                  <p className="text-[#6B7280] leading-relaxed">{methodology.description}</p>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Process Guarantee */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center"
          >
            <Card className="p-12 border-0 bg-gradient-to-br from-[#1E3A8A]/5 to-[#FF6B35]/5 shadow-lg shadow-black/5 rounded-3xl">
              <motion.div variants={fadeInUp} className="space-y-8">
                <div className="w-20 h-20 bg-gradient-to-r from-[#4CAF50] to-[#4CAF50]/80 rounded-2xl flex items-center justify-center mx-auto">
                  <Shield className="h-10 w-10 text-white" />
                </div>

                <div>
                  <h3 className="text-3xl lg:text-4xl font-bold text-[#111827] mb-4">
                    Our Blueprint Guarantee
                  </h3>
                  <p className="text-xl text-[#6B7280] leading-relaxed mb-8">
                    We&apos;re so confident in our blueprint that we guarantee measurable improvements within 90 days, or we&apos;ll work for free until you see results.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                  <div>
                    <div className="text-2xl font-bold text-[#111827] mb-2">90-Day</div>
                    <div className="text-[#6B7280]">Results Guarantee</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[#111827] mb-2">100%</div>
                    <div className="text-[#6B7280]">Transparent Process</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[#111827] mb-2">24/7</div>
                    <div className="text-[#6B7280]">Support Access</div>
                  </div>
                </div>
              </motion.div>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Premium Journey to Success Section */}
      <section className="py-32 bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E3A8A]/90 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/20 to-transparent rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/10 to-transparent rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Premium Header */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="mb-8">
              <Badge className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/20 via-white/10 to-[#FF6B35]/20 text-[#FF6B35] border border-[#FF6B35]/30 text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm">
                <Sparkles className="h-6 w-6 animate-pulse" />
                <span className="tracking-wide">YOUR SUCCESS JOURNEY</span>
              </Badge>
            </motion.div>

            <motion.h2
              variants={fadeInUp}
              className="text-5xl sm:text-6xl lg:text-7xl font-black tracking-tight leading-[0.9] mb-10 text-white"
            >
              <span className="block mb-3">Your Journey to</span>
              <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                Predictable Success
              </span>
            </motion.h2>

            <motion.p
              variants={fadeInUp}
              className="text-2xl lg:text-3xl text-white/90 max-w-5xl mx-auto leading-relaxed font-medium"
            >
              Watch your business transform through our proven methodology, with clear milestones and measurable results at every stage.
            </motion.p>
          </motion.div>

          {/* Premium Timeline */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="relative"
          >
            {/* Enhanced Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-2 h-full bg-gradient-to-b from-[#FF6B35] via-[#FF8A65] to-[#4CAF50] rounded-full shadow-lg shadow-[#FF6B35]/30"></div>

            <div className="space-y-20">
              {[
                {
                  week: "Week 1-2",
                  title: "Strategic Foundation",
                  description: "Deep-dive analysis, competitor research, and custom strategy development",
                  icon: <Search className="h-8 w-8" />,
                  metrics: ["Strategy Blueprint", "Market Analysis", "KPI Framework"],
                  color: "from-[#FF6B35] to-[#FF8A65]"
                },
                {
                  week: "Week 3-4",
                  title: "Launch & Implementation",
                  description: "Full deployment of campaigns, website optimization, and tracking systems",
                  icon: <Rocket className="h-8 w-8" />,
                  metrics: ["Live Campaigns", "Optimized Website", "Analytics Setup"],
                  color: "from-[#1E3A8A] to-[#1E40AF]"
                },
                {
                  week: "Week 5-8",
                  title: "Initial Optimization",
                  description: "Data-driven refinements, A/B testing, and performance improvements",
                  icon: <TrendingUp className="h-8 w-8" />,
                  metrics: ["Performance Data", "A/B Test Results", "Initial ROI"],
                  color: "from-[#4CAF50] to-[#45A049]"
                },
                {
                  week: "Week 9-12",
                  title: "Momentum Acceleration",
                  description: "Scaling successful strategies and expanding reach for maximum impact",
                  icon: <Zap className="h-8 w-8" />,
                  metrics: ["Scaled Campaigns", "Increased Traffic", "Higher Conversions"],
                  color: "from-[#FF6B35] to-[#FF8A65]"
                },
                {
                  week: "Month 4+",
                  title: "Sustained Excellence",
                  description: "Continuous optimization and growth with predictable, measurable results",
                  icon: <Award className="h-8 w-8" />,
                  metrics: ["Predictable Growth", "Optimized ROI", "Market Leadership"],
                  color: "from-[#1E3A8A] to-[#1E40AF]"
                }
              ].map((milestone, index) => (
                <motion.div key={index} variants={fadeInUp} className="relative">
                  <div className={`flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                    <Card className={`w-96 border-0 bg-white/95 backdrop-blur-xl shadow-2xl hover:shadow-[#FF6B35]/20 transition-all duration-500 rounded-3xl overflow-hidden group hover:scale-105 ${index % 2 === 0 ? 'mr-12' : 'ml-12'}`}>
                      <CardContent className="p-8">
                        {/* Timeline Badge */}
                        <div className="flex items-center justify-between mb-6">
                          <Badge className="bg-[#FF6B35]/10 text-[#FF6B35] border border-[#FF6B35]/20 px-4 py-2 text-sm font-bold rounded-full">
                            {milestone.week}
                          </Badge>
                          <motion.div
                            className={`w-16 h-16 bg-gradient-to-r ${milestone.color} rounded-2xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                            whileHover={{ rotate: 5 }}
                          >
                            {milestone.icon}
                          </motion.div>
                        </div>

                        <h4 className="text-2xl font-black text-[#1E3A8A] mb-4 group-hover:text-[#FF6B35] transition-colors duration-300">
                          {milestone.title}
                        </h4>
                        <p className="text-slate-600 leading-relaxed mb-6 font-medium">
                          {milestone.description}
                        </p>

                        {/* Key Metrics */}
                        <div className="space-y-3">
                          {milestone.metrics.map((metric, idx) => (
                            <motion.div
                              key={idx}
                              className="flex items-center space-x-3"
                              initial={{ opacity: 0, x: -10 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              transition={{ delay: idx * 0.1 }}
                            >
                              <motion.div
                                className="w-6 h-6 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                                whileHover={{ rotate: 10 }}
                              >
                                <CheckCircle className="h-3 w-3 text-white" />
                              </motion.div>
                              <span className="text-[#1E3A8A] text-sm font-semibold">{metric}</span>
                            </motion.div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Enhanced Timeline Dot */}
                  <motion.div
                    className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white border-4 border-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                    whileHover={{ scale: 1.2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="w-full h-full bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] rounded-full animate-pulse"></div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Premium Results CTA */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
            className="text-center mt-20"
          >
            <Card className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 text-white relative overflow-hidden shadow-2xl rounded-3xl">
              <CardContent className="p-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Badge className="inline-flex items-center px-6 py-3 bg-[#FF6B35]/20 text-[#FF6B35] border border-[#FF6B35]/30 mb-8 text-lg font-bold rounded-full backdrop-blur-sm">
                    <Trophy className="h-6 w-6 mr-3 animate-pulse" />
                    PROVEN SUCCESS PATH
                  </Badge>
                </motion.div>

                <h3 className="text-4xl lg:text-5xl font-black mb-8 leading-tight">
                  Ready to Start Your Success Journey?
                </h3>
                <p className="text-xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed font-medium">
                  Join hundreds of businesses who've transformed their digital presence into predictable revenue engines.
                </p>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white px-12 py-6 text-xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/40 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 border-2 border-[#FF6B35]/20"
                  >
                    <Target className="h-6 w-6 mr-3" />
                    Get Your Custom Blueprint
                    <ArrowRight className="h-6 w-6 ml-3" />
                  </Button>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827]"
            >
              Ready to Start Your Blueprint?
            </motion.h2>

            <motion.p
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Don&apos;t leave your growth to chance. Let our proven blueprint transform your business into a predictable revenue machine.
            </motion.p>

            <motion.div variants={fadeInUp}>
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-12 py-6 text-xl font-bold rounded-2xl transition-all duration-300 hover:scale-105 shadow-2xl shadow-[#FF6B35]/25"
              >
                <Target className="h-6 w-6 mr-3" />
                Get Your Custom Blueprint
                <ArrowRight className="h-6 w-6 ml-3" />
              </Button>
            </motion.div>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-8 text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-[#4CAF50]" />
                <span>90-day guarantee</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-[#4CAF50]" />
                <span>Expert team</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-[#4CAF50]" />
                <span>Proven results</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
