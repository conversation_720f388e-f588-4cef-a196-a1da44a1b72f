import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Design Leeds Service Configuration for web-design-leeds
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Design Leeds",
  "serviceSlug": "web-design-leeds",
  "metaTitle": "Web Design Leeds | WebforLeads",
  "metaDescription": "Top-rated web design agency in Leeds.  Get a stunning, results-driven website.  Call 0800 123 4567 or visit webforleads.uk",
  "keywords": [
    "web design Leeds",
    "website design Leeds",
    "web developer Leeds",
    "eCommerce website design Leeds"
  ],
  "heroTitle": "Stunning Websites That Drive Results for Leeds Businesses",
  "heroSubtitle": "WebforLeads: Your Leeds Web Design Experts",
  "heroDescription": "Since 2019, WebforLeads has helped businesses in Leeds and surrounding areas like Harrogate and Bradford thrive online. We create bespoke, high-performing websites designed to attract customers and grow your business. From sleek corporate sites to engaging e-commerce platforms, we've got you covered.",
  "heroBadgeText": "Google & Facebook Marketing Partner",
  "stats": [
    {
      "number": "500%",
      "label": "Average ROI for our clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Website revenue generated for our clients",
      "icon": <Target className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client satisfaction rating",
      "icon": <Users className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Average response time to inquiries",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic Design",
      "description": "We start with a deep dive into your business goals and target audience to create a website that perfectly aligns with your vision.",
      "highlight": "User-centric design"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Performance Optimization",
      "description": "We build websites for speed and efficiency, ensuring optimal search engine rankings and a seamless user experience.",
      "highlight": "Fast loading times"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion Focused",
      "description": "Every element of your website is meticulously crafted to convert visitors into customers.",
      "highlight": "Clear calls to action"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Data-Driven Results",
      "description": "We track key performance indicators (KPIs) to measure the success of your website and make data-driven improvements.",
      "highlight": "Regular performance reports"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "SEO Optimization",
      "description": "We implement best practices to improve your search engine rankings."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Responsive Design",
      "description": "Your website will look great on any device."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "E-commerce Solutions",
      "description": "We build robust and secure online stores."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Custom Development",
      "description": "We tailor your website to your specific needs."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Analytics Integration",
      "description": "We integrate analytics tools to track your website's performance."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Content Management System (CMS)",
      "description": "Easy-to-use CMS for managing your website content."
    }
  ],
  "packages": [
    {
      "name": "Starter Package",
      "price": "£3,500",
      "period": "one-time",
      "description": "Perfect for small businesses with basic needs. Includes a 5-page website, basic SEO, and responsive design.",
      "features": [
        "Responsive Design",
        "Basic SEO",
        "5 Pages"
      ],
      "highlight": false,
      "cta": "Learn More"
    },
    {
      "name": "Professional Package",
      "price": "£7,500",
      "period": "one-time",
      "description": "Ideal for businesses looking for a more comprehensive online presence. Includes a 10-page website, advanced SEO, and e-commerce functionality.",
      "features": [
        "Responsive Design",
        "Advanced SEO",
        "10 Pages",
        "E-commerce"
      ],
      "highlight": true,
      "cta": "Get a Quote"
    },
    {
      "name": "Custom Package",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored to your specific requirements.  Let's discuss your needs and create a bespoke solution.",
      "features": [
        "Custom Development",
        "Advanced Features",
        "Dedicated Project Manager"
      ],
      "highlight": false,
      "cta": "Contact Us"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Planning",
      "description": "We'll meet to discuss your goals, target audience, and brand identity.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "We'll design and develop your website, incorporating your feedback throughout the process.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Testing & Refinement",
      "description": "We'll thoroughly test your website to ensure it's performing optimally.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Ongoing Support",
      "description": "We'll launch your website and provide ongoing support to ensure its continued success.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads completely transformed our online presence.  Our website is now a powerful lead generation machine!",
    "name": "Sarah Jones",
    "role": "Marketing Manager",
    "company": "Northern Lights Brewery (Leeds)",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Website traffic increase"
  },
  "testimonials": [
    {
      "quote": "Fantastic service!  WebforLeads exceeded our expectations.",
      "name": "Mark Smith",
      "role": "Owner",
      "company": "The Yorkshire Tea Room (Harrogate)",
      "industry": "Hospitality",
      "metric": "315%",
      "metricLabel": "Lead generation increase",
      "avatar": "MC"
    },
    {
      "quote": "Our new website is amazing.  We're already seeing a significant increase in sales.",
      "name": "Emily Roberts",
      "role": "CEO",
      "company": "Artisan Bakers (Leeds)",
      "industry": "Food & Beverage",
      "metric": "200%",
      "metricLabel": "Sales increase",
      "avatar": "ER"
    },
    {
      "quote": "WebforLeads is a true partner. They understand our business and deliver exceptional results.",
      "name": "David Patel",
      "role": "Director",
      "company": "Leeds Digital Agency",
      "industry": "Digital Marketing",
      "metric": "400%",
      "metricLabel": "Brand awareness increase",
      "avatar": "DP"
    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to build a website?",
          "a": "The timeline varies depending on the project's complexity, but typically ranges from 4-8 weeks."
        },
        {
          "q": "What's the process like?",
          "a": "We follow a structured process, from initial consultation to launch and ongoing support."
        },
        {
          "q": "Can I see examples of your work?",
          "a": "Absolutely! Check out our portfolio on our website."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you offer e-commerce solutions?",
          "a": "Yes, we build robust and secure online stores."
        },
        {
          "q": "What CMS do you use?",
          "a": "We primarily use WordPress, but can work with other platforms as needed."
        },
        {
          "q": "Do you offer SEO services?",
          "a": "Yes, we offer comprehensive SEO services to help improve your search engine rankings."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What's your pricing structure?",
          "a": "Our pricing is based on project scope and complexity. Please contact us for a custom quote."
        },
        {
          "q": "What happens after the website is launched?",
          "a": "We offer ongoing maintenance and support to ensure your website continues to perform optimally."
        },
        {
          "q": "What payment methods do you accept?",
          "a": "We accept various payment methods, including bank transfer and credit cards."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get a Free Consultation",
    "title": "Ready to transform your online presence?",
    "description": "Let's discuss your web design needs.  Contact WebforLeads today!"
  },
  "structuredData": {
    "serviceName": "Web Design",
    "description": "WebforLeads provides professional web design services in Leeds and surrounding areas.",
    "priceRange": "£3000-£15000",
    "areaServed": "Leeds, UK",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebDesignLeedsPage() {
  return <ServiceTemplate config={serviceConfig} />;
}