import { useScroll, useTransform, MotionValue, UseScrollOptions } from "framer-motion";
import { useEffect, useState, RefObject } from "react";

interface UseSafeScrollOptions {
  target?: RefObject<HTMLElement | null>;
  offset?: UseScrollOptions["offset"];
}

export function useSafeScroll(options: UseSafeScrollOptions = {}) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Always call useScroll, but conditionally pass the target
  const { scrollYProgress } = useScroll(
    isMounted && options.target?.current
      ? { target: options.target, offset: options.offset || ["start end", "end start"] }
      : {}
  );

  return {
    scrollYProgress,
    isMounted
  };
}

export function useSafeTransform(
  value: MotionValue<number>,
  inputRange: number[],
  outputRange: string[] | number[]
) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Create static array with same length as outputRange during SSR
  const staticRange = isMounted
    ? outputRange
    : Array(outputRange.length).fill(outputRange[0]);

  return useTransform(value, inputRange, staticRange);
}
