"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Code, ArrowRight, CheckCircle, TrendingUp, Users, Award, Star,
  Target, Zap, BarChart3, Globe, Rocket, Shield, Smartphone,
  Monitor, Database, Cloud, Settings, Lock, ChevronRight, Sparkles,
  Trophy, LineChart, Cpu, Server, Layers, Brain, Gauge, Eye,
  Cog, Workflow, GitBranch, TestTube, Package, Wrench
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 }
};

// Premium stats for billion-dollar feel
const stats = [
  { number: "500%", label: "Average Efficiency Increase", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "£3.2M+", label: "Revenue Generated", icon: <Target className="h-6 w-6" /> },
  { number: "98%", label: "Project Success Rate", icon: <Users className="h-6 w-6" /> },
  { number: "24/7", label: "Support & Monitoring", icon: <Rocket className="h-6 w-6" /> }
];

// Premium development features
const developmentFeatures = [
  {
    icon: <Smartphone className="h-8 w-8" />,
    title: "Native iOS & Android Development",
    description: "Expert native app development using Swift and Kotlin for optimal performance, security, and user experience on both platforms.",
    highlight: "Native Excellence"
  },
  {
    icon: <Layers className="h-8 w-8" />,
    title: "Cross-Platform Solutions",
    description: "Efficient React Native and Flutter development for apps that work seamlessly across multiple platforms with shared codebase.",
    highlight: "Cross-Platform"
  },
  {
    icon: <Brain className="h-8 w-8" />,
    title: "AI-Powered Features",
    description: "Integration of machine learning, artificial intelligence, and advanced analytics to create intelligent, data-driven mobile applications.",
    highlight: "AI-Enhanced"
  },
  {
    icon: <Shield className="h-8 w-8" />,
    title: "Enterprise Security",
    description: "Bank-level security implementation with encryption, secure authentication, and compliance with industry standards and regulations.",
    highlight: "Security-First"
  }
];

export default function AppDevelopmentClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Smartphone className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">MOBILE APP DEVELOPMENT SERVICES</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Mobile App Development That
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Transforms Ideas Into Success
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Leading Mobile App Development Company Specializing in{" "}
                    <span className="text-[#FF6B35] relative">
                      iOS & Android App Development, Cross-Platform Solutions
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Professional mobile app development services that bring your ideas to life. We create innovative iOS and Android applications with cutting-edge technology, stunning design, and powerful functionality that drives user engagement and business growth.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Smartphone className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Cross-Platform
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Code className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Expert Developers
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Shield className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Enterprise Security
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>

          {/* Premium Stats */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={scaleIn}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-[#1E3A8A] mb-2">{stat.number}</div>
                <div className="text-lg text-slate-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Development Features Section */}
      <section className="py-32 mt-16 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/5 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-[#1E3A8A]/5 to-transparent rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <div className="inline-flex items-center space-x-2 px-4 py-2 bg-[#FF6B35]/10 border border-[#FF6B35]/20 rounded-full">
                <Sparkles className="h-4 w-4 text-[#FF6B35]" />
                <span className="text-[#FF6B35] font-semibold">Premium App Development</span>
              </div>

              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Mobile Apps That
                <span className="block text-[#FF6B35]">Drive Business Growth</span>
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our expert mobile app development services combine cutting-edge technology with innovative design to create applications that engage users and deliver exceptional business results.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {developmentFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <CardContent className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white flex-shrink-0">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#1E3A8A]/10 text-[#1E3A8A] text-xs font-semibold rounded-full mb-2">
                          {feature.highlight}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                      </div>
                    </div>

                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </CardContent>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>



      {/* Premium Pricing Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Smartphone className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">APP DEVELOPMENT PACKAGES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Choose Your
                <span className="block text-[#FF6B35]">App Development Package</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From MVP to enterprise-grade applications, we have the perfect package to bring your mobile app vision to life.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16"
          >
            {[
              {
                name: "MVP Launch",
                price: "£8,500",
                description: "Perfect for startups and new app ideas",
                features: [
                  "Native iOS or Android app",
                  "Core functionality development",
                  "Basic UI/UX design",
                  "App store submission",
                  "3 months support",
                  "Basic analytics integration"
                ],
                highlight: false,
                cta: "Launch My MVP"
              },
              {
                name: "Cross-Platform Pro",
                price: "£15,500",
                description: "For businesses targeting multiple platforms",
                features: [
                  "iOS & Android apps",
                  "Advanced feature set",
                  "Premium UI/UX design",
                  "Backend API development",
                  "Push notifications",
                  "6 months support & updates"
                ],
                highlight: true,
                cta: "Go Cross-Platform"
              },
              {
                name: "Enterprise Scale",
                price: "Custom",
                description: "Comprehensive solutions for large organizations",
                features: [
                  "Everything in Cross-Platform Pro",
                  "Custom integrations",
                  "Advanced security features",
                  "Dedicated development team",
                  "White-label options",
                  "Ongoing maintenance"
                ],
                highlight: false,
                cta: "Get Custom Quote"
              }
            ].map((pkg, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
                className={`relative ${pkg.highlight ? 'lg:-mt-8' : ''}`}
              >
                <Card className={`h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group ${pkg.highlight ? 'ring-2 ring-[#FF6B35] ring-opacity-50' : ''}`}>
                  {pkg.highlight && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="text-sm font-semibold text-white bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] px-3 py-1 rounded-full shadow-md">
                        {pkg.highlight}
                      </div>
                    </div>
                  )}
                  
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold text-[#1E3A8A] mb-2">{pkg.name}</h3>
                      <div className="mb-4">
                        <span className="text-4xl font-black text-[#FF6B35]">{pkg.price}</span>
                      </div>
                      <p className="text-slate-600">{pkg.description}</p>
                    </div>

                    <ul className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-[#FF6B35] flex-shrink-0" />
                          <span className="text-slate-700">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <Button 
                      onClick={() => {
                        const appointmentSection = document.getElementById('appointment-form');
                        if (appointmentSection) {
                          appointmentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                      }}
                      className={`w-full py-3 rounded-2xl font-bold transition-all duration-300 ${
                        pkg.highlight 
                          ? 'bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF8A65] hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl' 
                          : 'bg-[#1E3A8A] hover:bg-[#1E40AF] text-white'
                      }`}
                    >
                      {pkg.cta}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Process Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Cog className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">DEVELOPMENT PROCESS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Our Proven
                <span className="block text-[#FF6B35]">App Development Process</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From concept to launch, our streamlined process ensures your app is delivered on time, on budget, and exceeds expectations.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                step: "01",
                title: "Discovery & Planning",
                description: "We analyze your requirements, target audience, and business goals to create a comprehensive development strategy.",
                icon: <Eye className="h-8 w-8" />
              },
              {
                step: "02", 
                title: "Design & Prototyping",
                description: "Our designers create wireframes, mockups, and interactive prototypes to visualize your app before development.",
                icon: <Smartphone className="h-8 w-8" />
              },
              {
                step: "03",
                title: "Development & Testing",
                description: "Our developers build your app using cutting-edge technology while conducting rigorous testing throughout.",
                icon: <Code className="h-8 w-8" />
              },
              {
                step: "04",
                title: "Launch & Support",
                description: "We handle app store submission, launch strategy, and provide ongoing support to ensure your app's success.",
                icon: <Rocket className="h-8 w-8" />
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] flex items-center justify-center text-white mx-auto mb-4">
                        {process.icon}
                      </div>
                      <div className="text-6xl font-black text-[#1E3A8A]/10 mb-4">{process.step}</div>
                    </div>
                    
                    <h3 className="text-2xl font-bold text-[#1E3A8A] mb-4 group-hover:text-[#FF6B35] transition-colors duration-300">
                      {process.title}
                    </h3>
                    
                    <p className="text-slate-600 leading-relaxed group-hover:text-[#1E3A8A] transition-colors duration-300">
                      {process.description}
                    </p>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Testimonials Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Apps That Drive
                <span className="block text-[#FF6B35]">Real Business Results</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                See how our mobile app development services have transformed businesses across industries with innovative solutions and measurable results.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {[
              {
                quote: "Our mobile app increased customer engagement by 340% and boosted revenue by £2.1M in the first year. The development process was seamless and professional.",
                name: "James Wilson",
                role: "CEO",
                company: "RetailTech Solutions",
                industry: "E-commerce",
                metric: "340%",
                metricLabel: "Engagement Boost",
                avatar: "JW"
              },
              {
                quote: "The fintech app they built handles over 50,000 daily transactions flawlessly. Security, performance, and user experience are all world-class.",
                name: "Sarah Chen",
                role: "CTO",
                company: "PayFlow",
                industry: "FinTech",
                metric: "50K+",
                metricLabel: "Daily Transactions",
                avatar: "SC"
              },
              {
                quote: "From concept to App Store, the entire process took just 12 weeks. The app now has 4.9 stars and over 100K downloads in 6 months.",
                name: "Michael Rodriguez",
                role: "Founder",
                company: "HealthTracker Pro",
                industry: "HealthTech",
                metric: "100K+",
                metricLabel: "Downloads",
                avatar: "MR"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-1 text-[#FF6B35] mb-4">
                      {[...Array(5)].map((_, s) => <Star key={s} className="h-4 w-4 fill-current" />)}
                    </div>
                    
                    <blockquote className="text-slate-700 leading-relaxed mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] flex items-center justify-center text-white font-bold text-sm">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="text-sm font-bold text-[#1E3A8A]">{testimonial.name}</div>
                          <div className="text-xs text-slate-500">{testimonial.role}</div>
                          <div className="text-xs text-[#FF6B35] font-semibold">{testimonial.company}</div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-black text-[#FF6B35]">{testimonial.metric}</div>
                        <div className="text-xs text-slate-500 font-semibold">{testimonial.metricLabel}</div>
                      </div>
                    </div>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium FAQs Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Brain className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Everything About
                <span className="block text-[#FF6B35]">App Development</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Get answers to the most common questions about our mobile app development process, technologies, and what makes our solutions stand out.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {[
              {
                category: "Development Process",
                icon: <Rocket className="h-6 w-6" />,
                questions: [
                  {
                    q: "How long does mobile app development typically take?",
                    a: "Most mobile apps are completed in 12-20 weeks, depending on complexity. Simple apps take 8-12 weeks, while complex enterprise apps may take 20-30 weeks. We provide detailed timelines during our discovery phase."
                  },
                  {
                    q: "Do you develop for both iOS and Android?",
                    a: "Yes, we develop native iOS and Android apps, as well as cross-platform solutions using React Native and Flutter. We'll recommend the best approach based on your target audience, budget, and timeline."
                  }
                ]
              },
              {
                category: "Technology & Features",
                icon: <Code className="h-6 w-6" />,
                questions: [
                  {
                    q: "What technologies and frameworks do you use?",
                    a: "We use cutting-edge technologies including Swift/Kotlin for native development, React Native/Flutter for cross-platform, Node.js/Python for backends, and cloud services like AWS and Firebase for scalability."
                  },
                  {
                    q: "Can you integrate with existing systems and APIs?",
                    a: "Absolutely. We specialize in integrating apps with existing CRM systems, payment gateways, databases, and third-party APIs. Our team ensures seamless data flow and system compatibility."
                  }
                ]
              },
              {
                category: "Pricing & Investment",
                icon: <Target className="h-6 w-6" />,
                questions: [
                  {
                    q: "What's included in your app development packages?",
                    a: "Our packages include UI/UX design, native development, backend development, API integration, testing, deployment to app stores, and post-launch support. Higher tiers add advanced features and extended support."
                  },
                  {
                    q: "Do you offer maintenance and updates after launch?",
                    a: "Yes, we provide ongoing maintenance, security updates, feature enhancements, and performance optimization. We offer flexible maintenance packages to keep your app current and competitive."
                  }
                ]
              },
              {
                category: "Support & Deployment",
                icon: <Users className="h-6 w-6" />,
                questions: [
                  {
                    q: "Do you handle App Store and Google Play submissions?",
                    a: "Yes, we manage the entire app store submission process, including preparing store listings, handling review processes, and ensuring compliance with platform guidelines for successful approval."
                  },
                  {
                    q: "What kind of support do you provide after launch?",
                    a: "We provide 60 days of complimentary support after launch, including bug fixes, performance monitoring, and user feedback implementation. Extended support packages are available for ongoing optimization."
                  }
                ]
              }
            ].map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={fadeInUp}
                className="space-y-6"
              >
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF6B35]/80 flex items-center justify-center text-white">
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">{category.category}</h3>
                </div>

                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <motion.div
                      key={faqIndex}
                      variants={fadeInUp}
                      whileHover={{ y: -2 }}
                      transition={{ type: "spring", stiffness: 300, damping: 25 }}
                    >
                      <Card className="border-0 rounded-2xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                        <CardContent className="p-8">
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 rounded-full bg-[#FF6B35]/10 flex items-center justify-center flex-shrink-0 mt-1">
                              <div className="w-2 h-2 rounded-full bg-[#FF6B35]"></div>
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                                {faq.q}
                              </h4>
                              <p className="text-slate-600 leading-relaxed">
                                {faq.a}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                        
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="appointment-form" className="py-24 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <Badge className="px-4 py-1 bg-white/10 text-white border border-white/20 rounded-full">Get Started</Badge>
              <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">Ready to Build Your Next App?</h2>
              <p className="text-slate-300 text-lg max-w-xl">Transform your idea into a powerful mobile app that drives business growth and delights users.</p>
            </div>
            <div>
              <AppointmentForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
