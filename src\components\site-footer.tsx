"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Linkedin, Twitter, Facebook, MapPin, Mail, Phone, Star, Award, ArrowRight, Heart, Sparkles, Globe, Shield } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface FooterLink {
  label: string;
  href: string;
  external?: boolean;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

const footerSections: FooterSection[] = [
  {
    title: "Our Services",
    links: [
      { label: "Bespoke Web Design & Development", href: "/services/web-design" },
      { label: "Search Engine Optimisation (SEO)", href: "/services/seo" },
      { label: "Google Ads (PPC) Management", href: "/services/google-ads" },
      { label: "Social Media Marketing", href: "/services/social-media" },
      { label: "Mobile App Development", href: "/services/app-development" },
      { label: "Custom Software Development", href: "/services/software-development" },
    ]
  },
  {
    title: "UK Service Areas",
    links: [
      { label: "Web Design & SEO in London", href: "/locations/london" },
      { label: "Web Design & SEO in Manchester", href: "/locations/manchester" },
      { label: "Web Design & SEO in Birmingham", href: "/locations/birmingham" },
      { label: "Web Design & SEO in Leeds", href: "/locations/leeds" },
      { label: "Web Design & SEO in Bristol", href: "/locations/bristol" },
      { label: "Web Design & SEO in Liverpool", href: "/locations/liverpool" },
      { label: "View All 14+ Locations →", href: "/locations" },
    ]
  },
  {
    title: "Our Company",
    links: [
      { label: "About WebforLeads", href: "/about" },
      { label: "Our Blueprint Process", href: "/blueprint" },
      { label: "Case Studies & Results", href: "/case-studies" },
      { label: "Client Testimonials", href: "/testimonials" },
      { label: "Digital Marketing Blog", href: "/blog" },
      { label: "Contact Us", href: "/contact" },
    ]
  },
  {
    title: "Legal & Support",
    links: [
      { label: "Privacy Policy", href: "/privacy" },
      { label: "Terms of Service", href: "/terms" },
      { label: "Cookie Policy", href: "/cookies" },
      { label: "GDPR Compliance", href: "/gdpr" },
      { label: "Support Centre", href: "/support" },
      { label: "Site Map", href: "/sitemap" },
    ]
  }
];

const socialLinks = [
  { icon: <Linkedin className="h-5 w-5" />, href: "https://linkedin.com/company/webforleads", label: "LinkedIn" },
  { icon: <Twitter className="h-5 w-5" />, href: "https://twitter.com/webforleads", label: "Twitter" },
  { icon: <Facebook className="h-5 w-5" />, href: "https://facebook.com/webforleads", label: "Facebook" },
];

export default function SiteFooter() {
  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  return (
    <footer className="relative bg-background border-t border-[#1E3A8A]/10 overflow-hidden">
      {/* Premium Background System */}
      <div className="absolute inset-0">
        {/* Sophisticated gradient overlay */}
        <div className="absolute inset-0 gradient-mesh opacity-30" />

        {/* Premium geometric patterns */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-radial from-[#1E3A8A]/6 to-transparent rounded-full blur-3xl animate-bounce"></div>
          <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-radial from-[#FF6B35]/6 to-transparent rounded-full blur-3xl animate-bounce" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-radial from-[#4CAF50]/4 to-transparent rounded-full blur-3xl animate-pulse"></div>
        </div>

        {/* Premium grid overlay */}
        <div
          className="absolute inset-0 opacity-[0.015]"
          style={{
            backgroundImage: `
              linear-gradient(to right, #1E3A8A 1px, transparent 1px),
              linear-gradient(to bottom, #1E3A8A 1px, transparent 1px)
            `,
            backgroundSize: '80px 80px'
          }}
        />
      </div>

      <div className="relative">
        {/* Premium CTA Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="py-20 lg:py-24"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Card className="bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 rounded-3xl overflow-hidden hover:border-[#FF6B35]/30 transition-all duration-500 relative">
              {/* Background accent */}
              <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-radial from-[#FF6B35]/15 to-transparent rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-[#4CAF50]/10 to-transparent rounded-full blur-3xl animate-bounce"></div>

              <CardContent className="relative text-center p-16">
                <motion.div variants={fadeInUp} className="mb-8">
                  <Badge
                    className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm"
                  >
                    <Sparkles className="h-6 w-6 animate-pulse text-[#FF6B35]" />
                    <span className="tracking-wide">READY TO TRANSFORM YOUR BUSINESS?</span>
                  </Badge>
                </motion.div>

                <motion.h2
                  variants={fadeInUp}
                  className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-10"
                >
                  <span className="block text-[#1E3A8A] mb-3">Let's Build Your</span>
                  <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                    Digital Empire
                  </span>
                </motion.h2>

                <motion.p
                  variants={fadeInUp}
                  className="text-2xl lg:text-3xl text-slate-600 max-w-5xl mx-auto mb-12 leading-relaxed font-medium"
                >
                  Join hundreds of UK businesses that trust us to drive their digital growth and market dominance.
                </motion.p>

                <motion.div variants={fadeInUp}>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      asChild
                      size="lg"
                      className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white px-16 py-8 text-2xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/40 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 border-2 border-[#FF6B35]/20"
                    >
                      <Link href="/contact">
                        Start Your Growth Journey
                        <ArrowRight className="h-8 w-8 ml-4" />
                      </Link>
                    </Button>
                  </motion.div>
                </motion.div>
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Premium Footer Links */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="py-16 border-t border-[#1E3A8A]/10"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12">
              
              {/* Premium Brand Section */}
              <motion.div variants={fadeInUp} className="lg:col-span-1">
                <Link href="/" className="flex items-center space-x-3 mb-6 group">
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300"
                    whileHover={{ rotate: 5 }}
                  >
                    <span className="text-white font-bold text-xl">W</span>
                  </motion.div>
                  <span className="text-2xl font-bold text-[#1E3A8A]">
                    WebforLeads
                  </span>
                </Link>

                <p className="text-slate-600 mb-6 leading-relaxed font-medium">
                  Transforming UK businesses through data-driven digital marketing and premium web experiences.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3 text-slate-600">
                    <div className="w-8 h-8 bg-[#FF6B35]/10 rounded-lg flex items-center justify-center">
                      <MapPin className="h-4 w-4 text-[#FF6B35]" />
                    </div>
                    <span>London, UK & Nationwide</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-600">
                    <div className="w-8 h-8 bg-orange-500/10 rounded-lg flex items-center justify-center">
                      <Mail className="h-4 w-4 text-orange-500" />
                    </div>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-600">
                    <div className="w-8 h-8 bg-orange-500/10 rounded-lg flex items-center justify-center">
                      <Phone className="h-4 w-4 text-orange-500" />
                    </div>
                    <span>020 8123 4567 (London)</span>
                  </div>
                </div>

                {/* Premium Social Links */}
                <div className="flex space-x-3 mt-6">
                  {socialLinks.map((social, index) => (
                    <motion.div key={index} whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                      <Link
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-10 h-10 bg-white/60 backdrop-blur-sm border border-blue-900/10 rounded-xl flex items-center justify-center text-gray-600 hover:bg-orange-500 hover:text-white hover:border-orange-500 transition-all duration-300 shadow-lg hover:shadow-orange-500/25 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                      >
                        {social.icon}
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Premium Footer Sections */}
              {footerSections.map((section, index) => (
                <motion.div key={section.title} variants={fadeInUp}>
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">
                    {section.title}
                  </h3>
                  <ul className="space-y-3">
                    {section.links.map((link) => (
                      <li key={link.label}>
                        <Link
                          href={link.href}
                          className="text-gray-600 hover:text-orange-500 transition-colors duration-300 flex items-center group focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                          {...(link.external && { target: "_blank", rel: "noopener noreferrer" })}
                        >
                          <span>{link.label}</span>
                          <ArrowRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </Link>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Premium Bottom Bar */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="py-8 border-t border-blue-900/10"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
              
              {/* Copyright */}
              <div className="flex items-center space-x-4 text-gray-600">
                <span>© 2024 WebforLeads. All rights reserved.</span>
                <Separator orientation="vertical" className="h-4" />
                <div className="flex items-center space-x-1">
                  <span>Made with</span>
                  <Heart className="h-4 w-4 text-orange-500 fill-current" />
                  <span>in the UK</span>
                </div>
              </div>

              {/* Premium Trust Badges */}
              <div className="flex items-center space-x-4">
                <Badge variant="secondary" className="bg-green-500/10 text-green-500 border-green-500/20">
                  <Shield className="h-3 w-3 mr-1" />
                  SSL Secured
                </Badge>
                <Badge variant="secondary" className="bg-blue-900/10 text-blue-900 border-blue-900/20">
                  <Globe className="h-3 w-3 mr-1" />
                  UK Based
                </Badge>
              </div>

              {/* Premium Badges */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-4 py-2 bg-[#F9FAFB] border border-[#1E3A8A]/10 rounded-xl">
                  <Award className="h-4 w-4 text-[#4CAF50]" />
                  <span className="text-sm font-medium text-[#111827]">Google Partner</span>
                </div>
                <div className="flex items-center space-x-1 px-4 py-2 bg-[#F9FAFB] border border-[#1E3A8A]/10 rounded-xl">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-3 w-3 fill-[#FF6B35] text-[#FF6B35]" />
                  ))}
                  <span className="text-sm font-medium text-[#111827] ml-2">5.0</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
