import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Route registry for tracking all generated pages
interface GeneratedRoute {
  slug: string;
  title: string;
  templateId: string;
  templateName: string;
  keyword: string;
  location: string;
  filePath: string;
  createdAt: string;
  jobId: string;
  status: 'active' | 'inactive';
}

// Get the route registry storage directory
function getRouteRegistryDir(): string {
  const storageDir = path.join(process.cwd(), 'route-registry');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Save route to registry - MOVED TO @/lib/route-registry.ts
// This function is kept here for backward compatibility but should not be exported
function registerRoute(route: GeneratedRoute): void {
  const storageDir = getRouteRegistryDir();
  const filename = `route-${route.slug}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    console.log('📝 Registering route:', route.slug);
    const payload = JSON.stringify(route, null, 2);
    fs.writeFileSync(filepath, payload);
    console.log('✅ Route registered successfully');
  } catch (error) {
    console.error('❌ Failed to register route:', error);
    throw error;
  }
}

// Get all registered routes
function getAllRoutes(): GeneratedRoute[] {
  const storageDir = getRouteRegistryDir();
  
  try {
    const files = fs.readdirSync(storageDir);
    const routes: GeneratedRoute[] = [];

    for (const file of files) {
      if (file.startsWith('route-') && file.endsWith('.json')) {
        const filepath = path.join(storageDir, file);
        const content = fs.readFileSync(filepath, 'utf-8');
        const route = JSON.parse(content) as GeneratedRoute;
        routes.push(route);
      }
    }

    // Sort by created date (newest first)
    return routes.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  } catch (error) {
    console.error('Error reading routes:', error);
    return [];
  }
}

// Get route by slug
function getRouteBySlug(slug: string): GeneratedRoute | null {
  const storageDir = getRouteRegistryDir();
  
  try {
    const filename = `route-${slug}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf-8');
      return JSON.parse(content) as GeneratedRoute;
    }
    
    return null;
  } catch (error) {
    console.error('Error reading route:', error);
    return null;
  }
}

// Delete route from registry - MOVED TO @/lib/route-registry.ts
// This function is kept here for backward compatibility but should not be exported
function unregisterRoute(slug: string): boolean {
  const storageDir = getRouteRegistryDir();
  
  try {
    const filename = `route-${slug}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath);
      console.log('🗑️ Route unregistered:', slug);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error unregistering route:', error);
    return false;
  }
}

// Update route status - MOVED TO @/lib/route-registry.ts
// This function is kept here for backward compatibility but should not be exported
function updateRouteStatus(slug: string, status: 'active' | 'inactive'): boolean {
  const route = getRouteBySlug(slug);
  if (!route) return false;

  route.status = status;
  registerRoute(route);
  return true;
}

// Get routes by template
function getRoutesByTemplate(templateId: string): GeneratedRoute[] {
  const allRoutes = getAllRoutes();
  return allRoutes.filter(route => route.templateId === templateId);
}

// Get routes by job
function getRoutesByJob(jobId: string): GeneratedRoute[] {
  const allRoutes = getAllRoutes();
  return allRoutes.filter(route => route.jobId === jobId);
}

// Validate route exists on filesystem
function validateRouteFile(route: GeneratedRoute): boolean {
  try {
    return fs.existsSync(route.filePath);
  } catch {
    return false;
  }
}

// Clean up orphaned routes (routes without files)
function cleanupOrphanedRoutes(): { cleaned: number; errors: string[] } {
  const allRoutes = getAllRoutes();
  let cleaned = 0;
  const errors: string[] = [];

  for (const route of allRoutes) {
    if (!validateRouteFile(route)) {
      try {
        unregisterRoute(route.slug);
        cleaned++;
        console.log('🧹 Cleaned orphaned route:', route.slug);
      } catch (error) {
        const errorMsg = `Failed to clean route ${route.slug}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error('❌', errorMsg);
      }
    }
  }

  return { cleaned, errors };
}

// API Handlers
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');
  const slug = searchParams.get('slug');
  const templateId = searchParams.get('templateId');
  const jobId = searchParams.get('jobId');

  try {
    switch (action) {
      case 'get-by-slug':
        if (!slug) {
          return NextResponse.json(
            { success: false, error: 'Slug is required' },
            { status: 400 }
          );
        }
        const route = getRouteBySlug(slug);
        return NextResponse.json({
          success: true,
          route,
          exists: route !== null
        });

      case 'get-by-template':
        if (!templateId) {
          return NextResponse.json(
            { success: false, error: 'Template ID is required' },
            { status: 400 }
          );
        }
        const templateRoutes = getRoutesByTemplate(templateId);
        return NextResponse.json({
          success: true,
          routes: templateRoutes,
          count: templateRoutes.length
        });

      case 'get-by-job':
        if (!jobId) {
          return NextResponse.json(
            { success: false, error: 'Job ID is required' },
            { status: 400 }
          );
        }
        const jobRoutes = getRoutesByJob(jobId);
        return NextResponse.json({
          success: true,
          routes: jobRoutes,
          count: jobRoutes.length
        });

      case 'cleanup':
        const cleanupResult = cleanupOrphanedRoutes();
        return NextResponse.json({
          success: true,
          message: `Cleaned up ${cleanupResult.cleaned} orphaned routes`,
          cleaned: cleanupResult.cleaned,
          errors: cleanupResult.errors
        });

      case 'validate':
        if (!slug) {
          return NextResponse.json(
            { success: false, error: 'Slug is required' },
            { status: 400 }
          );
        }
        const routeToValidate = getRouteBySlug(slug);
        if (!routeToValidate) {
          return NextResponse.json({
            success: true,
            exists: false,
            valid: false
          });
        }
        const isValid = validateRouteFile(routeToValidate);
        return NextResponse.json({
          success: true,
          exists: true,
          valid: isValid,
          route: routeToValidate
        });

      default:
        // Get all routes
        const allRoutes = getAllRoutes();
        const stats = {
          total: allRoutes.length,
          active: allRoutes.filter(r => r.status === 'active').length,
          inactive: allRoutes.filter(r => r.status === 'inactive').length,
          byTemplate: {} as { [key: string]: number },
          recentRoutes: allRoutes.slice(0, 10)
        };

        // Calculate routes by template
        allRoutes.forEach(route => {
          stats.byTemplate[route.templateName] = (stats.byTemplate[route.templateName] || 0) + 1;
        });

        return NextResponse.json({
          success: true,
          routes: allRoutes,
          stats
        });
    }
  } catch (error) {
    console.error('Error in route registry API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process route registry request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Register a new route
export async function POST(request: NextRequest) {
  try {
    const route: GeneratedRoute = await request.json();
    
    // Validate required fields
    if (!route.slug || !route.title || !route.templateId || !route.filePath) {
      return NextResponse.json(
        { success: false, error: 'Missing required route fields' },
        { status: 400 }
      );
    }

    registerRoute(route);

    return NextResponse.json({
      success: true,
      message: 'Route registered successfully',
      route
    });

  } catch (error) {
    console.error('Error registering route:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to register route',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Update route status
export async function PATCH(request: NextRequest) {
  try {
    const { slug, status } = await request.json();
    
    if (!slug || !status) {
      return NextResponse.json(
        { success: false, error: 'Slug and status are required' },
        { status: 400 }
      );
    }

    const updated = updateRouteStatus(slug, status);
    
    if (!updated) {
      return NextResponse.json(
        { success: false, error: 'Route not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Route status updated successfully'
    });

  } catch (error) {
    console.error('Error updating route status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update route status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Delete route
export async function DELETE(request: NextRequest) {
  try {
    const { slug } = await request.json();
    
    if (!slug) {
      return NextResponse.json(
        { success: false, error: 'Slug is required' },
        { status: 400 }
      );
    }

    const deleted = unregisterRoute(slug);
    
    if (!deleted) {
      return NextResponse.json(
        { success: false, error: 'Route not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Route unregistered successfully'
    });

  } catch (error) {
    console.error('Error unregistering route:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to unregister route',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
