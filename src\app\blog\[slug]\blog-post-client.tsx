"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Calendar, Clock, ArrowLeft, Share2, User, ChevronRight, Tag, BookOpen
} from "lucide-react";

interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    canonicalUrl: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterTitle: string;
    twitterDescription: string;
    twitterImage: string;
    author: string;
    category: string;
    tags: string[];
    readingTime: number;
    wordCount: number;
  };
}

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

interface BlogPostClientProps {
  blogPost: BlogPost;
}

export default function BlogPostClient({ blogPost }: BlogPostClientProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const sharePost = () => {
    if (navigator.share) {
      navigator.share({
        title: blogPost.title,
        text: blogPost.excerpt,
        url: window.location.href,
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // ADVANCED BULLETPROOF LINK FIXER - Handles ALL possible broken link patterns
  const advancedLinkFixer = (content: string): string => {
    console.log('🚀 ADVANCED LINK FIXER STARTING...');
    console.log('📝 Content length:', content.length);

    let fixedContent = content;
    let fixCount = 0;

    // PATTERN 1: Fix "word(https://url)" patterns
    const pattern1Regex = /([a-zA-Z][a-zA-Z\s]*?)\s*\(https:\/\/[^)]+\)/g;
    fixedContent = fixedContent.replace(pattern1Regex, (match, text) => {
      const urlMatch = match.match(/\((https:\/\/[^)]+)\)/);
      if (urlMatch) {
        const url = urlMatch[1];
        let linkText = text.trim();

        // Smart link text generation
        if (url.includes('mckinsey.com')) linkText = 'McKinsey study';
        else if (url.includes('gartner.com')) linkText = 'Gartner report';
        else if (url.includes('hubspot.com')) linkText = 'HubSpot research';
        else if (url.includes('salesforce.com')) linkText = 'Salesforce report';
        else if (url.includes('google.com')) linkText = 'Google study';
        else if (url.includes('nielsen.com')) linkText = 'Nielsen report';
        else if (url.includes('forrester.com')) linkText = 'Forrester research';

        fixCount++;
        console.log(`✅ Pattern 1 fix #${fixCount}: "${match}" -> "[${linkText}](${url})"`);
        return `[${linkText}](${url})`;
      }
      return match;
    });

    // PATTERN 2: Fix "word(/internal/url)" patterns
    const pattern2Regex = /([a-zA-Z][a-zA-Z\s]*?)\s*\(\/[^)]+\)/g;
    fixedContent = fixedContent.replace(pattern2Regex, (match, text) => {
      const urlMatch = match.match(/\((\/[^)]+)\)/);
      if (urlMatch) {
        let url = urlMatch[1];
        let linkText = text.trim();

        // Smart internal link text
        if (url.includes('/services/web-design')) linkText = 'web design services';
        else if (url.includes('/services/digital-marketing')) linkText = 'digital marketing services';
        else if (url.includes('/services/seo')) linkText = 'SEO services';
        else if (url.includes('/portfolio')) linkText = 'our portfolio';
        else if (url.includes('/about')) linkText = 'about us';
        else if (url.includes('/contact')) linkText = 'contact us';
        else if (url.includes('/blog')) linkText = 'our blog';

        // Convert relative URLs to localhost:3000 if needed
        if (url.startsWith('/')) {
          url = `http://localhost:3000${url}`;
        }

        fixCount++;
        console.log(`✅ Pattern 2 fix #${fixCount}: "${match}" -> "[${linkText}](${url})"`);
        return `[${linkText}](${url})`;
      }
      return match;
    });

    // PATTERN 3: Fix "OurSEO services(/url)" -> "Our [SEO services](/url)"
    const pattern3Regex = /Our([A-Z][a-zA-Z]*)\s+([a-z\s]+)\s*\(\/[^)]+\)/g;
    fixedContent = fixedContent.replace(pattern3Regex, (match, firstWord, restWords) => {
      const urlMatch = match.match(/\((\/[^)]+)\)/);
      if (urlMatch) {
        const linkText = `${firstWord} ${restWords.trim()}`;
        fixCount++;
        console.log(`✅ Pattern 3 fix #${fixCount}: "${match}" -> "Our [${linkText}](${urlMatch[1]})"`);
        return `Our [${linkText}](${urlMatch[1]})`;
      }
      return match;
    });

    // PATTERN 4: Fix "aMcKinsey report(https://url)" -> "[McKinsey report](https://url)"
    const pattern4Regex = /a([A-Z][a-z]+)\s+([a-z]+)\s*\(https:\/\/[^)]+\)/g;
    fixedContent = fixedContent.replace(pattern4Regex, (match, company, type) => {
      const urlMatch = match.match(/\((https:\/\/[^)]+)\)/);
      if (urlMatch) {
        const linkText = `${company} ${type}`;
        fixCount++;
        console.log(`✅ Pattern 4 fix #${fixCount}: "${match}" -> "[${linkText}](${urlMatch[1]})"`);
        return `[${linkText}](${urlMatch[1]})`;
      }
      return match;
    });

    // PATTERN 5: Fix standalone "(https://url)" anywhere in text
    const pattern5Regex = /\s*\(https:\/\/[^)]+\)(?!\])/g;
    fixedContent = fixedContent.replace(pattern5Regex, (match) => {
      const urlMatch = match.match(/\((https:\/\/[^)]+)\)/);
      if (urlMatch) {
        const url = urlMatch[1];
        let linkText = 'source';

        if (url.includes('mckinsey.com')) linkText = 'McKinsey study';
        else if (url.includes('gartner.com')) linkText = 'Gartner report';
        else if (url.includes('hubspot.com')) linkText = 'HubSpot research';
        else if (url.includes('salesforce.com')) linkText = 'Salesforce report';
        else if (url.includes('google.com')) linkText = 'Google study';

        fixCount++;
        console.log(`✅ Pattern 5 fix #${fixCount}: "${match}" -> " [${linkText}](${url})"`);
        return ` [${linkText}](${url})`;
      }
      return match;
    });

    // PATTERN 6: Fix standalone "(/internal/url)" anywhere in text
    const pattern6Regex = /\s*\(\/[^)]+\)(?!\])/g;
    fixedContent = fixedContent.replace(pattern6Regex, (match) => {
      const urlMatch = match.match(/\((\/[^)]+)\)/);
      if (urlMatch) {
        const url = urlMatch[1];
        let linkText = 'Learn more';

        if (url.includes('/services/web-design')) linkText = 'web design services';
        else if (url.includes('/services/digital-marketing')) linkText = 'digital marketing services';
        else if (url.includes('/services/seo')) linkText = 'SEO services';
        else if (url.includes('/portfolio')) linkText = 'our portfolio';
        else if (url.includes('/about')) linkText = 'about us';
        else if (url.includes('/contact')) linkText = 'contact us';

        fixCount++;
        console.log(`✅ Pattern 6 fix #${fixCount}: "${match}" -> " [${linkText}](${url})"`);
        return ` [${linkText}](${url})`;
      }
      return match;
    });

    // PATTERN 7: Convert any remaining markdown links to HTML (fallback)
    const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    fixedContent = fixedContent.replace(markdownLinkRegex, (_match, text, url) => {
      const isInternal = url.startsWith('/') || url.startsWith('#') || url.includes('webforleads.uk');
      if (isInternal) {
        fixCount++;
        console.log(`✅ Pattern 7 fix #${fixCount}: Converting markdown to HTML internal link: [${text}](${url})`);
        return `<a href="${url}" class="internal-link">${text}</a>`;
      } else {
        fixCount++;
        console.log(`✅ Pattern 7 fix #${fixCount}: Converting markdown to HTML external link: [${text}](${url})`);
        return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="external-link">${text}</a>`;
      }
    });

    // FINAL CLEANUP: Remove any remaining broken patterns
    fixedContent = fixedContent.replace(/\(\s*https:\/\/[^)]*\s*\)/g, '');
    fixedContent = fixedContent.replace(/\(\s*\/[^)]*\s*\)/g, '');

    console.log(`🎉 ADVANCED LINK FIXER COMPLETE! Fixed ${fixCount} links total.`);
    console.log('📊 Final content length:', fixedContent.length);

    return fixedContent;
  };

  // Function to parse and style markdown content from Gemini
  const parseMarkdownContent = (content: string) => {
    // First, clean up the content and normalize line breaks
    let cleanContent = content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .trim();

    // Remove metadata section that appears at the end of Gemini content
    // This removes everything from TITLE: onwards
    cleanContent = cleanContent.replace(/\n*TITLE:[\s\S]*$/i, '');

    // Also remove any other common metadata patterns
    cleanContent = cleanContent.replace(/\n*(META_TITLE|EXCERPT|KEYWORDS|FOCUS_KEYWORD|OG_TITLE|TWITTER_TITLE|AUTHOR|CATEGORY|TAGS):[\s\S]*$/i, '');

    // Remove any trailing metadata lines that start with uppercase words followed by colon
    cleanContent = cleanContent.replace(/\n*[A-Z_]+:.*$/gm, '');

    // Remove any leftover internal linking suggestions
    cleanContent = cleanContent.replace(/Internal Link Opportunit(y|ies):.*$/gmi, '');
    cleanContent = cleanContent.replace(/External Link.*$/gmi, '');
    cleanContent = cleanContent.replace(/\n*Internal Link Opportunit(y|ies):[\s\S]*$/i, '');

    // Clean up any extra whitespace
    cleanContent = cleanContent.trim();

    // CRITICAL: Fix broken link formatting BEFORE any other processing
    console.log('🔍 Original content sample:', cleanContent.substring(0, 500));
    cleanContent = advancedLinkFixer(cleanContent);
    console.log('🔧 After link fixing sample:', cleanContent.substring(0, 500));

    // Convert markdown to HTML with proper styling
    let htmlContent = cleanContent
      // Headers (with proper spacing) - order matters, start with most specific
      .replace(/^##### (.*$)/gm, '\n\n<h5>$1</h5>\n\n')
      .replace(/^#### (.*$)/gm, '\n\n<h4>$1</h4>\n\n')
      .replace(/^### (.*$)/gm, '\n\n<h3>$1</h3>\n\n')
      .replace(/^## (.*$)/gm, '\n\n<h2>$1</h2>\n\n')
      .replace(/^# (.*$)/gm, '\n\n<h1>$1</h1>\n\n')

      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

      // Italic text (but not if it's already in bold)
      .replace(/(?<!\*)\*([^*]+?)\*(?!\*)/g, '<em>$1</em>')

      // Code blocks (inline)
      .replace(/`([^`]+)`/g, '<code>$1</code>')

      // HTML links are already properly formatted by Gemini, so we don't need to process them
      // Just ensure any remaining markdown links are converted (fallback)
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, (_match, text, url) => {
        console.log(`🔗 Processing fallback markdown link: [${text}](${url})`);
        // Check if it's an internal link (relative or same domain)
        const isInternal = url.startsWith('/') || url.startsWith('#') || url.includes('webforleads.uk');
        if (isInternal) {
          console.log(`🏠 Internal link: ${text} -> ${url}`);
          return `<a href="${url}" class="internal-link">${text}</a>`;
        } else {
          console.log(`🌐 External link: ${text} -> ${url}`);
          return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="external-link">${text}</a>`;
        }
      })

      // Horizontal rules
      .replace(/^---$/gm, '<hr>')

      // Lists - handle bullet points with better structure
      .replace(/^\* (.*$)/gm, '<li>$1</li>');

    // Process lists properly by grouping consecutive list items
    htmlContent = htmlContent.replace(/(<li>.*?<\/li>)(\s*<li>.*?<\/li>)*/g, (match) => {
      return '<ul>' + match + '</ul>';
    });

    // Split into paragraphs and process
    const paragraphs = htmlContent.split(/\n\s*\n/);
    const processedParagraphs = paragraphs.map(paragraph => {
      paragraph = paragraph.trim();
      if (!paragraph) return '';

      // Don't wrap if it's already a heading, list, or other HTML element
      if (paragraph.startsWith('<h') ||
          paragraph.startsWith('<ul') ||
          paragraph.startsWith('<li') ||
          paragraph.startsWith('<hr') ||
          paragraph.startsWith('<div') ||
          paragraph.includes('</h')) {
        return paragraph;
      }

      // Wrap in paragraph tags
      return `<p>${paragraph}</p>`;
    });

    return processedParagraphs.filter(p => p).join('\n\n');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Navigation spacing */}
      <div className="h-20 lg:h-24"></div>

      {/* Hero Section with Featured Image */}
      <section className="relative overflow-hidden">
        {/* Featured Image Background */}
        {blogPost.featuredImageUrl ? (
          <div className="absolute inset-0 z-0">
            <div className="relative h-full">
              <img
                src={blogPost.featuredImageUrl}
                alt={blogPost.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-b from-[#1E3A8A]/90 via-[#1E3A8A]/70 to-[#1E3A8A]/90"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-[#FF6B35]/20 via-transparent to-[#FF6B35]/20"></div>
            </div>
          </div>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E3A8A]/90">
            <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/20 to-transparent rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/10 to-transparent rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          </div>
        )}

        {/* Breadcrumb */}
        <div className="relative z-10 pt-8 pb-4">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-white/80 hover:text-white transition-colors">
                Home
              </Link>
              <ChevronRight className="h-4 w-4 text-white/60" />
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">
                Blog
              </Link>
              <ChevronRight className="h-4 w-4 text-white/60" />
              <span className="text-[#FF6B35] font-medium">{blogPost.seo.category}</span>
            </nav>
          </div>
        </div>

        {/* Blog Post Header Content */}
        <div className="relative z-10 py-20 lg:py-32">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="space-y-10"
            >
              <motion.div variants={fadeInUp} className="space-y-8">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Badge className="bg-[#FF6B35] text-white border-0 px-6 py-3 text-base font-bold rounded-full shadow-lg">
                      {blogPost.seo.category}
                    </Badge>
                    {blogPost.seo.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <Button
                    onClick={sharePost}
                    variant="outline"
                    size="sm"
                    className="border-[#FF6B35] bg-[#FF6B35]/10 text-[#FF6B35] hover:bg-[#FF6B35] hover:text-white backdrop-blur-sm font-semibold transition-all duration-300"
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                </div>

                <h1 className="text-4xl sm:text-5xl lg:text-7xl font-black tracking-tight leading-[0.9] text-white drop-shadow-lg">
                  {blogPost.title}
                </h1>

                <p className="text-xl lg:text-2xl text-white drop-shadow-md leading-relaxed max-w-4xl">
                  {blogPost.excerpt}
                </p>

                <div className="flex flex-wrap items-center gap-6 text-white/80">
                  <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(blogPost.publishedAt)}</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <Clock className="h-4 w-4" />
                    <span>{blogPost.seo.readingTime} min read</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <User className="h-4 w-4" />
                    <span>{blogPost.seo.author}</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <BookOpen className="h-4 w-4" />
                    <span>{blogPost.seo.wordCount} words</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Blog Post Content */}
      <section className="py-20 bg-white relative">
        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-[#1E3A8A]/5 to-transparent"></div>
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-[#FF6B35]/10 to-transparent rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-br from-[#1E3A8A]/10 to-transparent rounded-full blur-2xl"></div>

        <div className="max-w-[128rem] mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="bg-white rounded-3xl shadow-2xl shadow-gray-900/10 overflow-hidden"
          >
            {/* Main Content */}
            <div className="p-8 lg:p-16">
              <div
                className="blog-content-styled prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: parseMarkdownContent(blogPost.content) }}
              />
            </div>


          </motion.div>
        </div>
      </section>

      {/* Back to Blog */}
      <section className="py-16 bg-slate-50/50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Link href="/blog">
              <Button className="bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white px-8 py-4 text-lg font-bold rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to All Posts
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      <style jsx global>{`
        .blog-content-styled {
          font-size: 19px;
          line-height: 1.9;
          color: #374151;
          max-width: none !important;
        }

        .blog-content-styled h1,
        .blog-content-styled h2,
        .blog-content-styled h3,
        .blog-content-styled h4,
        .blog-content-styled h5,
        .blog-content-styled h6 {
          font-weight: 800;
          margin-top: 3.5rem;
          margin-bottom: 2rem;
          position: relative;
          padding-left: 2rem;
          scroll-margin-top: 100px;
        }

        .blog-content-styled h1::before,
        .blog-content-styled h2::before,
        .blog-content-styled h3::before,
        .blog-content-styled h4::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 8px;
          height: 80%;
          background: linear-gradient(135deg, #FF6B35, #FF8A65);
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .blog-content-styled h1 {
          font-size: 2.75rem;
          line-height: 1.2;
          color: #FF6B35;
          margin-top: 2rem;
          margin-bottom: 2.5rem;
          text-align: center;
          padding: 2rem;
          background: linear-gradient(135deg, #FF6B35/8, #FF8A65/5);
          border-radius: 20px;
          border: 2px solid #FF6B35/25;
          box-shadow: 0 8px 30px rgba(255, 107, 53, 0.15);
          position: relative;
        }

        .blog-content-styled h1::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 6px;
          background: linear-gradient(90deg, #FF6B35, #FF8A65, #FF6B35);
          border-radius: 20px 20px 0 0;
        }

        .blog-content-styled h2 {
          font-size: 2.25rem;
          line-height: 1.3;
          color: #FF6B35;
          padding: 1.5rem 2rem;
          background: linear-gradient(135deg, #FF6B35/10, #FF8A65/5);
          border-radius: 16px;
          border: 2px solid #FF6B35/30;
          box-shadow: 0 4px 20px rgba(255, 107, 53, 0.15);
          position: relative;
          overflow: hidden;
        }

        .blog-content-styled h2::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #FF6B35, #FF8A65);
        }

        .blog-content-styled h3 {
          font-size: 1.75rem;
          line-height: 1.4;
          color: #FF6B35;
          margin-top: 3rem;
        }

        .blog-content-styled h4 {
          font-size: 1.5rem;
          line-height: 1.4;
          color: #1E3A8A;
          margin-top: 2.5rem;
        }

        .blog-content-styled p {
          margin-bottom: 2rem;
          line-height: 1.9;
          color: #374151;
          text-align: justify;
          font-size: 19px;
        }

        .blog-content-styled p:first-of-type {
          font-size: 1.35rem;
          font-weight: 600;
          color: #1E3A8A;
          padding: 2rem;
          background: linear-gradient(135deg, #FF6B35/8, #FF8A65/5);
          border-radius: 16px;
          border: 2px solid #FF6B35/25;
          margin-bottom: 3rem;
          position: relative;
          box-shadow: 0 4px 20px rgba(255, 107, 53, 0.12);
        }

        .blog-content-styled p:first-of-type::before {
          content: '🎯 Key Insight';
          position: absolute;
          top: -12px;
          left: 20px;
          font-size: 14px;
          font-weight: 700;
          background: linear-gradient(135deg, #FF6B35, #FF8A65);
          color: white;
          padding: 6px 16px;
          border-radius: 20px;
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .blog-content-styled ul,
        .blog-content-styled ol {
          margin-bottom: 2.5rem;
          padding: 2rem;
          background: linear-gradient(135deg, #FF6B35/8, #FF8A65/5);
          border-radius: 16px;
          border: 2px solid #FF6B35/25;
          box-shadow: 0 4px 20px rgba(255, 107, 53, 0.12);
          position: relative;
        }

        .blog-content-styled ul::before {
          content: '🔥 Key Points';
          position: absolute;
          top: -12px;
          left: 20px;
          background: linear-gradient(135deg, #FF6B35, #FF8A65);
          color: white;
          padding: 6px 16px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 700;
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .blog-content-styled li {
          margin-bottom: 1rem;
          line-height: 1.8;
          position: relative;
          padding-left: 2.5rem;
          color: #374151;
          font-size: 18px;
        }

        .blog-content-styled ul li::before {
          content: '▶';
          position: absolute;
          left: 0;
          top: 2px;
          color: #FF6B35;
          font-weight: bold;
          font-size: 16px;
        }

        .blog-content-styled strong {
          color: #FF6B35;
          font-weight: 700;
          background: linear-gradient(135deg, #FF6B35/15, #FF6B35/5);
          padding: 0.3rem 0.6rem;
          border-radius: 6px;
          border: 1px solid #FF6B35/20;
          box-shadow: 0 1px 3px rgba(255, 107, 53, 0.1);
        }

        .blog-content-styled em {
          color: #1E3A8A;
          font-style: italic;
          font-weight: 600;
          background: linear-gradient(135deg, #1E3A8A/10, #1E3A8A/5);
          padding: 0.2rem 0.4rem;
          border-radius: 4px;
        }

        .blog-content-styled blockquote {
          border: none;
          padding: 2.5rem;
          margin: 3rem 0;
          background: linear-gradient(135deg, #1E3A8A/10, #FF6B35/10);
          border-radius: 20px;
          position: relative;
          font-style: italic;
          font-size: 1.2rem;
          color: #1E3A8A;
          border-left: 8px solid #FF6B35;
          box-shadow: 0 8px 30px rgba(30, 58, 138, 0.1);
        }

        .blog-content-styled blockquote::before {
          content: '"';
          position: absolute;
          top: -15px;
          left: 25px;
          font-size: 5rem;
          color: #FF6B35;
          font-family: serif;
          opacity: 0.7;
        }

        .blog-content-styled img {
          max-width: 100%;
          height: auto;
          max-height: 400px;
          width: auto;
          border-radius: 16px;
          box-shadow: 0 15px 35px -10px rgba(30, 58, 138, 0.2);
          margin: 2rem auto;
          display: block;
          border: 3px solid transparent;
          background: linear-gradient(white, white) padding-box,
                      linear-gradient(135deg, #1E3A8A, #FF6B35) border-box;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .blog-content-styled img:hover {
          transform: scale(1.01);
          box-shadow: 0 20px 40px -10px rgba(30, 58, 138, 0.3);
        }

        .blog-content-styled a {
          color: #FF6B35;
          text-decoration: none;
          font-weight: 600;
          position: relative;
          padding: 0.3rem 0.6rem;
          border-radius: 8px;
          background: linear-gradient(135deg, #FF6B35/15, #FF6B35/5);
          transition: all 0.3s ease;
          border: 1px solid #FF6B35/20;
        }

        .blog-content-styled a:hover {
          color: #1E3A8A;
          background: linear-gradient(135deg, #1E3A8A/15, #FF6B35/15);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
        }

        .blog-content-styled .internal-link {
          color: #1E3A8A;
          background: linear-gradient(135deg, #1E3A8A/15, #1E3A8A/5);
          border: 1px solid #1E3A8A/20;
        }

        .blog-content-styled .internal-link:hover {
          color: #FF6B35;
          background: linear-gradient(135deg, #FF6B35/15, #1E3A8A/15);
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.2);
        }

        .blog-content-styled .external-link::after {
          content: '↗';
          margin-left: 4px;
          font-size: 0.8em;
          opacity: 0.7;
        }

        .blog-content-styled code {
          background: linear-gradient(135deg, #1E3A8A/15, #FF6B35/15);
          color: #1E3A8A;
          padding: 0.3rem 0.6rem;
          border-radius: 8px;
          font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
          font-size: 0.9em;
          font-weight: 600;
          border: 1px solid #1E3A8A/20;
          box-shadow: 0 1px 3px rgba(30, 58, 138, 0.1);
        }

        .blog-content-styled pre {
          display: none !important;
        }

        .blog-content-styled table {
          width: 100%;
          border-collapse: collapse;
          margin: 3rem 0;
          background: white;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.1);
          border: 2px solid #1E3A8A/10;
        }

        .blog-content-styled th,
        .blog-content-styled td {
          padding: 1.25rem;
          text-align: left;
          border-bottom: 1px solid #e5e7eb;
        }

        .blog-content-styled th {
          background: linear-gradient(135deg, #1E3A8A, #1E3A8A/90);
          color: white;
          font-weight: 700;
          font-size: 16px;
        }

        .blog-content-styled tr:hover {
          background: linear-gradient(135deg, #1E3A8A/8, #FF6B35/8);
          transform: scale(1.01);
          transition: all 0.2s ease;
        }

        /* Additional styling for better readability */
        .blog-content-styled hr {
          border: none;
          height: 4px;
          background: linear-gradient(90deg, #1E3A8A, #FF6B35, #1E3A8A);
          margin: 3rem 0;
          border-radius: 2px;
        }

        .blog-content-styled .highlight {
          background: linear-gradient(135deg, #FF6B35/20, #FF6B35/10);
          padding: 0.5rem 1rem;
          border-radius: 8px;
          border-left: 4px solid #FF6B35;
          margin: 1rem 0;
          font-weight: 600;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .blog-content-styled {
            font-size: 17px;
          }

          .blog-content-styled h1 {
            font-size: 2.25rem;
          }

          .blog-content-styled h2 {
            font-size: 1.875rem;
            padding: 1.25rem 1.5rem;
          }

          .blog-content-styled h3 {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
}
