"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  ArrowRight, CheckCircle, TrendingUp, Users, Target,
  BarChart3, Palette, Rocket, Smartphone, Search,
  Sparkles, Trophy, Brush, Brain, Gauge, Star, Award
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 }
};

// Premium stats for billion-dollar feel
const stats = [
  { number: "500%", label: "Average Conversion Increase", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "£1.8M+", label: "Revenue Generated", icon: <Target className="h-6 w-6" /> },
  { number: "96%", label: "Client Satisfaction Rate", icon: <Users className="h-6 w-6" /> },
  { number: "24hrs", label: "Design Turnaround", icon: <Rocket className="h-6 w-6" /> }
];

// Premium design features
const designFeatures = [
  {
    icon: <Brain className="h-8 w-8" />,
    title: "Conversion Psychology Design",
    description: "Every element is strategically placed using proven psychological principles to maximize user engagement and conversions.",
    highlight: "Psychology-Driven"
  },
  {
    icon: <Gauge className="h-8 w-8" />,
    title: "Lightning-Fast Performance",
    description: "Sub-2-second load times guaranteed with advanced optimization techniques and premium hosting infrastructure.",
    highlight: "Speed Optimized"
  },
  {
    icon: <Smartphone className="h-8 w-8" />,
    title: "Mobile-First Excellence",
    description: "Designed for mobile perfection first, then scaled up to create seamless experiences across all devices.",
    highlight: "Mobile-First"
  },
  {
    icon: <Search className="h-8 w-8" />,
    title: "SEO-Engineered Architecture",
    description: "Built with advanced SEO architecture to dominate search rankings from day one of launch.",
    highlight: "SEO-Ready"
  }
];

// Premium service packages
const designPackages = [
  {
    name: "Conversion Catalyst",
    price: "£3,500",
    period: "",
    description: "Perfect for businesses ready to transform their online presence",
    features: [
      "Custom conversion-focused design",
      "Mobile-first responsive development",
      "Advanced SEO optimization",
      "Performance optimization",
      "2 weeks delivery",
      "30-day support included"
    ],
    highlight: false,
    cta: "Transform My Website"
  },
  {
    name: "Revenue Accelerator",
    price: "£7,500",
    period: "",
    description: "For businesses serious about market domination",
    features: [
      "Everything in Conversion Catalyst",
      "Advanced analytics integration",
      "A/B testing setup",
      "Custom CMS development",
      "E-commerce functionality",
      "90-day optimization support"
    ],
    highlight: true,
    cta: "Accelerate My Revenue"
  },
  {
    name: "Enterprise Dominator",
    price: "Custom",
    period: "",
    description: "Bespoke solutions for industry leaders",
    features: [
      "Everything in Revenue Accelerator",
      "Custom application development",
      "Advanced integrations",
      "Dedicated project team",
      "White-glove service",
      "Ongoing optimization"
    ],
    highlight: false,
    cta: "Get Custom Quote"
  }
];

const features = [
  {
    icon: <Palette className="h-6 w-6" />,
    title: "Custom Design & Branding",
    description: "Unique designs that reflect your brand identity and stand out from template-based competitors."
  },
  {
    icon: <Smartphone className="h-6 w-6" />,
    title: "Mobile-First Development",
    description: "Responsive designs that work flawlessly across all devices, ensuring optimal user experience."
  },
  {
    icon: <TrendingUp className="h-6 w-6" />,
    title: "Conversion Optimization",
    description: "Every element strategically placed to guide visitors toward taking action and becoming customers."
  },
  {
    icon: <Rocket className="h-6 w-6" />,
    title: "Lightning-Fast Performance",
    description: "Optimized for speed with advanced caching, compression, and modern development practices."
  },
  {
    icon: <Search className="h-6 w-6" />,
    title: "SEO-Optimized Structure",
    description: "Built with clean code and SEO best practices to help you rank higher in search results."
  },
  {
    icon: <BarChart3 className="h-6 w-6" />,
    title: "Advanced Analytics",
    description: "Comprehensive tracking setup to monitor performance and user behavior from day one."
  }
];

const process = [
  {
    step: "01",
    title: "Discovery & Strategy",
    description: "We dive deep into your business goals, target audience, and competitive landscape to create a strategic foundation.",
    duration: "Week 1"
  },
  {
    step: "02",
    title: "Design & Prototyping",
    description: "Custom designs and interactive prototypes that bring your vision to life before development begins.",
    duration: "Week 2-3"
  },
  {
    step: "03",
    title: "Development & Testing",
    description: "Clean, modern code development with rigorous testing across all devices and browsers.",
    duration: "Week 4-5"
  },
  {
    step: "04",
    title: "Launch & Optimization",
    description: "Smooth launch with ongoing monitoring and optimization to ensure peak performance.",
    duration: "Week 6+"
  }
];


export default function WebDesignClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Brush className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">PROFESSIONAL WEB DESIGN SERVICES</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Custom Web Design That
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Converts Visitors Into Customers
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Leading Web Design Company Specializing in{" "}
                    <span className="text-[#FF6B35] relative">
                      Responsive Website Design & UI/UX Optimization
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Professional web design services that combine stunning visuals with strategic functionality. We create custom websites, responsive designs, and user experiences that drive real business results and maximize conversions.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#4CAF50]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <TrendingUp className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#4CAF50] transition-colors duration-200">
                      Conversion-Focused Design
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#1E3A8A]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <CheckCircle className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#1E3A8A] transition-colors duration-200">
                      Expert Web Development
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#FF6B35]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Brush className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Premium UI/UX Design
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={scaleIn}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-[#1E3A8A] mb-2">{stat.number}</div>
                <div className="text-slate-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Design Features Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Sparkles className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">PREMIUM DESIGN FEATURES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Design That
                <span className="block text-[#FF6B35]">Drives Results</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Every element is strategically crafted to maximize conversions and deliver exceptional user experiences.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {designFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <CardContent className="relative p-8">
                    <div className="flex items-start space-x-6 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <h3 className="text-xl font-bold text-[#1E3A8A]">{feature.title}</h3>
                          <Badge className="bg-[#FF6B35]/10 text-[#FF6B35] border-[#FF6B35]/20 text-xs">
                            {feature.highlight}
                          </Badge>
                        </div>
                        <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                      </div>
                    </div>
                  </CardContent>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Features Grid Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Complete Web Design
                <span className="block text-[#FF6B35]">Services</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From concept to launch, we provide everything you need for a successful web presence.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 bg-white shadow-lg shadow-gray-900/5 rounded-2xl overflow-hidden group hover:shadow-xl hover:shadow-gray-900/10 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-xl flex items-center justify-center mb-6 text-white group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                    <h3 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Pricing Packages Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">PREMIUM PACKAGES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Choose Your
                <span className="block text-[#FF6B35]">Success Package</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Tailored solutions designed to meet your specific business goals and budget.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-3 gap-8"
          >
            {designPackages.map((pkg, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className={`relative h-full border-0 rounded-3xl overflow-hidden transition-all duration-500 ${
                  pkg.highlight
                    ? 'bg-gradient-to-b from-[#1E3A8A] to-[#1E3A8A]/90 text-white shadow-2xl shadow-[#1E3A8A]/25 scale-105'
                    : 'bg-white shadow-xl shadow-gray-900/5 hover:shadow-2xl hover:shadow-gray-900/10'
                }`}>
                  {pkg.highlight && (
                    <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] text-white text-center py-3 font-bold text-sm tracking-wide">
                      MOST POPULAR
                    </div>
                  )}

                  <CardContent className={`p-8 ${pkg.highlight ? 'pt-16' : ''}`}>
                    <div className="text-center mb-8">
                      <h3 className={`text-2xl font-bold mb-2 ${pkg.highlight ? 'text-white' : 'text-[#1E3A8A]'}`}>
                        {pkg.name}
                      </h3>
                      <p className={`mb-6 ${pkg.highlight ? 'text-slate-300' : 'text-slate-600'}`}>
                        {pkg.description}
                      </p>
                      <div className="mb-6">
                        <span className={`text-5xl font-bold ${pkg.highlight ? 'text-white' : 'text-[#1E3A8A]'}`}>
                          {pkg.price}
                        </span>
                        {pkg.period && (
                          <span className={`text-lg ${pkg.highlight ? 'text-blue-200' : 'text-slate-500'}`}>
                            {pkg.period}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <CheckCircle className={`h-5 w-5 flex-shrink-0 ${pkg.highlight ? 'text-[#FF6B35]' : 'text-[#4CAF50]'}`} />
                          <span className={`${pkg.highlight ? 'text-slate-300' : 'text-slate-600'}`}>
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>

                    <Button onClick={() => {
                      const el = document.getElementById('appointment-form');
                      if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }} className={`w-full py-4 rounded-xl font-bold transition-all duration-300 ${
                      pkg.highlight
                        ? 'bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF6B35]/90 hover:to-[#FF8A65]/90 text-white shadow-lg hover:shadow-xl'
                        : 'bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white shadow-lg hover:shadow-xl hover:scale-105'
                    }`}>
                      {pkg.cta}
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </CardContent>

                  {!pkg.highlight && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                  )}
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Rocket className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">OUR PROCESS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                From Concept to
                <span className="block text-[#FF6B35]">Launch</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Our proven 4-step process ensures your project is delivered on time, on budget, and exceeds expectations.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {process.map((step, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 bg-white shadow-lg shadow-gray-900/5 rounded-2xl overflow-hidden group hover:shadow-xl hover:shadow-gray-900/10 transition-all duration-300">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold group-hover:scale-110 transition-transform duration-300">
                      {step.step}
                    </div>
                    <h3 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                      {step.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed mb-4">
                      {step.description}
                    </p>
                    <Badge className="bg-[#1E3A8A]/10 text-[#1E3A8A] border-[#1E3A8A]/20">
                      {step.duration}
                    </Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      

      {/* Premium Testimonials Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Transforming Businesses
                <span className="block text-[#FF6B35]">Across Industries</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Real results from real clients who trusted us to redesign their digital presence and accelerate their growth.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16"
          >
            {/* Featured Testimonial */}
            <motion.div variants={fadeInUp} className="lg:col-span-2">
              <Card className="border-0 rounded-3xl shadow-2xl bg-gradient-to-br from-white via-white to-[#FF6B35]/5 overflow-hidden">
                <CardContent className="p-10 lg:p-12">
                  <div className="flex flex-col lg:flex-row items-start gap-8">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 text-[#FF6B35] mb-6">
                        {[...Array(5)].map((_, s) => <Star key={s} className="h-6 w-6 fill-current" />)}
                      </div>
                      <blockquote className="text-2xl lg:text-3xl font-bold text-[#1E3A8A] leading-relaxed mb-6">
                        "WebforLeads transformed our outdated website into a conversion machine. In just 90 days, we saw a 347% increase in qualified leads and our bounce rate dropped by 68%. The design is stunning and the performance is incredible."
                      </blockquote>
                      <div className="flex items-center gap-4">
                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] flex items-center justify-center text-white text-xl font-bold">
                          SM
                        </div>
                        <div>
                          <div className="text-lg font-bold text-[#1E3A8A]">Sarah Mitchell</div>
                          <div className="text-slate-600">CEO, TechFlow Solutions</div>
                          <div className="text-sm text-[#FF6B35] font-semibold">B2B SaaS • $2M ARR</div>
                        </div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-32 h-32 rounded-2xl bg-gradient-to-br from-[#FF6B35]/20 to-[#1E3A8A]/20 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-3xl font-black text-[#1E3A8A]">347%</div>
                          <div className="text-sm text-slate-600 font-semibold">Lead Increase</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>

          {/* Additional Testimonials Grid */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {[
              {
                quote: "The new website increased our conversion rate from 2.1% to 8.7% within the first month. The user experience is flawless and the design perfectly captures our brand.",
                name: "Marcus Chen",
                role: "Head of Growth",
                company: "FinanceForward",
                industry: "FinTech",
                metric: "315%",
                metricLabel: "Conversion Boost",
                avatar: "MC"
              },
              {
                quote: "Working with WebforLeads was a game-changer. Our organic traffic doubled, and we're now ranking #1 for our primary keywords. The technical SEO work was exceptional.",
                name: "Elena Rodriguez",
                role: "Marketing Director",
                company: "GreenEnergy Co",
                industry: "Clean Tech",
                metric: "200%",
                metricLabel: "Traffic Growth",
                avatar: "ER"
              },
              {
                quote: "The e-commerce redesign completely transformed our business. Sales increased by 425% and our average order value went up by 60%. The checkout flow is incredibly smooth.",
                name: "David Thompson",
                role: "Founder",
                company: "ArtisanCraft",
                industry: "E-commerce",
                metric: "425%",
                metricLabel: "Sales Increase",
                avatar: "DT"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-1 text-[#FF6B35] mb-4">
                      {[...Array(5)].map((_, s) => <Star key={s} className="h-4 w-4 fill-current" />)}
                    </div>
                    
                    <blockquote className="text-slate-700 leading-relaxed mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] flex items-center justify-center text-white font-bold text-sm">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="text-sm font-bold text-[#1E3A8A]">{testimonial.name}</div>
                          <div className="text-xs text-slate-500">{testimonial.role}</div>
                          <div className="text-xs text-[#FF6B35] font-semibold">{testimonial.company}</div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-black text-[#FF6B35]">{testimonial.metric}</div>
                        <div className="text-xs text-slate-500 font-semibold">{testimonial.metricLabel}</div>
                      </div>
                    </div>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="mt-20 text-center"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <p className="text-slate-500 font-semibold">Trusted by 500+ businesses across industries</p>
              <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
                {["SaaS", "E-commerce", "FinTech", "HealthTech", "Real Estate", "Manufacturing"].map((industry, index) => (
                  <div key={index} className="px-4 py-2 bg-slate-100 rounded-full text-sm font-semibold text-slate-600">
                    {industry}
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Premium FAQs Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Brain className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Everything You Need
                <span className="block text-[#FF6B35]">To Know</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Get answers to the most common questions about our web design process, pricing, and what makes us different.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {[
              {
                category: "Process & Timeline",
                icon: <Rocket className="h-6 w-6" />,
                questions: [
                  {
                    q: "How long does a typical web design project take?",
                    a: "Most projects are completed in 4-6 weeks, depending on complexity and scope. Simple brochure sites can be done in 2-3 weeks, while complex e-commerce or custom applications may take 8-12 weeks. We provide detailed timelines during our initial consultation."
                  },
                  {
                    q: "What's your design and development process?",
                    a: "We follow a proven 4-phase process: Discovery & Strategy (Week 1), Design & Prototyping (Weeks 2-3), Development & Testing (Weeks 4-5), and Launch & Optimization (Week 6+). Each phase includes client reviews and feedback cycles."
                  }
                ]
              },
              {
                category: "Services & Features",
                icon: <Palette className="h-6 w-6" />,
                questions: [
                  {
                    q: "Do you provide content writing and SEO services?",
                    a: "Yes! We offer comprehensive content strategy, copywriting, and technical SEO optimization. Our team includes experienced copywriters and SEO specialists who ensure your site ranks well and converts visitors into customers."
                  },
                  {
                    q: "Will my website be mobile-friendly and responsive?",
                    a: "Absolutely. All our websites are built with a mobile-first approach, ensuring perfect functionality across all devices. We test on multiple screen sizes and optimize for Core Web Vitals to guarantee excellent user experience."
                  }
                ]
              },
              {
                category: "Pricing & Investment",
                icon: <Target className="h-6 w-6" />,
                questions: [
                  {
                    q: "What's included in your web design packages?",
                    a: "Our packages include custom design, responsive development, SEO optimization, analytics setup, SSL certificate, hosting setup assistance, and training. Higher-tier packages add e-commerce functionality, advanced integrations, and extended support."
                  },
                  {
                    q: "Do you offer payment plans or financing options?",
                    a: "Yes, we offer flexible payment schedules with 50% upfront and 50% on completion for most projects. For larger projects, we can arrange milestone-based payments or extended payment plans to fit your budget."
                  }
                ]
              },
              {
                category: "Support & Maintenance",
                icon: <Users className="h-6 w-6" />,
                questions: [
                  {
                    q: "What kind of support do you provide after launch?",
                    a: "We provide 30 days of complimentary support after launch, including bug fixes, minor content updates, and technical assistance. We also offer ongoing maintenance retainers for continued optimization, security updates, and feature additions."
                  },
                  {
                    q: "Can you help with website hosting and domain setup?",
                    a: "While we don't provide hosting directly, we recommend premium hosting providers and can assist with setup, domain configuration, and DNS management. We ensure your site is hosted on fast, secure servers optimized for performance."
                  }
                ]
              }
            ].map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={fadeInUp}
                className="space-y-6"
              >
                {/* Category Header */}
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF6B35]/80 flex items-center justify-center text-white">
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">{category.category}</h3>
                </div>

                {/* Questions for this category */}
                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <motion.div
                      key={faqIndex}
                      variants={fadeInUp}
                      whileHover={{ y: -2 }}
                      transition={{ type: "spring", stiffness: 300, damping: 25 }}
                    >
                      <Card className="border-0 rounded-2xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                        <CardContent className="p-8">
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 rounded-full bg-[#FF6B35]/10 flex items-center justify-center flex-shrink-0 mt-1">
                              <div className="w-2 h-2 rounded-full bg-[#FF6B35]"></div>
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                                {faq.q}
                              </h4>
                              <p className="text-slate-600 leading-relaxed">
                                {faq.a}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                        
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Help Section */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="mt-20 text-center"
          >
            <motion.div variants={fadeInUp}>
              <Card className="border-0 rounded-3xl shadow-xl bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] text-white overflow-hidden">
                <CardContent className="p-10">
                  <div className="max-w-2xl mx-auto">
                    <h3 className="text-2xl font-bold mb-4">Still Have Questions?</h3>
                    <p className="text-slate-300 mb-6 leading-relaxed">
                      Our team is here to help! Schedule a free consultation to discuss your specific needs and get personalized answers to all your questions.
                    </p>
                    <Button onClick={() => {
                      const el = document.getElementById('appointment-form');
                      if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }} className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF6B35]/90 hover:to-[#FF8A65]/90 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                      Schedule Free Consultation
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="appointment-form" className="py-24 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <Badge className="px-4 py-1 bg-white/10 text-white border border-white/20 rounded-full">Get Started</Badge>
              <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">Let’s Plan Your Next Big Win</h2>
              <p className="text-slate-300 text-lg max-w-xl">Share your goals and we'll propose a high-impact roadmap with clear timelines and pricing.</p>
            </div>
            <div>
              <AppointmentForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
