"use client";

import React from "react";
import { motion } from "framer-motion";
import { CircleHelp, CheckCircle, MessageCircle, Lightbulb, ArrowRight, Sparkles, HelpCircle, Users } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";


const faqData = [
  {
    id: "web-design-cost",
    question: "How much does professional web design and development cost?",
    answer: "Professional website costs vary based on complexity, features, and design requirements. Our custom websites typically range from $3,000 to $15,000, with most small business websites falling in the $5,000-$8,000 range. This includes responsive design, SEO optimization, content management system, and mobile optimization."
  },
  {
    id: "website-timeline",
    question: "How long does it take to build a custom website?",
    answer: "Most professional websites take 4-8 weeks to complete, depending on the scope and complexity. This includes design, development, content creation, testing, and launch. Mobile apps typically take 8-16 weeks, while complex software development projects can take 3-6 months."
  },
  {
    id: "seo-results",
    question: "How long does SEO take to show results?",
    answer: "SEO is a long-term strategy. You can expect to see initial improvements in 3-6 months, with significant results in 6-12 months. Our SEO services include technical optimization, content creation, keyword research, and link building to improve your search engine rankings and organic traffic."
  },
  {
    id: "mobile-app-development",
    question: "Do you develop both iOS and Android mobile apps?",
    answer: "Yes, we develop native iOS and Android apps, as well as cross-platform solutions using React Native and Flutter. Our mobile app development services include UI/UX design, backend development, API integration, app store optimization, and ongoing maintenance and support."
  },
  {
    id: "digital-marketing-services",
    question: "What digital marketing services do you offer?",
    answer: "We offer comprehensive digital marketing services including SEO, Google Ads management, social media marketing, content marketing, email marketing, conversion rate optimization, and analytics. Our data-driven approach ensures maximum ROI and measurable results for your marketing investment."
  },
  {
    id: "ecommerce-development",
    question: "Can you build e-commerce websites and online stores?",
    answer: "Absolutely! We specialize in e-commerce development using platforms like Shopify, WooCommerce, and Magento. Our e-commerce solutions include payment gateway integration, inventory management, shipping calculators, product catalogs, and mobile-responsive design for optimal user experience."
  },
  {
    id: "website-maintenance",
    question: "Do you provide ongoing website maintenance and support?",
    answer: "Yes, we offer comprehensive website maintenance services including security updates, performance optimization, content updates, backup management, and technical support. Our maintenance plans ensure your website stays secure, fast, and up-to-date with the latest technologies."
  },
  {
    id: "social-media-management",
    question: "What social media platforms do you manage?",
    answer: "We manage all major social media platforms including Facebook, Instagram, LinkedIn, Twitter, YouTube, and TikTok. Our social media marketing services include content creation, community management, paid advertising, influencer partnerships, and performance analytics to grow your online presence."
  }
];

export default function SectionFAQ() {
  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  return (
    <section className="relative py-24 lg:py-32 bg-white overflow-hidden">

      <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Header Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="mb-8">
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm"
            >
              <HelpCircle className="h-6 w-6 animate-pulse text-[#FF6B35]" />
              <span className="tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
            </Badge>
          </motion.div>

          <motion.h2
            variants={fadeInUp}
            className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-10"
          >
            <span className="block text-[#1E3A8A] mb-3">Frequently Asked Questions</span>
            <span className="block text-[#FF6B35]">
              Web Design, SEO & Development
            </span>
          </motion.h2>

          <motion.p
            variants={fadeInUp}
            className="text-xl lg:text-2xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-medium"
          >
            Get answers to common questions about our web design, development, SEO, digital marketing, and mobile app development services. Learn about pricing, timelines, and what to expect from our professional services.
          </motion.p>
        </motion.div>

        {/* Premium FAQ Accordion */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="space-y-6"
        >
          <Accordion type="single" collapsible className="space-y-6">
            {faqData.map((faq, index) => (
              <motion.div
                key={faq.id}
                variants={fadeInUp}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AccordionItem
                  value={faq.id}
                  className="border-0 bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 rounded-3xl overflow-hidden transition-all duration-500 hover:border-[#FF6B35]/30"
                >
                  <AccordionTrigger className="px-8 py-6 text-left hover:no-underline hover:bg-gradient-to-r hover:from-[#FF6B35]/5 hover:to-[#1E3A8A]/5 transition-all duration-300 group focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/20">
                    <div className="flex items-center space-x-6 w-full">
                      <motion.div
                        className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center shadow-2xl shadow-[#1E3A8A]/25 group-hover:scale-110 transition-transform duration-300"
                        whileHover={{ rotate: 5 }}
                      >
                        <MessageCircle className="h-8 w-8 text-white" />
                      </motion.div>
                      <span className="text-2xl font-black text-[#1E3A8A] group-hover:text-[#FF6B35] transition-colors duration-300">
                        {faq.question}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-8 pb-8">
                    <div className="border-t border-slate-200/50 pt-6 mt-6"></div>
                    <div className="flex items-start space-x-6 pl-20">
                      <motion.div
                        className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-xl flex items-center justify-center shadow-lg"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <Lightbulb className="h-6 w-6 text-white" />
                      </motion.div>
                      <motion.p
                        className="text-slate-600 text-lg leading-relaxed font-medium"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        {faq.answer}
                      </motion.p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </motion.div>
            ))}
          </Accordion>
        </motion.div>

        {/* Premium CTA Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="text-center mt-20"
        >
          <Card className="glass-card shadow-premium-lg border-webforleads-navy/10 relative overflow-hidden">
            {/* Background accent */}
            <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-radial from-webforleads-orange/8 to-transparent rounded-full blur-3xl animate-pulse-premium"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-webforleads-green/6 to-transparent rounded-full blur-3xl animate-float"></div>

            <CardContent className="relative p-12 text-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Badge className="inline-flex items-center px-4 py-2 bg-webforleads-navy/10 text-webforleads-navy border-webforleads-navy/20 mb-6">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Expert Support
                </Badge>
              </motion.div>

              <motion.div
                className="inline-flex items-center justify-center w-16 h-16 gradient-navy-premium rounded-2xl mb-6 shadow-premium"
                whileHover={{ scale: 1.1, rotate: 5 }}
              >
                <Users className="h-8 w-8 text-white" />
              </motion.div>

              <h3 className="text-3xl lg:text-4xl font-bold text-webforleads-gray-900 mb-6">
                Still Have Questions?
              </h3>
              <p className="text-xl text-webforleads-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                Our team of digital marketing experts is here to help. Get personalized answers to your specific questions.
              </p>
              <Button
                size="lg"
                className="btn-premium gradient-orange-premium text-white px-10 py-4 text-lg font-semibold rounded-xl shadow-premium-lg hover:shadow-glow-orange focus-premium-orange"
              >
                <MessageCircle className="h-5 w-5 mr-2" />
                Schedule a Free Consultation
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
