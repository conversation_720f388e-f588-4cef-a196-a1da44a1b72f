// Template Registry - Central configuration for all service templates
// This file manages all available service templates for bulk generation

export interface TemplateConfig {
  id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  defaultKeywords: string[];
  exampleLocations: string[];
  pricing: {
    starter: string;
    growth: string;
    enterprise: string;
  };
  estimatedPages?: number;
}

export interface ServiceConfig {
  serviceName: string;
  serviceSlug: string;
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  hero: { badge: string; title: string; description: string };
  blueprint: { badge: string; title: string; description: string; features: string[] };
  coreProblem: { badge: string; title: string; description: string; problems: string[] };
  services: { badge: string; title: string; description: string; services: Array<{ title: string; description: string; features: string[] }> };
  testimonials: { badge: string; title: string; description: string };
  faq: { badge: string; title: string; description: string; faqs: Array<{ question: string; answer: string }> };
  geo: { badge: string; title: string; description: string };
  finalCTA: { badge: string; title: string; description: string };
  structuredData: { serviceName: string; description: string; priceRange: string; areaServed: string; aggregateRating: { ratingValue: string; reviewCount: string } };
}

// Available Service Templates
export const SERVICE_TEMPLATES: TemplateConfig[] = [
  {
    id: "web-design",
    name: "Web Design Services",
    slug: "web-design",
    description: "Professional web design and development services including custom websites, responsive design, and conversion optimization.",
    category: "Design & Development",
    defaultKeywords: [
      "web design",
      "website development", 
      "custom web design",
      "responsive web design",
      "website redesign",
      "professional web design",
      "business website design",
      "e-commerce web design",
      "wordpress development",
      "website builder"
    ],
    exampleLocations: [
      "London", "Manchester", "Birmingham", "Leeds", "Liverpool",
      "Bristol", "Sheffield", "Edinburgh", "Glasgow", "Cardiff"
    ],
    pricing: {
      starter: "£3,500",
      growth: "£7,500", 
      enterprise: "Custom"
    },
    estimatedPages: 100
  },
  {
    id: "seo",
    name: "SEO Services",
    slug: "seo",
    description: "Comprehensive search engine optimization services including technical SEO, keyword research, and link building.",
    category: "Digital Marketing",
    defaultKeywords: [
      "SEO services",
      "search engine optimization",
      "local SEO",
      "SEO company",
      "SEO consultant",
      "technical SEO",
      "SEO audit",
      "keyword research",
      "link building",
      "Google ranking"
    ],
    exampleLocations: [
      "London", "Manchester", "Birmingham", "Leeds", "Liverpool",
      "Bristol", "Sheffield", "Edinburgh", "Glasgow", "Cardiff"
    ],
    pricing: {
      starter: "£1,500",
      growth: "£3,500",
      enterprise: "£7,500"
    },
    estimatedPages: 150
  },
  {
    id: "digital-marketing",
    name: "Digital Marketing Services", 
    slug: "digital-marketing",
    description: "Full-service digital marketing including PPC, social media, content marketing, and conversion optimization.",
    category: "Digital Marketing",
    defaultKeywords: [
      "digital marketing",
      "online marketing",
      "digital marketing agency",
      "PPC management",
      "social media marketing",
      "content marketing",
      "email marketing",
      "conversion optimization",
      "marketing automation",
      "digital advertising"
    ],
    exampleLocations: [
      "London", "Manchester", "Birmingham", "Leeds", "Liverpool",
      "Bristol", "Sheffield", "Edinburgh", "Glasgow", "Cardiff"
    ],
    pricing: {
      starter: "£2,000",
      growth: "£5,000",
      enterprise: "£10,000"
    },
    estimatedPages: 120
  },
  {
    id: "app-development",
    name: "App Development Services",
    slug: "app-development", 
    description: "Mobile and web application development services including iOS, Android, and cross-platform solutions.",
    category: "Design & Development",
    defaultKeywords: [
      "app development",
      "mobile app development",
      "iOS app development", 
      "Android app development",
      "web app development",
      "custom app development",
      "app design",
      "mobile application",
      "app development company",
      "software development"
    ],
    exampleLocations: [
      "London", "Manchester", "Birmingham", "Leeds", "Liverpool",
      "Bristol", "Sheffield", "Edinburgh", "Glasgow", "Cardiff"
    ],
    pricing: {
      starter: "£15,000",
      growth: "£35,000",
      enterprise: "£75,000"
    },
    estimatedPages: 80
  },

  {
    id: "google-ads",
    name: "Google Ads Services",
    slug: "google-ads",
    description: "Professional Google Ads management and PPC advertising services to drive instant, qualified traffic and maximize ROI.",
    category: "Digital Marketing",
    defaultKeywords: [
      "Google Ads management",
      "PPC advertising",
      "Google Ads services",
      "pay per click",
      "Google AdWords",
      "PPC management",
      "search advertising",
      "display advertising",
      "shopping ads",
      "YouTube advertising"
    ],
    exampleLocations: [
      "London", "Manchester", "Birmingham", "Leeds", "Liverpool",
      "Bristol", "Sheffield", "Edinburgh", "Glasgow", "Cardiff"
    ],
    pricing: {
      starter: "£800",
      growth: "£1,800",
      enterprise: "£4,500"
    },
    estimatedPages: 130
  },
  {
    id: "social-media",
    name: "Social Media Marketing",
    slug: "social-media",
    description: "Comprehensive social media marketing and management services to build your brand and engage your audience across all platforms.",
    category: "Digital Marketing",
    defaultKeywords: [
      "social media marketing",
      "social media management",
      "Facebook marketing",
      "Instagram marketing",
      "LinkedIn marketing",
      "TikTok marketing",
      "social media strategy",
      "content creation",
      "social media advertising",
      "influencer marketing"
    ],
    exampleLocations: [
      "London", "Manchester", "Birmingham", "Leeds", "Liverpool",
      "Bristol", "Sheffield", "Edinburgh", "Glasgow", "Cardiff"
    ],
    pricing: {
      starter: "£1,200",
      growth: "£2,800",
      enterprise: "£5,500"
    },
    estimatedPages: 140
  },

];

// Get template by ID
export function getTemplateById(templateId: string): TemplateConfig | null {
  return SERVICE_TEMPLATES.find(template => template.id === templateId) || null;
}

// Get templates by category
export function getTemplatesByCategory(category: string): TemplateConfig[] {
  return SERVICE_TEMPLATES.filter(template => template.category === category);
}

// Get all template categories
export function getTemplateCategories(): string[] {
  const categories = SERVICE_TEMPLATES.map(template => template.category);
  return [...new Set(categories)];
}

// Get template suggestions based on keywords
export function getTemplateSuggestions(keywords: string[]): TemplateConfig[] {
  const suggestions: { template: TemplateConfig; score: number }[] = [];
  
  SERVICE_TEMPLATES.forEach(template => {
    let score = 0;
    keywords.forEach(keyword => {
      template.defaultKeywords.forEach(defaultKeyword => {
        if (defaultKeyword.toLowerCase().includes(keyword.toLowerCase()) || 
            keyword.toLowerCase().includes(defaultKeyword.toLowerCase())) {
          score++;
        }
      });
    });
    
    if (score > 0) {
      suggestions.push({ template, score });
    }
  });
  
  return suggestions
    .sort((a, b) => b.score - a.score)
    .map(item => item.template)
    .slice(0, 3);
}

// Validate template configuration
export function validateTemplate(templateId: string): boolean {
  const template = getTemplateById(templateId);
  return template !== null;
}

// Get template file path
export function getTemplateFilePath(templateId: string): string {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }
  
  return `src/app/${template.slug}/page.tsx`;
}

// Generate slug for template page
export function generateTemplateSlug(templateId: string, keyword: string, location: string): string {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }
  
  // Create slug from keyword and location
  const combined = `${keyword} ${location}`;
  return combined
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Generate page title for template
export function generateTemplateTitle(templateId: string, keyword: string, location: string): string {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }
  
  // Capitalize first letter of each word
  const capitalizeWords = (str: string) => {
    return str.replace(/\b\w/g, l => l.toUpperCase());
  };
  
  return `Professional ${capitalizeWords(keyword)} Services in ${capitalizeWords(location)} | WebforLeads`;
}

export default SERVICE_TEMPLATES;
