"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  RefreshCw, ExternalLink, CheckCircle, AlertCircle, 
  Globe, FileText, Loader2, Search, Map
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

export default function SitemapAdminPage() {
  const [isRevalidating, setIsRevalidating] = useState(false);
  const [lastRevalidation, setLastRevalidation] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleRevalidateSitemap = async () => {
    try {
      setIsRevalidating(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/revalidate-sitemap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type: 'manual', source: 'admin' }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Sitemap revalidated successfully!');
        setLastRevalidation(new Date().toLocaleString());
      } else {
        setError(data.error || 'Failed to revalidate sitemap');
      }
    } catch (err) {
      setError('Failed to revalidate sitemap');
      console.error('Error revalidating sitemap:', err);
    } finally {
      setIsRevalidating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Header */}
      <section className="bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="text-center"
          >
            <motion.div variants={fadeInUp} className="mb-6">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <Map className="h-12 w-12 text-[#FF6B35]" />
                <h1 className="text-4xl md:text-5xl font-bold">Sitemap Management</h1>
              </div>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                Monitor and manage your website's XML sitemap for optimal SEO performance
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Error/Success Messages */}
      {(error || success) && (
        <section className="py-4">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4"
              >
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                  <span className="text-red-700">{error}</span>
                  <Button
                    onClick={() => setError(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-red-500 hover:text-red-700"
                  >
                    ×
                  </Button>
                </div>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-50 border border-green-200 rounded-xl p-4 mb-4"
              >
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-green-700">{success}</span>
                  <Button
                    onClick={() => setSuccess(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-green-500 hover:text-green-700"
                  >
                    ×
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        </section>
      )}

      {/* Sitemap Management */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="space-y-8"
          >
            {/* Sitemap Status */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-[#1E3A8A] mb-4">Sitemap Status</h2>
                    <p className="text-gray-600">Your sitemap is automatically updated when new content is created</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div className="bg-gray-50 rounded-lg p-6 text-center">
                      <Globe className="h-12 w-12 text-[#1E3A8A] mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Sitemap URL</h3>
                      <p className="text-gray-600 mb-4">https://webforleads.uk/sitemap.xml</p>
                      <Button
                        onClick={() => window.open('/sitemap.xml', '_blank')}
                        variant="outline"
                        className="border-[#1E3A8A] text-[#1E3A8A] hover:bg-[#1E3A8A] hover:text-white"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Sitemap
                      </Button>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-6 text-center">
                      <Search className="h-12 w-12 text-[#FF6B35] mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Robots.txt</h3>
                      <p className="text-gray-600 mb-4">https://webforleads.uk/robots.txt</p>
                      <Button
                        onClick={() => window.open('/robots.txt', '_blank')}
                        variant="outline"
                        className="border-[#FF6B35] text-[#FF6B35] hover:bg-[#FF6B35] hover:text-white"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Robots.txt
                      </Button>
                    </div>
                  </div>

                  {/* Manual Revalidation */}
                  <div className="text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Manual Revalidation</h3>
                    <p className="text-gray-600 mb-6">
                      Force update the sitemap cache if needed (normally happens automatically)
                    </p>
                    
                    <Button
                      onClick={handleRevalidateSitemap}
                      disabled={isRevalidating}
                      className="bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white px-8 py-3"
                    >
                      {isRevalidating ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Revalidating...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-5 w-5 mr-2" />
                          Revalidate Sitemap
                        </>
                      )}
                    </Button>

                    {lastRevalidation && (
                      <p className="text-sm text-gray-500 mt-4">
                        Last revalidation: {lastRevalidation}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Automatic Updates Info */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-0 shadow-lg">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Automatic Sitemap Updates</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white rounded-lg p-6">
                      <div className="flex items-center space-x-3 mb-4">
                        <FileText className="h-8 w-8 text-green-600" />
                        <h4 className="text-lg font-semibold text-gray-900">Blog Posts</h4>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Sitemap automatically updates when new blog posts are generated
                      </p>
                      <Badge className="bg-green-100 text-green-800">Auto-Updated</Badge>
                    </div>

                    <div className="bg-white rounded-lg p-6">
                      <div className="flex items-center space-x-3 mb-4">
                        <Globe className="h-8 w-8 text-blue-600" />
                        <h4 className="text-lg font-semibold text-gray-900">Service Pages</h4>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Sitemap automatically updates when new area service pages are created
                      </p>
                      <Badge className="bg-blue-100 text-blue-800">Auto-Updated</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
