// Test script to debug the create-job API
const testData = {
  name: "Test Job",
  templateId: "web-design",
  templateName: "Web Design",
  keywords: ["web design", "website development"],
  locations: ["Leeds", "Manchester"]
};

fetch('http://localhost:3000/api/bulk-pages/create-job', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(testData)
})
.then(response => {
  console.log('Response status:', response.status);
  return response.json();
})
.then(data => {
  console.log('Response data:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
