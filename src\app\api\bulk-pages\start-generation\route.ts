import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { updateBulkJob } from '@/lib/bulk-pages-storage';
import { getTemplateById, type ServiceConfig } from '../../../site-pages/template-registry';
import { registerRoute } from '@/lib/route-registry';

// Generate config with proper React icon components
function generateConfigWithIcons(config: ServiceConfig): string {
  // Convert the config to a string and replace icon references with actual React components
  let configString = JSON.stringify(config, null, 2);

  // Replace icon string references with React components
  const iconReplacements = {
    '"TrendingUp"': '<TrendingUp className="h-6 w-6" />',
    '"Target"': '<Target className="h-6 w-6" />',
    '"Users"': '<Users className="h-6 w-6" />',
    '"Rocket"': '<Rocket className="h-6 w-6" />',
    '"BarChart3"': '<BarChart3 className="h-6 w-6" />',
    '"Search"': '<Search className="h-6 w-6" />',
    '"Globe"': '<Globe className="h-6 w-6" />',
    '"Zap"': '<Zap className="h-6 w-6" />',
    '"Brain"': '<Brain className="h-8 w-8" />',
    '"Gauge"': '<Gauge className="h-8 w-8" />',
    '"Smartphone"': '<Smartphone className="h-6 w-6" />',
    '"Palette"': '<Palette className="h-6 w-6" />',
    '"Code"': '<Code className="h-6 w-6" />',
    '"DollarSign"': '<DollarSign className="h-6 w-6" />',
    '"MousePointer"': '<MousePointer className="h-6 w-6" />',
    '"Share2"': '<Share2 className="h-6 w-6" />',
    '"Heart"': '<Heart className="h-6 w-6" />',
    '"MessageCircle"': '<MessageCircle className="h-6 w-6" />',
    '"Camera"': '<Camera className="h-6 w-6" />',
    '"Tablet"': '<Tablet className="h-6 w-6" />',
    '"Monitor"': '<Monitor className="h-6 w-6" />'
  };

  // Apply replacements
  Object.entries(iconReplacements).forEach(([search, replace]) => {
    configString = configString.replace(new RegExp(search, 'g'), replace);
  });

  return configString;
}

// Environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_TEXT_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

// Interfaces
interface GeneratedPage {
  id: string;
  keyword: string;
  location: string;
  slug: string;
  title: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  createdAt: string;
  filePath?: string;
  error?: string;
  progress?: number;
}

interface BulkGenerationJob {
  id: string;
  name: string;
  templateId: string;
  templateName: string;
  keywords: string[];
  locations: string[];
  status: 'pending' | 'running' | 'completed' | 'paused' | 'error';
  progress: number;
  totalPages: number;
  completedPages: number;
  createdAt: string;
  estimatedCompletion?: string;
  pages: GeneratedPage[];
}

interface ServiceConfig {
  serviceName: string;
  serviceSlug: string;
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  heroBadgeText: string;
  stats: Array<{ number: string; label: string; icon: string }>;
  designFeatures: Array<{ title: string; description: string; highlight: string }>;
  features: Array<{ title: string; description: string }>;
  packages: Array<{ name: string; price: string; period: string; description: string; features: string[]; highlight: boolean; cta: string }>;
  process: Array<{ step: string; title: string; description: string; duration: string }>;
  featuredTestimonial: { quote: string; name: string; role: string; company: string; rating: number; result: string; resultLabel: string };
  testimonials: Array<{ quote: string; name: string; role: string; company: string; industry: string; metric: string; metricLabel: string; avatar: string }>;
  faqCategories: Array<{ category: string; questions: Array<{ q: string; a: string }> }>;
  finalCTA: { badge: string; title: string; description: string };
  structuredData: { serviceName: string; description: string; priceRange: string; areaServed: string; aggregateRating: { ratingValue: string; reviewCount: string } };
}

// Generate service configuration using Gemini AI
async function generateServiceConfig(keyword: string, location: string, templateId: string): Promise<ServiceConfig> {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  const prompt = `Generate a comprehensive service configuration for a "${keyword}" service page targeting "${location}" using the "${template.name}" template.

IMPORTANT BUSINESS CONTEXT:
- Company Name: WebforLeads
- Business Type: Digital Marketing Agency
- Location: Based in UK, serving ${location} and surrounding areas
- Website: webforleads.uk
- Phone: 0800 123 4567
- Email: <EMAIL>
- Established: 2019
- Team Size: 15+ specialists
- Awards: Google Partner, Facebook Marketing Partner

Create content that is:
- Highly localized for ${location} with specific local references
- Professional and conversion-focused for WebforLeads brand
- SEO-optimized for "${keyword} ${location}"
- Uses "WebforLeads" as the company name throughout (NO placeholders like [agency name])
- Includes specific ${location} business references and local landmarks
- Mentions serving ${location} and surrounding areas
- Uses real testimonials with ${location}-area business names
- Matches the ${template.category} category
- Uses pricing structure similar to: ${template.pricing.starter} - ${template.pricing.enterprise}
- NO PLACEHOLDER TEXT - use real, specific content for WebforLeads

Return ONLY a valid JSON object with this exact structure:
{
  "serviceName": "Service name (e.g., Web Design)",
  "serviceSlug": "service-slug",
  "metaTitle": "SEO-optimized title under 60 characters",
  "metaDescription": "SEO-optimized description under 160 characters",
  "keywords": ["primary keyword", "secondary keyword", "tertiary keyword"],
  "heroTitle": "Compelling hero title",
  "heroSubtitle": "Supporting subtitle",
  "heroDescription": "Engaging description paragraph",
  "heroBadgeText": "BADGE TEXT",
  "stats": [
    {"number": "500%", "label": "Stat label", "icon": "TrendingUp"},
    {"number": "£1.8M+", "label": "Stat label", "icon": "Target"},
    {"number": "96%", "label": "Stat label", "icon": "Users"},
    {"number": "24hrs", "label": "Stat label", "icon": "Rocket"}
  ],
  "designFeatures": [
    {"icon": "Brain", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"},
    {"icon": "Gauge", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"},
    {"icon": "Target", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"},
    {"icon": "BarChart3", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"}
  ],
  "features": [
    {"icon": "Search", "title": "Feature title", "description": "Feature description"},
    {"icon": "Target", "title": "Feature title", "description": "Feature description"},
    {"icon": "Globe", "title": "Feature title", "description": "Feature description"},
    {"icon": "Zap", "title": "Feature title", "description": "Feature description"},
    {"icon": "BarChart3", "title": "Feature title", "description": "Feature description"},
    {"icon": "Users", "title": "Feature title", "description": "Feature description"}
  ],
  "packages": [
    {"name": "Package name", "price": "£3,500", "period": "one-time", "description": "Package description", "features": ["Feature 1", "Feature 2"], "highlight": false, "cta": "CTA text"},
    {"name": "Package name", "price": "£7,500", "period": "one-time", "description": "Package description", "features": ["Feature 1", "Feature 2"], "highlight": true, "cta": "CTA text"},
    {"name": "Package name", "price": "Custom", "period": "one-time", "description": "Package description", "features": ["Feature 1", "Feature 2"], "highlight": false, "cta": "CTA text"}
  ],
  "process": [
    {"step": "01", "title": "Step title", "description": "Step description", "duration": "Week 1"},
    {"step": "02", "title": "Step title", "description": "Step description", "duration": "Week 2-3"},
    {"step": "03", "title": "Step title", "description": "Step description", "duration": "Week 4-5"},
    {"step": "04", "title": "Step title", "description": "Step description", "duration": "Week 6+"}
  ],
  "featuredTestimonial": {
    "quote": "Testimonial quote",
    "name": "Client name",
    "role": "Client role",
    "company": "Company name",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Result label"
  },
  "testimonials": [
    {"quote": "Quote", "name": "Name", "role": "Role", "company": "Company", "industry": "Industry", "metric": "315%", "metricLabel": "Metric label", "avatar": "MC"},
    {"quote": "Quote", "name": "Name", "role": "Role", "company": "Company", "industry": "Industry", "metric": "200%", "metricLabel": "Metric label", "avatar": "ER"},
    {"quote": "Quote", "name": "Name", "role": "Role", "company": "Company", "industry": "Industry", "metric": "400%", "metricLabel": "Metric label", "avatar": "DP"}
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": "Rocket",
      "questions": [
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"}
      ]
    },
    {
      "category": "Services & Features",
      "icon": "Target",
      "questions": [
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"}
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": "BarChart3",
      "questions": [
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"}
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "CTA title",
    "description": "CTA description"
  },
  "structuredData": {
    "serviceName": "Service name",
    "description": "Service description",
    "priceRange": "£3000-£15000",
    "areaServed": "${location}",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
}`;

  try {
    console.log('🤖 Generating service config with Gemini AI');
    console.log('🔑 Keyword:', keyword);
    console.log('📍 Location:', location);

    const response = await fetch(`${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 4000,
        }
      }),
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!generatedText) {
      throw new Error('No content generated by Gemini');
    }

    // Parse JSON from generated text
    const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in generated content');
    }

    const config = JSON.parse(jsonMatch[0]) as ServiceConfig;
    console.log('✅ Service config generated successfully');
    
    return config;

  } catch (error) {
    console.error('❌ Error generating service config:', error);
    throw error;
  }
}

// Create page file from service config - simple individual folder approach
async function createPageFile(config: ServiceConfig, slug: string, templateId: string): Promise<string> {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  // Map template IDs to folder names
  const templateFolderMap: { [key: string]: string } = {
    'web-design': 'web-design',
    'seo': 'seo-services',
    'google-ads': 'google-ads',
    'social-media': 'social-media',
    'app-development': 'app-development',
    'digital-marketing': 'digital-marketing'
  };

  const serviceFolder = templateFolderMap[templateId];
  if (!serviceFolder) {
    throw new Error(`No folder mapping found for template: ${templateId}`);
  }

  // Generate component name from slug
  const componentName = slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('') + 'Page';

  const pageContent = `import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// ${config.serviceName} Service Configuration for ${slug}
// Generated using ${template.name} template for WebforLeads
const serviceConfig = ${generateConfigWithIcons(config)};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: \`https://webforleads.uk/${serviceFolder}/\${serviceConfig.serviceSlug}\`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: \`https://webforleads.uk/${serviceFolder}/\${serviceConfig.serviceSlug}\`,
  },
};

export default function ${componentName}() {
  return <ServiceTemplate config={serviceConfig} />;
}`;

  // Create individual folder for each page
  const pageDir = path.join(process.cwd(), 'src', 'app', serviceFolder, slug);
  const pageFile = path.join(pageDir, 'page.tsx');

  if (!fs.existsSync(pageDir)) {
    fs.mkdirSync(pageDir, { recursive: true });
  }

  fs.writeFileSync(pageFile, pageContent);

  console.log('📄 Page file created:', pageFile);
  console.log('📁 Service folder:', serviceFolder);
  console.log('🔗 URL will be:', `/${serviceFolder}/${slug}`);
  return pageFile;
}



export async function POST(request: NextRequest) {
  try {
    console.log('\n🚀 STARTING BULK GENERATION PROCESS');
    
    const { jobId } = await request.json();
    
    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    console.log('🆔 Job ID:', jobId);

    // Update job status to running
    const job = updateBulkJob(jobId, { 
      status: 'running',
      progress: 0
    });

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('✅ Job status updated to running');

    // Start background generation process
    generatePagesInBackground(jobId);

    return NextResponse.json({
      success: true,
      message: 'Generation started successfully',
      jobId
    });

  } catch (error) {
    console.error('💥 START GENERATION ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to start generation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Background generation process
async function generatePagesInBackground(jobId: string) {
  console.log('🔄 Starting background generation for job:', jobId);
  
  // This will run in the background
  // In a production environment, you'd want to use a proper job queue
  setTimeout(async () => {
    await processJobPages(jobId);
  }, 1000);
}

// Process all pages in a job
async function processJobPages(jobId: string) {
  try {
    const job = updateBulkJob(jobId, {});
    if (!job) return;

    console.log('📊 Processing', job.pages.length, 'pages for job:', jobId);

    for (let i = 0; i < job.pages.length; i++) {
      const page = job.pages[i];
      
      try {
        console.log(`🔄 Processing page ${i + 1}/${job.pages.length}: ${page.title}`);
        
        // Update page status to generating
        job.pages[i].status = 'generating';
        updateBulkJob(jobId, { pages: job.pages });

        // Generate service config with AI using template
        const config = await generateServiceConfig(page.keyword, page.location, job.templateId);

        // Update config with proper slug and service name
        config.serviceSlug = page.slug;

        // Create page file
        const filePath = await createPageFile(config, page.slug, job.templateId);
        
        // Register route in route registry
        registerRoute({
          slug: page.slug,
          title: page.title,
          templateId: job.templateId,
          templateName: job.templateName,
          keyword: page.keyword,
          location: page.location,
          filePath,
          createdAt: new Date().toISOString(),
          jobId: job.id,
          status: 'active'
        });

        // Update page status to completed
        job.pages[i].status = 'completed';
        job.pages[i].filePath = filePath;
        job.pages[i].progress = 100;

        // Update job progress
        const completedPages = job.pages.filter((p: { status: string }) => p.status === 'completed').length;
        const progress = Math.round((completedPages / job.pages.length) * 100);
        
        updateBulkJob(jobId, {
          pages: job.pages,
          completedPages,
          progress,
          status: progress === 100 ? 'completed' : 'running'
        });

        console.log(`✅ Page completed: ${page.title} (${completedPages}/${job.pages.length})`);

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`❌ Error processing page ${page.title}:`, error);
        
        // Update page status to error
        job.pages[i].status = 'error';
        job.pages[i].error = error instanceof Error ? error.message : 'Unknown error';
        
        updateBulkJob(jobId, { pages: job.pages });
      }
    }

    console.log('🎉 Bulk generation completed for job:', jobId);

    // Revalidate sitemap after bulk generation completes
    try {
      console.log('🗺️ Revalidating sitemap after bulk page generation...');
      await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/revalidate-sitemap`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'bulk-pages', jobId })
      });
      console.log('✅ Sitemap revalidation triggered');
    } catch (sitemapError) {
      console.warn('⚠️ Failed to revalidate sitemap:', sitemapError);
      // Don't fail the generation if sitemap revalidation fails
    }

  } catch (error) {
    console.error('💥 Error in background generation:', error);
    updateBulkJob(jobId, { status: 'error' });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bulk generation start API endpoint. Use POST to start generation for a job.',
    endpoints: {
      'POST /api/bulk-pages/start-generation': 'Start generation process for a bulk job'
    }
  });
}
