import * as React from 'react';

interface ContactEmailTemplateProps {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service?: string;
  message: string;
  submittedAt: string;
}

export function ContactEmailTemplate({
  name,
  email,
  phone,
  company,
  service,
  message,
  submittedAt
}: ContactEmailTemplateProps) {
  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      lineHeight: '1.6',
      color: '#2D3748',
      maxWidth: '650px',
      margin: '0 auto',
      backgroundColor: '#F7FAFC'
    }}>
      {/* Email Container */}
      <div style={{
        backgroundColor: '#FFFFFF',
        margin: '20px',
        borderRadius: '16px',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>

        {/* Header Section */}
        <div style={{
          backgroundColor: '#1E3A8A',
          padding: '0',
          position: 'relative' as const
        }}>
          {/* Top accent bar */}
          <div style={{
            backgroundColor: '#FF6B35',
            height: '6px',
            width: '100%'
          }} />

          <div style={{
            padding: '40px 40px 30px 40px',
            textAlign: 'center' as const
          }}>
            {/* Logo/Brand area */}
            <div style={{
              backgroundColor: '#FFFFFF',
              display: 'inline-block',
              padding: '12px 24px',
              borderRadius: '8px',
              marginBottom: '20px'
            }}>
              <h1 style={{
                color: '#1E3A8A',
                fontSize: '24px',
                fontWeight: '800',
                margin: '0',
                letterSpacing: '-0.5px'
              }}>
                WebforLeads
              </h1>
            </div>

            <h2 style={{
              color: '#FFFFFF',
              fontSize: '32px',
              fontWeight: '700',
              margin: '0 0 8px 0',
              letterSpacing: '-0.5px'
            }}>
              New Contact Message
            </h2>

            <p style={{
              color: '#FF6B35',
              fontSize: '16px',
              fontWeight: '600',
              margin: '0',
              textTransform: 'uppercase' as const,
              letterSpacing: '1px'
            }}>
              Premium Digital Solutions
            </p>
          </div>

          {/* Bottom wave decoration */}
          <div style={{
            height: '20px',
            backgroundColor: '#1E3A8A',
            position: 'relative' as const
          }}>
            <div style={{
              position: 'absolute' as const,
              bottom: '0',
              left: '0',
              width: '100%',
              height: '20px',
              backgroundColor: '#FFFFFF',
              borderRadius: '20px 20px 0 0'
            }} />
          </div>
        </div>

        {/* Main Content */}
        <div style={{
          padding: '40px 40px 30px 40px'
        }}>
          {/* Priority Alert */}
          <div style={{
            backgroundColor: '#FF6B35',
            color: '#FFFFFF',
            padding: '16px 24px',
            borderRadius: '12px',
            marginBottom: '30px',
            textAlign: 'center' as const,
            border: '3px solid #E53E3E'
          }}>
            <h3 style={{
              margin: '0 0 4px 0',
              fontSize: '18px',
              fontWeight: '700',
              textTransform: 'uppercase' as const,
              letterSpacing: '1px'
            }}>
              💬 NEW INQUIRY RECEIVED
            </h3>
            <p style={{
              margin: '0',
              fontSize: '14px',
              fontWeight: '600'
            }}>
              Respond within 2 hours for optimal customer satisfaction
            </p>
          </div>

          {/* Contact Information Card */}
          <div style={{
            backgroundColor: '#F7FAFC',
            border: '2px solid #E2E8F0',
            borderRadius: '16px',
            overflow: 'hidden',
            marginBottom: '30px'
          }}>
            {/* Card Header */}
            <div style={{
              backgroundColor: '#1E3A8A',
              padding: '20px 30px',
              borderBottom: '4px solid #FF6B35'
            }}>
              <h3 style={{
                color: '#FFFFFF',
                fontSize: '20px',
                fontWeight: '700',
                margin: '0'
              }}>
                👤 Contact Information
              </h3>
            </div>

            {/* Card Content */}
            <div style={{
              padding: '30px'
            }}>
              <div style={{
                display: 'grid',
                gap: '20px'
              }}>
                {/* Name */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '16px',
                  backgroundColor: '#FFFFFF',
                  borderRadius: '12px',
                  border: '2px solid #E2E8F0'
                }}>
                  <div style={{
                    backgroundColor: '#1E3A8A',
                    color: '#FFFFFF',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: '700',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '1px',
                    minWidth: '80px',
                    textAlign: 'center' as const,
                    marginRight: '16px'
                  }}>
                    Name
                  </div>
                  <span style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#2D3748'
                  }}>
                    {name}
                  </span>
                </div>

                {/* Email */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '16px',
                  backgroundColor: '#FFFFFF',
                  borderRadius: '12px',
                  border: '2px solid #E2E8F0'
                }}>
                  <div style={{
                    backgroundColor: '#FF6B35',
                    color: '#FFFFFF',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: '700',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '1px',
                    minWidth: '80px',
                    textAlign: 'center' as const,
                    marginRight: '16px'
                  }}>
                    Email
                  </div>
                  <a href={`mailto:${email}`} style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#FF6B35',
                    textDecoration: 'none'
                  }}>
                    {email}
                  </a>
                </div>

                {phone && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '16px',
                    backgroundColor: '#FFFFFF',
                    borderRadius: '12px',
                    border: '2px solid #E2E8F0'
                  }}>
                    <div style={{
                      backgroundColor: '#1E3A8A',
                      color: '#FFFFFF',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      fontWeight: '700',
                      textTransform: 'uppercase' as const,
                      letterSpacing: '1px',
                      minWidth: '80px',
                      textAlign: 'center' as const,
                      marginRight: '16px'
                    }}>
                      Phone
                    </div>
                    <a href={`tel:${phone}`} style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#FF6B35',
                      textDecoration: 'none'
                    }}>
                      {phone}
                    </a>
                  </div>
                )}

                {company && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '16px',
                    backgroundColor: '#FFFFFF',
                    borderRadius: '12px',
                    border: '2px solid #E2E8F0'
                  }}>
                    <div style={{
                      backgroundColor: '#1E3A8A',
                      color: '#FFFFFF',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      fontWeight: '700',
                      textTransform: 'uppercase' as const,
                      letterSpacing: '1px',
                      minWidth: '80px',
                      textAlign: 'center' as const,
                      marginRight: '16px'
                    }}>
                      Company
                    </div>
                    <span style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#2D3748'
                    }}>
                      {company}
                    </span>
                  </div>
                )}

                {service && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '16px',
                    backgroundColor: '#FFFFFF',
                    borderRadius: '12px',
                    border: '2px solid #E2E8F0'
                  }}>
                    <div style={{
                      backgroundColor: '#FF6B35',
                      color: '#FFFFFF',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      fontWeight: '700',
                      textTransform: 'uppercase' as const,
                      letterSpacing: '1px',
                      minWidth: '80px',
                      textAlign: 'center' as const,
                      marginRight: '16px'
                    }}>
                      Service
                    </div>
                    <span style={{
                      backgroundColor: '#1E3A8A',
                      color: '#FFFFFF',
                      padding: '8px 16px',
                      borderRadius: '20px',
                      fontSize: '14px',
                      fontWeight: '700',
                      textTransform: 'uppercase' as const,
                      letterSpacing: '0.5px'
                    }}>
                      {service}
                    </span>
                  </div>
                )}

                {/* Timestamp */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '16px',
                  backgroundColor: '#FFFFFF',
                  borderRadius: '12px',
                  border: '2px solid #E2E8F0'
                }}>
                  <div style={{
                    backgroundColor: '#718096',
                    color: '#FFFFFF',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: '700',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '1px',
                    minWidth: '80px',
                    textAlign: 'center' as const,
                    marginRight: '16px'
                  }}>
                    Time
                  </div>
                  <span style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#718096'
                  }}>
                    {submittedAt}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Message Section */}
          <div style={{
            backgroundColor: '#F7FAFC',
            border: '2px solid #E2E8F0',
            borderRadius: '16px',
            overflow: 'hidden',
            marginBottom: '30px'
          }}>
            {/* Message Header */}
            <div style={{
              backgroundColor: '#FF6B35',
              padding: '20px 30px',
              borderBottom: '4px solid #1E3A8A'
            }}>
              <h3 style={{
                color: '#FFFFFF',
                fontSize: '20px',
                fontWeight: '700',
                margin: '0'
              }}>
                💬 Client Message
              </h3>
            </div>

            {/* Message Content */}
            <div style={{
              padding: '30px'
            }}>
              <div style={{
                backgroundColor: '#FFFFFF',
                border: '3px solid #E2E8F0',
                borderRadius: '12px',
                padding: '24px',
                fontSize: '16px',
                lineHeight: '1.7',
                color: '#2D3748',
                whiteSpace: 'pre-wrap' as const,
                fontWeight: '500',
                borderLeft: '6px solid #FF6B35'
              }}>
                {message}
              </div>

              {/* Message Analytics */}
              <div style={{
                marginTop: '20px',
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: '12px',
                textAlign: 'center' as const
              }}>
                <div style={{
                  backgroundColor: '#EDF2F7',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '2px solid #CBD5E0'
                }}>
                  <div style={{
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#1E3A8A',
                    marginBottom: '2px'
                  }}>
                    {message.split(' ').length}
                  </div>
                  <div style={{
                    fontSize: '11px',
                    fontWeight: '600',
                    color: '#718096',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '0.5px'
                  }}>
                    Words
                  </div>
                </div>

                <div style={{
                  backgroundColor: '#EDF2F7',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '2px solid #CBD5E0'
                }}>
                  <div style={{
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#FF6B35',
                    marginBottom: '2px'
                  }}>
                    {message.length > 100 ? 'HIGH' : 'MED'}
                  </div>
                  <div style={{
                    fontSize: '11px',
                    fontWeight: '600',
                    color: '#718096',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '0.5px'
                  }}>
                    Detail
                  </div>
                </div>

                <div style={{
                  backgroundColor: '#EDF2F7',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '2px solid #CBD5E0'
                }}>
                  <div style={{
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#38A169',
                    marginBottom: '2px'
                  }}>
                    A+
                  </div>
                  <div style={{
                    fontSize: '11px',
                    fontWeight: '600',
                    color: '#718096',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '0.5px'
                  }}>
                    Quality
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '16px',
            marginBottom: '30px'
          }}>
            <a href={`mailto:${email}`} style={{
              backgroundColor: '#FF6B35',
              color: '#FFFFFF',
              padding: '16px 24px',
              borderRadius: '12px',
              textDecoration: 'none',
              fontWeight: '700',
              fontSize: '16px',
              textAlign: 'center' as const,
              textTransform: 'uppercase' as const,
              letterSpacing: '1px',
              border: '3px solid #FF6B35'
            }}>
              📧 Reply to Client
            </a>

            {phone && (
              <a href={`tel:${phone}`} style={{
                backgroundColor: '#1E3A8A',
                color: '#FFFFFF',
                padding: '16px 24px',
                borderRadius: '12px',
                textDecoration: 'none',
                fontWeight: '700',
                fontSize: '16px',
                textAlign: 'center' as const,
                textTransform: 'uppercase' as const,
                letterSpacing: '1px',
                border: '3px solid #1E3A8A'
              }}>
                📞 Call Now
              </a>
            )}
          </div>

          {/* Response Time Tracker */}
          <div style={{
            backgroundColor: '#F7FAFC',
            border: '2px solid #E2E8F0',
            borderRadius: '16px',
            overflow: 'hidden',
            marginBottom: '30px'
          }}>
            <div style={{
              backgroundColor: '#1E3A8A',
              padding: '16px 30px',
              borderBottom: '4px solid #FF6B35'
            }}>
              <h3 style={{
                color: '#FFFFFF',
                fontSize: '18px',
                fontWeight: '700',
                margin: '0'
              }}>
                ⏰ Response Time Goals
              </h3>
            </div>

            <div style={{
              padding: '24px 30px'
            }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: '16px',
                textAlign: 'center' as const
              }}>
                <div style={{
                  backgroundColor: '#FFFFFF',
                  padding: '16px',
                  borderRadius: '12px',
                  border: '2px solid #38A169'
                }}>
                  <div style={{
                    fontSize: '20px',
                    fontWeight: '800',
                    color: '#38A169',
                    marginBottom: '4px'
                  }}>
                    &lt; 1hr
                  </div>
                  <div style={{
                    fontSize: '12px',
                    fontWeight: '600',
                    color: '#718096',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '1px'
                  }}>
                    Excellent
                  </div>
                </div>

                <div style={{
                  backgroundColor: '#FFFFFF',
                  padding: '16px',
                  borderRadius: '12px',
                  border: '2px solid #FF6B35'
                }}>
                  <div style={{
                    fontSize: '20px',
                    fontWeight: '800',
                    color: '#FF6B35',
                    marginBottom: '4px'
                  }}>
                    &lt; 2hr
                  </div>
                  <div style={{
                    fontSize: '12px',
                    fontWeight: '600',
                    color: '#718096',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '1px'
                  }}>
                    Target
                  </div>
                </div>

                <div style={{
                  backgroundColor: '#FFFFFF',
                  padding: '16px',
                  borderRadius: '12px',
                  border: '2px solid #E53E3E'
                }}>
                  <div style={{
                    fontSize: '20px',
                    fontWeight: '800',
                    color: '#E53E3E',
                    marginBottom: '4px'
                  }}>
                    &gt; 4hr
                  </div>
                  <div style={{
                    fontSize: '12px',
                    fontWeight: '600',
                    color: '#718096',
                    textTransform: 'uppercase' as const,
                    letterSpacing: '1px'
                  }}>
                    Late
                  </div>
                </div>
              </div>

              <div style={{
                marginTop: '20px',
                padding: '16px',
                backgroundColor: '#EDF2F7',
                borderRadius: '12px',
                border: '2px solid #CBD5E0'
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: '#4A5568',
                  textAlign: 'center' as const
                }}>
                  💡 <strong>Pro Tip:</strong> Quick responses increase customer satisfaction by 67%
                  and conversion rates by 391%.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div style={{
          backgroundColor: '#1E3A8A',
          padding: '30px 40px',
          textAlign: 'center' as const
        }}>
          <div style={{
            backgroundColor: '#FFFFFF',
            padding: '20px',
            borderRadius: '12px',
            marginBottom: '20px'
          }}>
            <h4 style={{
              color: '#1E3A8A',
              fontSize: '18px',
              fontWeight: '700',
              margin: '0 0 8px 0'
            }}>
              WebforLeads
            </h4>
            <p style={{
              color: '#FF6B35',
              fontSize: '14px',
              fontWeight: '600',
              margin: '0 0 12px 0',
              textTransform: 'uppercase' as const,
              letterSpacing: '1px'
            }}>
              Premium Digital Solutions
            </p>
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '20px',
              flexWrap: 'wrap' as const
            }}>
              <a href="mailto:<EMAIL>" style={{
                color: '#FF6B35',
                textDecoration: 'none',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                📧 <EMAIL>
              </a>
              <a href="tel:+442079460958" style={{
                color: '#FF6B35',
                textDecoration: 'none',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                📞 +44 20 7946 0958
              </a>
            </div>
          </div>

          <p style={{
            color: '#A0AEC0',
            fontSize: '12px',
            margin: '0',
            fontWeight: '500'
          }}>
            This message was submitted through your WebforLeads contact form.<br />
            Automated inquiry notification system - Do not reply to this email.
          </p>
        </div>

        {/* Bottom accent */}
        <div style={{
          backgroundColor: '#FF6B35',
          height: '6px',
          width: '100%'
        }} />
      </div>
    </div>
  );
}
