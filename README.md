# WebforLeads - Digital Marketing Agency Website

A modern, high-performance website for WebforLeads digital marketing agency built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

- 🚀 **Modern Tech Stack**: Next.js 15, TypeScript, Tailwind CSS
- 📱 **Responsive Design**: Mobile-first approach with premium UI components
- 🎨 **Premium Animations**: Framer Motion for smooth interactions
- 📧 **Email Integration**: Resend API for contact and appointment forms
- 🎯 **SEO Optimized**: Built-in SEO best practices
- ⚡ **Performance**: Optimized for Core Web Vitals
- 🔒 **Type Safe**: Full TypeScript implementation

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Copy environment variables:

```bash
cp .env.example .env.local
```

4. Update the environment variables in `.env.local`:

```env
RESEND_API_KEY=your_resend_api_key_here
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>
BUSINESS_EMAIL=<EMAIL>
```

### Development

Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the website.

### Build

Create a production build:

```bash
npm run build
```

Start the production server:

```bash
npm start
```

## Deployment on Vercel

### Quick Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/webforleads)

### Manual Deployment

1. **Push to GitHub**: Ensure your code is pushed to a GitHub repository

2. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository

3. **Configure Environment Variables**:
   - In Vercel dashboard, go to Project Settings → Environment Variables
   - Add the following variables (make sure to set them for Production, Preview, and Development):
     ```
     RESEND_API_KEY = your_resend_api_key_here
     FROM_EMAIL = <EMAIL>
     TO_EMAIL = <EMAIL>
     BUSINESS_EMAIL = <EMAIL>
     ```
   - **Important**: Don't use quotes around the values in Vercel dashboard

4. **Deploy**: Vercel will automatically build and deploy your site

### Environment Variables Setup

For production deployment, you'll need:

- **RESEND_API_KEY**: Get from [resend.com](https://resend.com)
- **FROM_EMAIL**: The email address to send from
- **TO_EMAIL**: The email address to receive contact forms
- **BUSINESS_EMAIL**: Your business email address

## Project Structure

```
webforleads/
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── api/            # API routes
│   │   ├── about/          # About page
│   │   ├── blog/           # Blog page
│   │   ├── contact/        # Contact page
│   │   └── services/       # Services pages
│   ├── components/         # React components
│   │   ├── forms/          # Form components
│   │   ├── ui/             # UI components
│   │   └── ...
│   └── lib/                # Utility functions
├── public/                 # Static assets
└── ...
```

## Tech Stack

- **Framework**: Next.js 15
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Animations**: Framer Motion
- **Email**: Resend
- **Icons**: Lucide React
- **Deployment**: Vercel

## Performance

This website is optimized for:
- ⚡ Core Web Vitals
- 📱 Mobile performance
- 🔍 SEO best practices
- ♿ Accessibility standards

## Support

For support or questions, contact [<EMAIL>](mailto:<EMAIL>)
