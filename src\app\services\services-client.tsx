"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Globe, Search, MousePointer, Smartphone, Share2, Code,
  ArrowRight, CheckCircle, TrendingUp, Users, Award, Star,
  Target, Zap, BarChart3, Palette, Rocket, Shield, Clock,
  DollarSign, Lightbulb, Settings, Monitor, Megaphone,
  PieChart, HeadphonesIcon, Trophy, Sparkles
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";
import ContactForm from "@/components/forms/contact-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const services = [
  {
    id: "web-design",
    title: "Conversion-Focused Web Design & Development",
    description: "Custom websites built to convert visitors into customers, not just look pretty. Every element is strategically designed around your customer journey.",
    icon: <Globe className="h-8 w-8" />,
    gradient: "from-[#1E3A8A] to-[#1E3A8A]/80",
    features: ["Custom Design & Development", "Mobile-First Responsive Design", "Conversion Rate Optimization", "Advanced Analytics Integration", "SEO-Optimized Structure", "Lightning-Fast Performance"],
    price: "From £2,500",
    timeline: "4-6 weeks",
    href: "/services/web-design"
  },
  {
    id: "seo",
    title: "Authoritative Search Engine Optimisation (SEO)",
    description: "Dominate Google's first page with our data-driven SEO strategies. We follow Google's E-E-A-T guidelines to establish your authority.",
    icon: <Search className="h-8 w-8" />,
    gradient: "from-[#FF6B35] to-[#FF6B35]/80",
    features: ["Technical SEO Audits", "Keyword Research & Strategy", "Content Optimization", "Local SEO Domination", "Link Building Campaigns", "Monthly Performance Reports"],
    price: "From £1,500",
    timeline: "3-6 months",
    href: "/services/seo"
  },
  {
    id: "app-development",
    title: "Mobile App Development",
    description: "Native iOS and Android apps that users love. Built with cutting-edge technology and designed for maximum user engagement.",
    icon: <Smartphone className="h-8 w-8" />,
    gradient: "from-[#1E3A8A] to-[#1E3A8A]/80",
    features: ["Native iOS & Android Development", "Cross-Platform Solutions", "UI/UX Design", "App Store Optimization", "Backend Development", "Ongoing Maintenance"],
    price: "From £15,000",
    timeline: "8-16 weeks",
    href: "/services/app-development"
  },
  {
    id: "digital-marketing",
    title: "Data-Driven Digital Marketing",
    description: "Comprehensive digital marketing strategies that drive qualified traffic, generate leads, and increase revenue through multiple channels.",
    icon: <Target className="h-8 w-8" />,
    gradient: "from-[#FF6B35] to-[#FF6B35]/80",
    features: ["PPC Campaign Management", "Social Media Marketing", "Content Marketing", "Email Marketing", "Conversion Optimization", "Analytics & Reporting"],
    price: "From £2,000",
    timeline: "Ongoing",
    href: "/services/digital-marketing"
  },
  {
    id: "social-media",
    title: "Social Media Marketing",
    description: "Build communities and drive sales through strategic social media marketing across all major platforms.",
    icon: <Share2 className="h-8 w-8" />,
    gradient: "from-[#1E3A8A] to-[#1E3A8A]/80",
    features: ["Content Creation & Strategy", "Community Management", "Social Media Advertising", "Influencer Partnerships", "Analytics & Reporting", "Brand Building"],
    price: "From £1,500",
    timeline: "Ongoing",
    href: "/services/social-media"
  },
  {
    id: "google-ads",
    title: "Google Ads Management",
    description: "Expert Google Ads management that maximizes ROI, reduces costs, and drives qualified traffic to your business.",
    icon: <MousePointer className="h-8 w-8" />,
    gradient: "from-[#FF6B35] to-[#FF6B35]/80",
    features: ["Campaign Setup & Optimization", "Keyword Research", "Ad Copy Testing", "Landing Page Optimization", "Conversion Tracking", "Performance Reporting"],
    price: "From £1,500",
    timeline: "Ongoing",
    href: "/services/google-ads"
  }
];

const stats = [
  { number: "500+", label: "Projects Completed", icon: <Rocket className="h-6 w-6" /> },
  { number: "98%", label: "Client Satisfaction", icon: <Star className="h-6 w-6" /> },
  { number: "£5M+", label: "Revenue Generated", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "24/7", label: "Support Available", icon: <Shield className="h-6 w-6" /> }
];

const processSteps = [
  {
    step: "01",
    title: "Discovery & Strategy",
    description: "We analyze your business, competitors, and target audience to create a comprehensive digital strategy.",
    icon: <Lightbulb className="h-8 w-8" />,
    duration: "1-2 weeks"
  },
  {
    step: "02",
    title: "Design & Development",
    description: "Our expert team creates stunning designs and develops robust solutions tailored to your needs.",
    icon: <Settings className="h-8 w-8" />,
    duration: "4-8 weeks"
  },
  {
    step: "03",
    title: "Testing & Launch",
    description: "Rigorous testing ensures everything works perfectly before we launch your project to the world.",
    icon: <Rocket className="h-8 w-8" />,
    duration: "1-2 weeks"
  },
  {
    step: "04",
    title: "Growth & Optimization",
    description: "Continuous monitoring and optimization to ensure your digital presence keeps growing.",
    icon: <TrendingUp className="h-8 w-8" />,
    duration: "Ongoing"
  }
];

const whyChooseUs = [
  {
    title: "Proven Track Record",
    description: "Over 500 successful projects with measurable results and satisfied clients.",
    icon: <Trophy className="h-8 w-8" />,
    color: "from-[#1E3A8A] to-[#1E40AF]"
  },
  {
    title: "Expert Team",
    description: "Certified professionals with years of experience in digital marketing and development.",
    icon: <Users className="h-8 w-8" />,
    color: "from-[#FF6B35] to-[#FF8A65]"
  },
  {
    title: "24/7 Support",
    description: "Round-the-clock support to ensure your business never stops growing.",
    icon: <HeadphonesIcon className="h-8 w-8" />,
    color: "from-[#1E3A8A] to-[#1E40AF]"
  },
  {
    title: "ROI Focused",
    description: "Every strategy is designed to maximize your return on investment and drive growth.",
    icon: <DollarSign className="h-8 w-8" />,
    color: "from-[#FF6B35] to-[#FF8A65]"
  }
];

const industries = [
  { name: "E-commerce", icon: <Globe className="h-6 w-6" />, projects: "150+" },
  { name: "Healthcare", icon: <Shield className="h-6 w-6" />, projects: "80+" },
  { name: "Real Estate", icon: <Monitor className="h-6 w-6" />, projects: "120+" },
  { name: "Technology", icon: <Code className="h-6 w-6" />, projects: "90+" },
  { name: "Finance", icon: <BarChart3 className="h-6 w-6" />, projects: "60+" },
  { name: "Education", icon: <Award className="h-6 w-6" />, projects: "70+" }
];

export default function ServicesClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-[#FF6B35]/5 to-[#1E3A8A]/5 text-[#1E3A8A] border border-[#1E3A8A]/20 hover:shadow-lg hover:shadow-[#1E3A8A]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Sparkles className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">COMPREHENSIVE DIGITAL SERVICES</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Digital Services That
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Transform Your Business
                      </span>
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Complete Digital Solutions Including{" "}
                    <span className="text-[#FF6B35] relative">
                      Web Design, Development, SEO, App Development & Digital Marketing
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    From custom websites and mobile apps to SEO optimization and digital marketing campaigns, we provide comprehensive solutions that drive growth and deliver measurable results.
                  </p>
                </motion.div>
              </motion.div>
            </motion.div>

            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section - Separated with spacing */}
      <section className="py-20 bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-blue-200 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#1E3A8A]/10 via-[#FF6B35]/5 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#1E3A8A]/20 rounded-full mb-6">
                <Target className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">OUR EXPERTISE</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Our Digital
                <span className="block text-[#FF6B35]">Services</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Comprehensive digital solutions designed to transform your business and drive sustainable growth through proven strategies and cutting-edge technology.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {services.map((service, index) => (
              <motion.div
                key={service.id}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className={`w-16 h-16 bg-gradient-to-r ${service.gradient} rounded-2xl flex items-center justify-center text-white flex-shrink-0`}>
                        {service.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                        <p className="text-gray-600 leading-relaxed mb-4">{service.description}</p>
                      </div>
                    </div>

                    <div className="space-y-4 mb-6">
                      <div className="grid grid-cols-1 gap-2">
                        {service.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-[#FF6B35] flex-shrink-0" />
                            <span className="text-sm text-gray-600">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <div className="text-2xl font-bold text-gray-900">{service.price}</div>
                        <div className="text-sm text-gray-500">{service.timeline}</div>
                      </div>
                    </div>

                    <Link href={service.href}>
                      <Button className="w-full bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                        Learn More
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </Link>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-32 bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/20 via-white/10 to-[#FF6B35]/20 text-white border border-white/20 rounded-full mb-6">
                <Award className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">WHY CHOOSE US</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
                Why Businesses
                <span className="block text-[#FF6B35]">Trust WebforLeads</span>
              </h2>
              <p className="text-xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
                We combine expertise, innovation, and dedication to deliver exceptional results that exceed expectations.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {whyChooseUs.map((item, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center group"
              >
                <div className={`w-20 h-20 bg-gradient-to-r ${item.color} rounded-3xl flex items-center justify-center mx-auto mb-6 text-white group-hover:scale-110 transition-transform duration-300 shadow-2xl`}>
                  {item.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{item.title}</h3>
                <p className="text-blue-200 leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Our Process Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#1E3A8A]/10 via-[#FF6B35]/5 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#1E3A8A]/20 rounded-full mb-6">
                <Settings className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">OUR PROCESS</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                How We
                <span className="block text-[#FF6B35]">Work Together</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our proven 4-step process ensures your project is delivered on time, on budget, and exceeds your expectations.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="relative group"
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group-hover:shadow-2xl group-hover:shadow-gray-900/10 transition-all duration-500 p-8">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center mx-auto mb-6 text-white group-hover:scale-110 transition-transform duration-300">
                      {step.icon}
                    </div>

                    <div className="text-4xl font-black text-[#FF6B35] mb-4">{step.step}</div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                    <p className="text-gray-600 leading-relaxed mb-4">{step.description}</p>

                    <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#FF6B35]/10 to-[#1E3A8A]/10 rounded-full">
                      <Clock className="h-4 w-4 mr-2 text-[#FF6B35]" />
                      <span className="text-sm font-medium text-[#1E3A8A]">{step.duration}</span>
                    </div>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Industries We Serve */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#1E3A8A]/10 via-[#FF6B35]/5 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#1E3A8A]/20 rounded-full mb-6">
                <PieChart className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">INDUSTRIES</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Industries We
                <span className="block text-[#FF6B35]">Serve</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We have extensive experience working with businesses across various industries, delivering tailored solutions that meet specific sector needs.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
          >
            {industries.map((industry, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="group"
              >
                <Card className="relative border-0 bg-white shadow-lg shadow-gray-900/5 rounded-2xl overflow-hidden group-hover:shadow-xl group-hover:shadow-gray-900/10 transition-all duration-500 p-6 text-center">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative">
                    <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                      {industry.icon}
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{industry.name}</h3>
                    <div className="text-sm text-[#FF6B35] font-semibold">{industry.projects} Projects</div>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Ready to Transform
                <span className="block text-orange-600">Your Business?</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Let's discuss your project and create a custom solution that drives growth and delivers results.
              </p>
            </motion.div>
          </motion.div>

          <motion.div variants={fadeInUp}>
            <ContactForm />
          </motion.div>
        </div>
      </section>
    </div>
  );
}
