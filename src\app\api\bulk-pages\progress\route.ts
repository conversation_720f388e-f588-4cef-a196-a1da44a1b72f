import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Interfaces
interface GeneratedPage {
  id: string;
  keyword: string;
  location: string;
  slug: string;
  title: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  createdAt: string;
  filePath?: string;
  error?: string;
  progress?: number;
}

interface BulkGenerationJob {
  id: string;
  name: string;
  keywords: string[];
  locations: string[];
  status: 'pending' | 'running' | 'completed' | 'paused' | 'error';
  progress: number;
  totalPages: number;
  completedPages: number;
  createdAt: string;
  estimatedCompletion?: string;
  pages: GeneratedPage[];
}

// Get the bulk pages storage directory
function getBulkPagesStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'bulk-pages-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Get single bulk job by ID
function getBulkJobById(jobId: string): BulkGenerationJob | null {
  const storageDir = getBulkPagesStorageDir();
  
  try {
    const filename = `job-${jobId}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf-8');
      return JSON.parse(content) as BulkGenerationJob;
    }
    
    return null;
  } catch (error) {
    console.error('Error reading bulk job:', error);
    return null;
  }
}

// Calculate estimated completion time
function calculateEstimatedCompletion(job: BulkGenerationJob): string | undefined {
  if (job.status !== 'running' || job.completedPages === 0) {
    return undefined;
  }

  const startTime = new Date(job.createdAt).getTime();
  const currentTime = Date.now();
  const elapsedTime = currentTime - startTime;
  
  // Calculate average time per page
  const avgTimePerPage = elapsedTime / job.completedPages;
  const remainingPages = job.totalPages - job.completedPages;
  const estimatedRemainingTime = remainingPages * avgTimePerPage;
  
  const estimatedCompletion = new Date(currentTime + estimatedRemainingTime);
  return estimatedCompletion.toISOString();
}

// Get detailed progress information
function getDetailedProgress(job: BulkGenerationJob) {
  const statusCounts = {
    pending: 0,
    generating: 0,
    completed: 0,
    error: 0
  };

  job.pages.forEach(page => {
    statusCounts[page.status]++;
  });

  const recentPages = job.pages
    .filter(page => page.status === 'completed' || page.status === 'error')
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5);

  const errorPages = job.pages.filter(page => page.status === 'error');

  return {
    statusCounts,
    recentPages,
    errorPages: errorPages.slice(0, 10), // Limit to 10 most recent errors
    successRate: job.totalPages > 0 ? Math.round((statusCounts.completed / job.totalPages) * 100) : 0,
    errorRate: job.totalPages > 0 ? Math.round((statusCounts.error / job.totalPages) * 100) : 0
  };
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('jobId');

  if (!jobId) {
    return NextResponse.json(
      { success: false, error: 'Job ID is required' },
      { status: 400 }
    );
  }

  try {
    console.log('📊 Getting progress for job:', jobId);

    const job = getBulkJobById(jobId);
    
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    // Calculate estimated completion
    const estimatedCompletion = calculateEstimatedCompletion(job);
    
    // Get detailed progress
    const detailedProgress = getDetailedProgress(job);

    // Calculate performance metrics
    const startTime = new Date(job.createdAt).getTime();
    const currentTime = Date.now();
    const elapsedTime = currentTime - startTime;
    const elapsedMinutes = Math.round(elapsedTime / (1000 * 60));
    
    const pagesPerMinute = elapsedMinutes > 0 ? Math.round((job.completedPages / elapsedMinutes) * 10) / 10 : 0;

    console.log('📈 Progress Summary:');
    console.log('   - Status:', job.status);
    console.log('   - Progress:', job.progress + '%');
    console.log('   - Completed:', job.completedPages + '/' + job.totalPages);
    console.log('   - Success Rate:', detailedProgress.successRate + '%');
    console.log('   - Error Rate:', detailedProgress.errorRate + '%');
    console.log('   - Pages/min:', pagesPerMinute);

    return NextResponse.json({
      success: true,
      jobId: job.id,
      status: job.status,
      progress: job.progress,
      totalPages: job.totalPages,
      completedPages: job.completedPages,
      estimatedCompletion,
      elapsedTime: elapsedMinutes,
      pagesPerMinute,
      detailedProgress,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error getting job progress:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get job progress',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Get detailed job information including all pages
export async function POST(request: NextRequest) {
  try {
    const { jobId, includePages = false } = await request.json();

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const job = getBulkJobById(jobId);
    
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    const detailedProgress = getDetailedProgress(job);
    const estimatedCompletion = calculateEstimatedCompletion(job);

    const response = {
      success: true,
      job: {
        id: job.id,
        name: job.name,
        status: job.status,
        progress: job.progress,
        totalPages: job.totalPages,
        completedPages: job.completedPages,
        createdAt: job.createdAt,
        estimatedCompletion,
        keywords: job.keywords,
        locations: job.locations
      },
      detailedProgress
    };

    if (includePages) {
      (response.job as { pages?: unknown }).pages = job.pages;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error getting detailed job info:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get job information',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
