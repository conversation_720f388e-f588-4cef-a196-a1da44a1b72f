# Blog Automation Setup

This document explains how to set up automatic blog post generation that runs 3 times daily.

## Overview

The system automatically generates high-quality blog posts using Gemini's text and image generation capabilities. Posts are generated at:
- 8:00 AM
- 2:00 PM  
- 8:00 PM

## Setup Instructions

### 1. Environment Variables

Add these environment variables to your Vercel project or `.env.local`:

```bash
# Blog Generation Cron Job Secret (generate a secure random string)
CRON_SECRET=your-secure-random-secret-key-here

# Base URL for your application
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# Gemini API Key (already configured)
GEMINI_API_KEY=your-gemini-api-key-here
```

### 2. Vercel Cron Jobs (Recommended)

The `vercel.json` file is already configured with the cron schedule. When you deploy to Vercel, the cron jobs will automatically start running.

**Cron Schedule:** `0 8,14,20 * * *`
- Runs at 8:00 AM, 2:00 PM, and 8:00 PM UTC daily
- Adjust timezone as needed for your target audience

### 3. Alternative: External Cron Service

If not using Vercel, you can use external services like:

#### GitHub Actions
Create `.github/workflows/blog-cron.yml`:

```yaml
name: Generate Blog Posts
on:
  schedule:
    - cron: '0 8,14,20 * * *'
  workflow_dispatch:

jobs:
  generate-blog:
    runs-on: ubuntu-latest
    steps:
      - name: Generate Blog Post
        run: |
          curl -X POST "https://your-domain.com/api/cron/generate-blog" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}"
```

#### Cron-job.org
1. Sign up at https://cron-job.org
2. Create a new cron job with URL: `https://your-domain.com/api/cron/generate-blog`
3. Add header: `Authorization: Bearer your-cron-secret`
4. Set schedule: `0 8,14,20 * * *`

### 4. Manual Testing

Test the cron endpoint manually:

```bash
curl -X POST "http://localhost:3000/api/cron/generate-blog" \
  -H "Authorization: Bearer your-cron-secret"
```

## Features

### Smart Generation
- **Duplicate Prevention**: Won't generate if a post was created in the last 6 hours
- **Error Handling**: Graceful fallback if generation fails
- **Logging**: Detailed logs for monitoring and debugging

### Content Quality
- **SEO Optimized**: All posts include proper meta descriptions and structure
- **Professional Images**: Gemini-generated images with brand colors
- **Fallback Images**: Professional SVG placeholders if image generation fails
- **Consistent Branding**: Navy blue (#1E3A8A) and orange (#FF6B35) color scheme

### Blog Management
- **Admin Interface**: `/admin/blog` for manual generation and management
- **Public Blog**: `/blog` for displaying all posts
- **Individual Posts**: `/blog/[slug]` for full post pages

## Monitoring

### Logs
Check your deployment logs to monitor cron job execution:
- Vercel: Functions tab in your project dashboard
- Other platforms: Check your platform's logging system

### Success Indicators
- ✅ `CRON JOB: Blog post generated successfully`
- ✅ New posts appear in `/admin/blog`
- ✅ Posts are visible on `/blog`

### Common Issues
- ❌ `Unauthorized`: Check CRON_SECRET environment variable
- ❌ `Recent post exists`: Normal behavior, prevents duplicate posts
- ❌ `Failed to generate`: Check Gemini API key and quotas

## Customization

### Adjust Frequency
Edit `vercel.json` cron schedule:
- `0 */6 * * *` - Every 6 hours
- `0 9,15,21 * * *` - 9 AM, 3 PM, 9 PM
- `0 12 * * *` - Daily at noon

### Content Topics
The system automatically generates diverse topics. To customize:
1. Edit the prompt in `/api/blog/generate/route.ts`
2. Modify the `generateBlogIdeas()` function
3. Add specific industry keywords or topics

## Security

- **Secret Protection**: Never commit CRON_SECRET to version control
- **Authorization**: All cron requests require valid Bearer token
- **Rate Limiting**: Built-in duplicate prevention (6-hour cooldown)

## Support

For issues or questions:
1. Check the logs in your deployment platform
2. Test the endpoint manually
3. Verify environment variables are set correctly
4. Ensure Gemini API key has sufficient quota
