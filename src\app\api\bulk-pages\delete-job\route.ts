import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { unregisterRoute } from '@/lib/route-registry';

// Get the bulk pages storage directory
function getBulkPagesStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'bulk-pages-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Get single bulk job by ID
function getBulkJobById(jobId: string) {
  const storageDir = getBulkPagesStorageDir();
  
  try {
    const filename = `job-${jobId}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf-8');
      return JSON.parse(content);
    }
    
    return null;
  } catch (error) {
    console.error('Error reading bulk job:', error);
    return null;
  }
}

// Delete generated page files and unregister routes
function deleteGeneratedPages(job: { pages?: Array<{ filePath?: string; slug?: string; status?: string }> }): { deletedFiles: number; unregisteredRoutes: number; errors: string[] } {
  let deletedFiles = 0;
  let unregisteredRoutes = 0;
  const errors: string[] = [];

  if (!job.pages || !Array.isArray(job.pages)) {
    return { deletedFiles, unregisteredRoutes, errors };
  }

  for (const page of job.pages) {
    if (page.filePath && page.status === 'completed') {
      try {
        // Delete the page file
        if (fs.existsSync(page.filePath)) {
          fs.unlinkSync(page.filePath);
          deletedFiles++;
          console.log('🗑️ Deleted page file:', page.filePath);
        }

        // Unregister route
        if (page.slug) {
          const routeDeleted = unregisterRoute(page.slug);
          if (routeDeleted) {
            unregisteredRoutes++;
            console.log('🗑️ Unregistered route:', page.slug);
          }
        }

        // Try to delete the directory if it's empty
        const pageDir = path.dirname(page.filePath);
        try {
          const files = fs.readdirSync(pageDir);
          if (files.length === 0) {
            fs.rmdirSync(pageDir);
            console.log('🗑️ Deleted empty directory:', pageDir);
          }
        } catch {
          // Directory not empty or other error, ignore
        }

      } catch (error) {
        const errorMsg = `Failed to delete ${page.filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error('❌', errorMsg);
      }
    }
  }

  return { deletedFiles, unregisteredRoutes, errors };
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('\n🗑️ DELETING BULK GENERATION JOB');
    
    const { jobId, deleteFiles = true } = await request.json();
    
    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    console.log('🆔 Job ID:', jobId);
    console.log('🗑️ Delete Files:', deleteFiles);

    // Get job details before deletion
    const job = getBulkJobById(jobId);
    
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('📋 Job Details:');
    console.log('   - Name:', job.name);
    console.log('   - Status:', job.status);
    console.log('   - Total Pages:', job.totalPages);
    console.log('   - Completed Pages:', job.completedPages);

    let deletionResults = { deletedFiles: 0, unregisteredRoutes: 0, errors: [] as string[] };

    // Delete generated page files if requested
    if (deleteFiles) {
      console.log('🗑️ Deleting generated page files and routes...');
      deletionResults = deleteGeneratedPages(job);
      console.log('✅ Deleted', deletionResults.deletedFiles, 'page files');
      console.log('✅ Unregistered', deletionResults.unregisteredRoutes, 'routes');

      if (deletionResults.errors.length > 0) {
        console.log('⚠️ Errors during deletion:', deletionResults.errors.length);
      }
    }

    // Delete job file
    const storageDir = getBulkPagesStorageDir();
    const filename = `job-${jobId}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath);
      console.log('🗑️ Deleted job file:', filepath);
    }

    console.log('✅ BULK JOB DELETION COMPLETED');

    return NextResponse.json({
      success: true,
      message: 'Bulk job deleted successfully',
      deletionResults: {
        jobDeleted: true,
        filesDeleted: deleteFiles,
        deletedFiles: deletionResults.deletedFiles,
        unregisteredRoutes: deletionResults.unregisteredRoutes,
        errors: deletionResults.errors
      }
    });

  } catch (error) {
    console.error('💥 DELETE JOB ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bulk job deletion API endpoint. Use DELETE to remove a job and optionally its generated files.',
    endpoints: {
      'DELETE /api/bulk-pages/delete-job': 'Delete a bulk generation job and optionally its generated files'
    },
    parameters: {
      jobId: 'Required - ID of the job to delete',
      deleteFiles: 'Optional - Whether to delete generated page files (default: true)'
    }
  });
}
