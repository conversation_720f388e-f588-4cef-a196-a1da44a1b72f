"use client";

import React from "react";
import { motion } from "framer-motion";

interface City {
  id: string;
  name: string;
  x: number;
  y: number;
}

interface UKMapProps {
  selectedCity: string | null;
  onCitySelect: (cityId: string) => void;
  cities: City[];
}

export default function UKMap({ selectedCity, onCitySelect, cities }: UKMapProps) {
  const mapVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    show: { opacity: 1, scale: 1 }
  };

  const pinVariants = {
    hidden: { opacity: 0, scale: 0 },
    show: { opacity: 1, scale: 1 }
  };

  return (
    <motion.div
      initial="hidden"
      whileInView="show"
      viewport={{ once: true, amount: 0.3 }}
      variants={mapVariants}
      className="relative w-full max-w-md mx-auto"
    >
      <svg
        viewBox="0 0 400 500"
        className="w-full h-auto"
        aria-label="Interactive map of the United Kingdom"
      >
        {/* Simplified UK outline */}
        <path
          d="M200 50 L220 60 L240 80 L250 100 L260 120 L270 140 L280 160 L290 180 L300 200 L310 220 L320 240 L330 260 L340 280 L350 300 L360 320 L350 340 L340 360 L330 380 L320 400 L310 420 L300 440 L280 450 L260 460 L240 470 L220 475 L200 480 L180 475 L160 470 L140 460 L120 450 L100 440 L90 420 L80 400 L70 380 L60 360 L50 340 L40 320 L50 300 L60 280 L70 260 L80 240 L90 220 L100 200 L110 180 L120 160 L130 140 L140 120 L150 100 L160 80 L180 60 Z"
          fill="#1E3A8A"
          fillOpacity="0.1"
          stroke="#1E3A8A"
          strokeWidth="2"
          strokeOpacity="0.3"
        />
        
        {/* Scotland outline */}
        <path
          d="M180 50 L200 45 L220 50 L240 60 L250 80 L240 100 L230 120 L220 140 L210 160 L200 180 L190 160 L180 140 L170 120 L160 100 L150 80 L160 60 Z"
          fill="#1E3A8A"
          fillOpacity="0.05"
          stroke="#1E3A8A"
          strokeWidth="1"
          strokeOpacity="0.2"
        />

        {/* City pins */}
        {cities.map((city, index) => (
          <motion.g
            key={city.id}
            variants={pinVariants}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            transition={{ delay: index * 0.05 }}
          >
            <motion.circle
              cx={city.x}
              cy={city.y}
              r={selectedCity === city.id ? "8" : "6"}
              fill={selectedCity === city.id ? "#FF6B35" : "#1E3A8A"}
              stroke="white"
              strokeWidth="2"
              className="cursor-pointer transition-all duration-200"
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onCitySelect(city.id)}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  onCitySelect(city.id);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`Focus city: ${city.name}`}
            />
            
            {/* Pulsing effect for selected city */}
            {selectedCity === city.id && (
              <motion.circle
                cx={city.x}
                cy={city.y}
                r="12"
                fill="#FF6B35"
                fillOpacity="0.3"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.3, 0, 0.3]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            )}
            
            {/* City label on hover */}
            <motion.text
              x={city.x}
              y={city.y - 15}
              textAnchor="middle"
              className="text-xs font-medium fill-[#111827] pointer-events-none"
              initial={{ opacity: 0 }}
              whileHover={{ opacity: 1 }}
            >
              {city.name}
            </motion.text>
          </motion.g>
        ))}
      </svg>
    </motion.div>
  );
}
