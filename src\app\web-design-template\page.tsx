import { Metadata } from 'next';
import ServiceTemplate from '../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3
} from "lucide-react";

// Web Design Service Configuration
const webDesignConfig = {
  serviceName: "Web Design",
  serviceSlug: "web-design",
  
  metaTitle: "Professional Web Design Services | Custom Website Design Company | Responsive Web Design",
  metaDescription: "Leading web design company creating stunning, responsive websites that convert visitors into customers. Custom web design services including UI/UX design, mobile-first development, and conversion optimization.",
  keywords: [
    "web design", 
    "website design", 
    "custom web design", 
    "responsive web design", 
    "UI/UX design",
    "professional web design",
    "website development",
    "mobile-first design",
    "conversion optimization"
  ],
  
  heroTitle: "Professional Web Design Services That Convert",
  heroSubtitle: "Custom Websites, Mobile-First Design & Conversion Optimization",
  heroDescription: "Transform your business with stunning, high-performing websites that convert visitors into customers. Our expert team creates custom designs that reflect your brand and drive results.",
  heroBadgeText: "AWARD-WINNING WEB DESIGN",

  stats: [
    { number: "500%", label: "Average Conversion Increase", icon: <TrendingUp className="h-6 w-6" /> },
    { number: "£1.8M+", label: "Revenue Generated", icon: <Target className="h-6 w-6" /> },
    { number: "96%", label: "Client Satisfaction Rate", icon: <Users className="h-6 w-6" /> },
    { number: "24hrs", label: "Design Turnaround", icon: <Rocket className="h-6 w-6" /> }
  ],

  designFeatures: [
    {
      icon: <Brain className="h-8 w-8" />,
      title: "Conversion Psychology Design",
      description: "Every element is strategically placed using proven psychological principles to maximize user engagement and conversions.",
      highlight: "Psychology-Driven"
    },
    {
      icon: <Gauge className="h-8 w-8" />,
      title: "Lightning-Fast Performance",
      description: "Sub-2-second load times guaranteed with advanced optimization techniques and premium hosting infrastructure.",
      highlight: "Speed Optimized"
    },
    {
      icon: <Smartphone className="h-8 w-8" />,
      title: "Mobile-First Excellence",
      description: "Designed for mobile perfection first, then scaled up to create seamless experiences across all devices.",
      highlight: "Mobile-First"
    },
    {
      icon: <Search className="h-8 w-8" />,
      title: "SEO-Engineered Architecture",
      description: "Built with advanced SEO architecture to dominate search rankings from day one of launch.",
      highlight: "SEO-Ready"
    }
  ],

  features: [
    {
      icon: <Palette className="h-6 w-6" />,
      title: "Custom Design & Branding",
      description: "Unique designs that reflect your brand identity and stand out from template-based competitors."
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: "Mobile-First Development",
      description: "Responsive designs that work flawlessly across all devices, ensuring optimal user experience."
    },
    {
      icon: <TrendingUp className="h-6 w-6" />,
      title: "Conversion Optimization",
      description: "Every element strategically placed to guide visitors toward taking action and becoming customers."
    },
    {
      icon: <Rocket className="h-6 w-6" />,
      title: "Lightning-Fast Performance",
      description: "Optimized for speed with advanced caching, compression, and modern development practices."
    },
    {
      icon: <Search className="h-6 w-6" />,
      title: "SEO-Optimized Structure",
      description: "Built with clean code and SEO best practices to help you rank higher in search results."
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Advanced Analytics",
      description: "Comprehensive tracking setup to monitor performance and user behavior from day one."
    }
  ],
  
  packages: [
    {
      name: "Conversion Catalyst",
      price: "£3,500",
      period: "",
      description: "Perfect for businesses ready to transform their online presence with a professional website",
      features: [
        "Custom conversion-focused design",
        "Mobile-first responsive development",
        "Advanced SEO optimization",
        "Performance optimization (90+ PageSpeed)",
        "Contact forms & lead capture",
        "2 weeks delivery timeline",
        "30-day support included",
        "SSL certificate & security setup"
      ],
      highlight: false,
      cta: "Transform My Website"
    },
    {
      name: "Growth Accelerator",
      price: "£6,500",
      period: "",
      description: "Complete solution for ambitious businesses seeking market dominance and rapid growth",
      features: [
        "Everything in Conversion Catalyst",
        "Advanced analytics & tracking setup",
        "A/B testing implementation",
        "Content management system (CMS)",
        "E-commerce functionality",
        "Blog & content sections",
        "Social media integration",
        "3 months support included"
      ],
      highlight: true,
      cta: "Accelerate My Growth"
    },
    {
      name: "Enterprise Dominator",
      price: "£12,500",
      period: "",
      description: "Premium solution for enterprises requiring maximum impact and advanced functionality",
      features: [
        "Everything in Growth Accelerator",
        "Custom integrations & APIs",
        "Advanced security features",
        "Multi-language support",
        "Custom admin dashboard",
        "Advanced user management",
        "Priority support & maintenance",
        "6 months support included"
      ],
      highlight: false,
      cta: "Dominate My Market"
    }
  ],

  process: [
    {
      step: "01",
      title: "Discovery & Strategy",
      description: "We dive deep into your business goals, target audience, and competitive landscape to create a strategic foundation.",
      duration: "Week 1"
    },
    {
      step: "02",
      title: "Design & Prototyping",
      description: "Custom designs and interactive prototypes that bring your vision to life before development begins.",
      duration: "Week 2-3"
    },
    {
      step: "03",
      title: "Development & Testing",
      description: "Clean, modern code development with rigorous testing across all devices and browsers.",
      duration: "Week 4-5"
    },
    {
      step: "04",
      title: "Launch & Optimization",
      description: "Smooth launch with ongoing monitoring and optimization to ensure peak performance.",
      duration: "Week 6+"
    }
  ],

  featuredTestimonial: {
    quote: "WebforLeads transformed our online presence completely. Our new website increased conversions by 340% and perfectly captures our brand essence. The team's attention to detail and strategic approach exceeded all expectations.",
    name: "Sarah Mitchell",
    role: "CEO & Founder",
    company: "TechVision Solutions",
    rating: 5,
    result: "340%",
    resultLabel: "Conversion Increase"
  },

  testimonials: [
    {
      quote: "The new website increased our conversion rate from 2.1% to 8.7% within the first month. The user experience is flawless and the design perfectly captures our brand.",
      name: "Marcus Chen",
      role: "Head of Growth",
      company: "FinanceForward",
      industry: "FinTech",
      metric: "315%",
      metricLabel: "Conversion Boost",
      avatar: "MC"
    },
    {
      quote: "Our online sales doubled in just 6 weeks after launch. The mobile experience is incredible and our customers love the new checkout process.",
      name: "Emma Rodriguez",
      role: "E-commerce Director",
      company: "StyleHub",
      industry: "Fashion",
      metric: "200%",
      metricLabel: "Sales Increase",
      avatar: "ER"
    },
    {
      quote: "The SEO improvements were immediate. We're now ranking #1 for our main keywords and organic traffic has increased by 400%.",
      name: "David Park",
      role: "Marketing Manager",
      company: "GreenTech Solutions",
      industry: "Technology",
      metric: "400%",
      metricLabel: "Traffic Growth",
      avatar: "DP"
    }
  ],

  faqCategories: [
    {
      category: "Process & Timeline",
      icon: <Rocket className="h-6 w-6" />,
      questions: [
        {
          q: "How long does a typical web design project take?",
          a: "Most projects are completed in 4-6 weeks, depending on complexity and scope. Simple brochure sites can be done in 2-3 weeks, while complex e-commerce or custom applications may take 8-12 weeks. We provide detailed timelines during our initial consultation."
        },
        {
          q: "What information do you need to get started?",
          a: "We'll need your brand guidelines, existing content, high-quality images, and a clear understanding of your business goals and target audience. Don't worry if you don't have everything ready - we can help you gather and create what's needed."
        },
        {
          q: "Can I see the design before development starts?",
          a: "Absolutely! We create detailed mockups and interactive prototypes so you can see exactly how your website will look and function before we begin development. This ensures you're completely satisfied with the design."
        }
      ]
    },
    {
      category: "Services & Features",
      icon: <Palette className="h-6 w-6" />,
      questions: [
        {
          q: "Do you provide content writing and SEO services?",
          a: "Yes! We offer comprehensive content strategy, copywriting, and technical SEO optimization. Our team includes experienced copywriters and SEO specialists who ensure your site ranks well and converts visitors into customers."
        },
        {
          q: "Will my website be mobile-friendly and responsive?",
          a: "Absolutely. All our websites are built with a mobile-first approach, ensuring perfect functionality across all devices. We test on multiple screen sizes and optimize for Core Web Vitals to guarantee excellent user experience."
        },
        {
          q: "Do you provide ongoing maintenance and support?",
          a: "Yes, we offer comprehensive maintenance packages including regular updates, security monitoring, performance optimization, and technical support. All our packages include initial support periods with options to extend."
        }
      ]
    },
    {
      category: "Technical & Pricing",
      icon: <BarChart3 className="h-6 w-6" />,
      questions: [
        {
          q: "What platform do you build websites on?",
          a: "We use modern technologies like React, Next.js, and WordPress depending on your needs. We choose the best platform based on your specific requirements, scalability needs, and technical preferences."
        },
        {
          q: "Do you offer payment plans for larger projects?",
          a: "Yes, we offer flexible payment plans for projects over £5,000. Typically, we require 50% upfront and the remainder upon completion, but we can customize payment schedules to fit your budget."
        },
        {
          q: "Can I update the website content myself?",
          a: "Absolutely. We build user-friendly content management systems that allow you to easily update text, images, and other content without technical knowledge. We also provide training and documentation."
        }
      ]
    }
  ],

  finalCTA: {
    badge: "Get Started",
    title: "Let's Plan Your Next Big Win",
    description: "Share your goals and we'll propose a high-impact roadmap with clear timelines and pricing."
  },

  structuredData: {
    serviceName: "Professional Web Design Services",
    description: "Custom web design and development services creating responsive, conversion-focused websites for businesses of all sizes.",
    priceRange: "£3000-£15000",
    areaServed: "United Kingdom",
    aggregateRating: {
      ratingValue: "4.9",
      reviewCount: "127"
    }
  }
};

export const metadata: Metadata = {
  title: webDesignConfig.metaTitle,
  description: webDesignConfig.metaDescription,
  keywords: webDesignConfig.keywords.join(", "),
  openGraph: {
    title: webDesignConfig.metaTitle,
    description: webDesignConfig.metaDescription,
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: webDesignConfig.metaTitle,
    description: webDesignConfig.metaDescription,
  },
};

export default function WebDesignTemplatePage() {
  return <ServiceTemplate config={webDesignConfig} />;
}
