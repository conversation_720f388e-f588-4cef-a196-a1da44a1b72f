import { Metadata } from 'next';
import ServiceTemplate from '../site-pages/service-template';
import {
  Smartphone, Tablet, Monitor, Code, Zap, Users,
  Target, Rocket, Brain, Gauge, BarChart3, Globe
} from "lucide-react";

// App Development Services Configuration
const appDevelopmentConfig = {
  serviceName: "App Development",
  serviceSlug: "app-development",
  
  metaTitle: "Professional App Development | Mobile & Web App Development Services | iOS & Android Apps",
  metaDescription: "Expert app development services for iOS, Android, and web applications. Custom mobile app development with modern technologies and user-centric design.",
  keywords: [
    "app development", 
    "mobile app development", 
    "iOS app development",
    "Android app development",
    "web app development",
    "custom app development",
    "mobile application",
    "app design",
    "cross-platform apps",
    "native app development"
  ],
  
  heroTitle: "Professional App Development That Delivers",
  heroSubtitle: "Mobile & Web Application Development",
  heroDescription: "Transform your ideas into powerful mobile and web applications. Our expert developers create user-friendly, scalable apps that drive engagement and business growth.",
  heroBadgeText: "CERTIFIED APP DEVELOPERS",
  
  stats: [
    { number: "500+", label: "Apps Developed", icon: <Smartphone className="h-6 w-6" /> },
    { number: "2M+", label: "App Downloads", icon: <Users className="h-6 w-6" /> },
    { number: "4.8★", label: "Average App Rating", icon: <Target className="h-6 w-6" /> },
    { number: "12 Weeks", label: "Average Delivery", icon: <Rocket className="h-6 w-6" /> }
  ],
  
  designFeatures: [
    {
      icon: <Brain className="h-8 w-8" />,
      title: "Strategic App Planning",
      description: "Comprehensive app strategy including market research, user journey mapping, and feature prioritization for maximum impact.",
      highlight: "Strategy-First"
    },
    {
      icon: <Code className="h-8 w-8" />,
      title: "Modern Development Stack",
      description: "Cutting-edge technologies including React Native, Flutter, Swift, Kotlin, and progressive web app frameworks.",
      highlight: "Tech-Advanced"
    },
    {
      icon: <Gauge className="h-8 w-8" />,
      title: "Performance Optimization",
      description: "Optimized for speed, efficiency, and user experience with advanced caching, lazy loading, and performance monitoring.",
      highlight: "Performance-Driven"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "User-Centric Design",
      description: "Intuitive UI/UX design focused on user engagement, accessibility, and conversion optimization.",
      highlight: "User-Focused"
    }
  ],
  
  features: [
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: "Native iOS Development",
      description: "High-performance native iOS apps using Swift and Objective-C for optimal user experience and App Store success."
    },
    {
      icon: <Tablet className="h-6 w-6" />,
      title: "Native Android Development",
      description: "Feature-rich Android applications using Kotlin and Java, optimized for Google Play Store distribution."
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: "Cross-Platform Development",
      description: "Cost-effective cross-platform apps using React Native and Flutter for simultaneous iOS and Android deployment."
    },
    {
      icon: <Monitor className="h-6 w-6" />,
      title: "Progressive Web Apps",
      description: "Modern web applications that work like native apps with offline functionality and push notifications."
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Analytics Integration",
      description: "Comprehensive analytics setup to track user behavior, app performance, and business metrics."
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "API Development & Integration",
      description: "Custom API development and third-party service integrations for enhanced app functionality."
    }
  ],
  
  packages: [
    {
      name: "App Starter",
      price: "£15,000",
      period: "project",
      description: "Perfect for simple mobile apps and MVPs",
      features: [
        "Single platform (iOS or Android)",
        "Up to 8 core features",
        "Basic UI/UX design",
        "User authentication",
        "Basic analytics integration",
        "App store submission",
        "3 months post-launch support"
      ],
      highlight: false,
      cta: "Start App Project"
    },
    {
      name: "App Professional",
      price: "£35,000",
      period: "project",
      description: "Comprehensive app solution for growing businesses",
      features: [
        "Cross-platform (iOS & Android)",
        "Up to 15 advanced features",
        "Custom UI/UX design",
        "Advanced user management",
        "Payment gateway integration",
        "Push notifications",
        "Admin dashboard",
        "6 months post-launch support"
      ],
      highlight: true,
      cta: "Build Professional App"
    },
    {
      name: "App Enterprise",
      price: "£75,000",
      period: "project",
      description: "Premium app solution for large businesses and complex requirements",
      features: [
        "Multi-platform (iOS, Android, Web)",
        "Unlimited features & complexity",
        "Premium UI/UX with animations",
        "Advanced security & encryption",
        "Custom backend development",
        "Real-time features",
        "Dedicated project manager",
        "12 months post-launch support"
      ],
      highlight: false,
      cta: "Create Enterprise App"
    }
  ],
  
  process: [
    {
      step: "01",
      title: "Discovery & Planning",
      description: "Comprehensive requirements gathering, market research, and technical architecture planning for your app project.",
      duration: "Week 1-2"
    },
    {
      step: "02",
      title: "Design & Prototyping",
      description: "UI/UX design creation, interactive prototypes, and user testing to ensure optimal user experience.",
      duration: "Week 3-5"
    },
    {
      step: "03",
      title: "Development & Testing",
      description: "Agile development process with regular testing, quality assurance, and progress updates.",
      duration: "Week 6-12"
    },
    {
      step: "04",
      title: "Launch & Support",
      description: "App store submission, launch support, and ongoing maintenance to ensure continued success.",
      duration: "Week 13+"
    }
  ],
  
  featuredTestimonial: {
    quote: "WebforLeads developed our e-commerce app that now has over 100K downloads and generates £2M+ in annual revenue. The user experience is exceptional and the app performance is flawless.",
    name: "Rachel Thompson",
    role: "CEO",
    company: "RetailTech Solutions",
    rating: 5,
    result: "100K+",
    resultLabel: "App Downloads"
  },
  
  testimonials: [
    {
      quote: "Our fitness app reached #3 in the App Store health category within 2 months of launch. The development quality and user interface are outstanding.",
      name: "Mike Stevens",
      role: "Founder",
      company: "FitTrack Pro",
      industry: "Health & Fitness",
      metric: "#3",
      metricLabel: "App Store Ranking",
      avatar: "MS"
    },
    {
      quote: "The cross-platform app saved us 60% in development costs while maintaining native performance. User retention rate is 85% after 30 days.",
      name: "Sarah Kim",
      role: "Product Manager",
      company: "EduLearn",
      industry: "Education",
      metric: "85%",
      metricLabel: "User Retention",
      avatar: "SK"
    },
    {
      quote: "From concept to App Store in 10 weeks. The project management and communication throughout the development process was exceptional.",
      name: "David Wilson",
      role: "CTO",
      company: "StartupFlow",
      industry: "Technology",
      metric: "10 Weeks",
      metricLabel: "Time to Market",
      avatar: "DW"
    }
  ],
  
  faqCategories: [
    {
      category: "Development Process",
      icon: <Code className="h-6 w-6" />,
      questions: [
        {
          q: "How long does it take to develop a mobile app?",
          a: "Development time varies based on complexity. Simple apps take 8-12 weeks, while complex enterprise apps can take 16-24 weeks. We provide detailed timelines during the planning phase."
        },
        {
          q: "Should I build for iOS, Android, or both platforms?",
          a: "We analyze your target audience and business goals to recommend the best approach. Cross-platform development can be cost-effective, while native development offers optimal performance."
        },
        {
          q: "What technologies do you use for app development?",
          a: "We use modern technologies including React Native, Flutter, Swift, Kotlin, and progressive web app frameworks, chosen based on your specific requirements and goals."
        }
      ]
    },
    {
      category: "Design & Features",
      icon: <Smartphone className="h-6 w-6" />,
      questions: [
        {
          q: "Do you provide UI/UX design services?",
          a: "Yes, we provide comprehensive UI/UX design including user research, wireframing, prototyping, and visual design to ensure your app delivers an exceptional user experience."
        },
        {
          q: "Can you integrate with existing systems and APIs?",
          a: "Absolutely. We specialize in integrating apps with existing business systems, third-party APIs, payment gateways, and cloud services to enhance functionality."
        },
        {
          q: "What about app maintenance and updates?",
          a: "We provide ongoing maintenance, bug fixes, feature updates, and OS compatibility updates. All packages include post-launch support with extended maintenance options available."
        }
      ]
    },
    {
      category: "Launch & Success",
      icon: <Rocket className="h-6 w-6" />,
      questions: [
        {
          q: "Do you help with app store submission?",
          a: "Yes, we handle the complete app store submission process including App Store and Google Play Store guidelines compliance, metadata optimization, and launch strategy."
        },
        {
          q: "How do you ensure app security?",
          a: "We implement industry-standard security measures including data encryption, secure authentication, API security, and regular security audits to protect user data."
        },
        {
          q: "What if my app idea already exists?",
          a: "We help you identify unique value propositions and differentiating features to make your app stand out in the market through competitive analysis and strategic positioning."
        }
      ]
    }
  ],
  
  finalCTA: {
    badge: "Get Started",
    title: "Ready to Build Your Dream App?",
    description: "Get a free app consultation and project estimate to turn your idea into a successful mobile application."
  },
  
  structuredData: {
    serviceName: "Professional App Development Services",
    description: "Expert mobile and web application development services including iOS, Android, cross-platform, and progressive web apps with custom design and development.",
    priceRange: "£15000-£75000",
    areaServed: "United Kingdom",
    aggregateRating: {
      ratingValue: "4.9",
      reviewCount: "78"
    }
  }
};

export const metadata: Metadata = {
  title: appDevelopmentConfig.metaTitle,
  description: appDevelopmentConfig.metaDescription,
  keywords: appDevelopmentConfig.keywords.join(", "),
  openGraph: {
    title: appDevelopmentConfig.metaTitle,
    description: appDevelopmentConfig.metaDescription,
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: appDevelopmentConfig.metaTitle,
    description: appDevelopmentConfig.metaDescription,
  },
};

export default function AppDevelopmentPage() {
  return <ServiceTemplate config={appDevelopmentConfig} />;
}
