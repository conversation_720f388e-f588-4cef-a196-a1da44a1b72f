import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    canonicalUrl: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterTitle: string;
    twitterDescription: string;
    twitterImage: string;
    author: string;
    category: string;
    tags: string[];
    readingTime: number;
    wordCount: number;
  };
}

// Get the blog storage directory
function getBlogStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'blog-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// saveBlogPost function moved to @/lib/blog-storage.ts

// Get all blog posts
function getAllBlogPosts(): BlogPost[] {
  const storageDir = getBlogStorageDir();
  
  try {
    const files = fs.readdirSync(storageDir);
    const blogPosts: BlogPost[] = [];

    for (const file of files) {
      if (file.endsWith('.json')) {
        const filepath = path.join(storageDir, file);
        const content = fs.readFileSync(filepath, 'utf-8');
        const blogPost = JSON.parse(content) as BlogPost;
        blogPosts.push(blogPost);
      }
    }

    // Sort by published date (newest first)
    return blogPosts.sort((a, b) => 
      new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    );
  } catch (error) {
    console.error('Error reading blog posts:', error);
    return [];
  }
}

// Get single blog post by slug
function getBlogPostBySlug(slug: string): BlogPost | null {
  const storageDir = getBlogStorageDir();
  
  try {
    const files = fs.readdirSync(storageDir);
    
    for (const file of files) {
      if (file.endsWith('.json') && file.includes(slug)) {
        const filepath = path.join(storageDir, file);
        const content = fs.readFileSync(filepath, 'utf-8');
        return JSON.parse(content) as BlogPost;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error reading blog post:', error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const slug = searchParams.get('slug');

  try {
    if (slug) {
      // Get single blog post
      const blogPost = getBlogPostBySlug(slug);
      
      if (!blogPost) {
        return NextResponse.json(
          { success: false, error: 'Blog post not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        blogPost
      });
    } else {
      // Get all blog posts
      const blogPosts = getAllBlogPosts();
      
      return NextResponse.json({
        success: true,
        blogPosts,
        count: blogPosts.length
      });
    }
  } catch (error) {
    console.error('Error in blog list API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch blog posts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const slug = searchParams.get('slug');

  if (!slug) {
    return NextResponse.json(
      { success: false, error: 'Slug parameter required' },
      { status: 400 }
    );
  }

  try {
    const storageDir = getBlogStorageDir();
    const files = fs.readdirSync(storageDir);
    
    for (const file of files) {
      if (file.endsWith('.json') && file.includes(slug)) {
        const filepath = path.join(storageDir, file);
        fs.unlinkSync(filepath);
        
        return NextResponse.json({
          success: true,
          message: 'Blog post deleted successfully'
        });
      }
    }
    
    return NextResponse.json(
      { success: false, error: 'Blog post not found' },
      { status: 404 }
    );
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete blog post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
