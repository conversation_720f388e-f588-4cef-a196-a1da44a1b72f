"use client";

import { motion } from 'framer-motion';
import { Phone, Mail, MessageSquare, Calendar, CheckCircle, Shield, ArrowRight, Building, MapPin, Coffee } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import AppointmentForm from '@/components/forms/appointment-form';

// Data for contact methods
const contactMethods = [
  {
    icon: <Phone className="h-6 w-6" />,
    title: "Phone",
    description: "Speak directly with our team",
    contact: "020 8123 4567",
    action: "Call Now",
    available: "Mon-Fri, 9AM-6PM"
  },
  {
    icon: <Mail className="h-6 w-6" />,
    title: "Email",
    description: "Send us a detailed message",
    contact: "<EMAIL>",
    action: "Send Email",
    available: "24/7 Response"
  },
  {
    icon: <MessageSquare className="h-6 w-6" />,
    title: "Live Chat",
    description: "Instant support during business hours",
    contact: "Available on website",
    action: "Start Chat",
    available: "Mon-Fri, 9AM-6PM"
  },
  {
    icon: <Calendar className="h-6 w-6" />,
    title: "Book a Meeting",
    description: "Schedule a strategy session",
    contact: "Free 30-minute consultation",
    action: "Book Now",
    available: "Flexible scheduling"
  }
];

// Data for office locations
const offices = [
  {
    city: "London",
    address: "123 Digital Street, London, EC1A 1BB",
    phone: "020 8123 4567",
    email: "<EMAIL>",
    primary: true
  },
  {
    city: "Manchester",
    address: "456 Innovation Way, Manchester, M1 2CD",
    phone: "0161 123 4567",
    email: "<EMAIL>",
    primary: false
  },
  {
    city: "Birmingham",
    address: "789 Business Park, Birmingham, B1 3EF",
    phone: "0121 123 4567",
    email: "<EMAIL>",
    primary: false
  }
];

// Data for FAQs
const faqs = [
  {
    question: "How quickly can you start working on my project?",
    answer: "We can typically begin new projects within 48 hours of contract signing. For urgent projects, we offer expedited onboarding."
  },
  {
    question: "Do you work with businesses outside of London?",
    answer: "Absolutely! We work with businesses across the entire UK and internationally. Our digital-first approach means location is no barrier."
  },
  {
    question: "What's included in your free consultation?",
    answer: "Our free consultation includes a comprehensive audit of your current digital presence, competitor analysis, and a custom strategy roadmap."
  },
  {
    question: "How do you measure success?",
    answer: "We track KPIs that matter to your business: lead generation, conversion rates, revenue growth, and ROI. You'll receive detailed monthly reports."
  }
];

// Motion variants for animations
const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

export default function ContactClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="flex items-center justify-center min-h-[85vh]">
            {/* Centered Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="text-center space-y-10 max-w-6xl"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <MessageSquare className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">GET IN TOUCH WITH OUR EXPERTS</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Let&apos;s Start Your
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Digital Success.
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "320px" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-4xl mx-auto font-bold">
                    Ready to dominate your market? Connect with our{" "}
                    <span className="text-[#FF6B35] relative">
                      digital marketing experts
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                    {" "}and discover how we can transform your business.
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-4xl mx-auto font-medium">
                    From strategy sessions to full-scale digital transformations, we&apos;re here to help you achieve measurable results and sustainable growth.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center justify-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#4CAF50]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <CheckCircle className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#4CAF50] transition-colors duration-200">
                      Free Consultation
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#1E3A8A]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Phone className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#1E3A8A] transition-colors duration-200">
                      Quick Response
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#FF6B35]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Shield className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Expert Team
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
            >
              Multiple Ways to Connect
            </motion.h2>
            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] max-w-3xl mx-auto"
            >
              Choose the method that works best for you. We&apos;re here to help and respond quickly to all inquiries.
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {contactMethods.map((method, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="h-full p-8 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl hover:shadow-xl transition-all duration-300 text-center group">
                  <div className="w-16 h-16 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white group-hover:scale-110 transition-transform duration-300">
                    {method.icon}
                  </div>
                  <h3 className="text-xl font-bold text-[#111827] mb-2">{method.title}</h3>
                  <p className="text-[#6B7280] mb-4">{method.description}</p>
                  <p className="text-[#FF6B35] font-semibold mb-2">{method.contact}</p>
                  <p className="text-sm text-[#6B7280] mb-6">{method.available}</p>
                  <Button className="w-full bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white rounded-xl transition-all duration-300">
                    {method.action}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Form & Offices */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">

            {/* Contact Form */}
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
            >
              <AppointmentForm />
            </motion.div>

            {/* Office Locations */}
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="space-y-8"
            >
              <div>
                <h3 className="text-2xl font-bold text-[#111827] mb-6">Our Offices</h3>
                <p className="text-[#6B7280] mb-8">
                  With offices across the UK, we&apos;re always close to our clients and understand local markets.
                </p>
              </div>

              {offices.map((office, index) => (
                <motion.div key={index} variants={fadeInUp}>
                  <Card className={`p-6 border-0 shadow-lg shadow-black/5 rounded-2xl ${
                    office.primary
                      ? 'bg-gradient-to-br from-[#FF6B35]/5 to-white ring-2 ring-[#FF6B35]'
                      : 'bg-white'
                  }`}>
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-xl flex items-center justify-center text-white flex-shrink-0">
                        <Building className="h-6 w-6" />
                      </div>
                      <div className="flex-grow">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="text-lg font-bold text-[#111827]">{office.city}</h4>
                          {office.primary && (
                            <Badge variant="default" className="bg-[#FF6B35] text-white text-xs hover:bg-[#FF6B35]/90">
                              Headquarters
                            </Badge>
                          )}
                        </div>
                        <div className="space-y-2 text-sm text-[#6B7280]">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-[#FF6B35]" />
                            <span>{office.address}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Phone className="h-4 w-4 text-[#FF6B35]" />
                            <span>{office.phone}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-[#FF6B35]" />
                            <span>{office.email}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
            >
              Frequently Asked Questions
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-[#6B7280]"
            >
              Quick answers to common questions about working with us.
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-6"
          >
            {faqs.map((faq, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="p-6 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl">
                  <h4 className="text-lg font-bold text-[#111827] mb-3">{faq.question}</h4>
                  <p className="text-[#6B7280] leading-relaxed">{faq.answer}</p>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827]"
            >
              Ready to Get Started?
            </motion.h2>

            <motion.p
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Don&apos;t wait – your competitors aren&apos;t. Book your free strategy session today and discover how we can transform your digital presence.
            </motion.p>

            <motion.div variants={fadeInUp}>
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-12 py-6 text-xl font-bold rounded-2xl transition-all duration-300 hover:scale-105 shadow-2xl shadow-[#FF6B35]/25"
              >
                <Calendar className="h-6 w-6 mr-3" />
                Book Your Free Strategy Session
                <ArrowRight className="h-6 w-6 ml-3" />
              </Button>
            </motion.div>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-8 text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-[#4CAF50]" />
                <span>No obligation</span>
              </div>
              <div className="flex items-center space-x-2">
                <Coffee className="h-5 w-5 text-[#4CAF50]" />
                <span>30-minute session</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-[#4CAF50]" />
                <span>Actionable insights</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}