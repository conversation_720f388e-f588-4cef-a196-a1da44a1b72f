import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import BlogPostClient from './blog-post-client';

interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    canonicalUrl: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterTitle: string;
    twitterDescription: string;
    twitterImage: string;
    author: string;
    category: string;
    tags: string[];
    readingTime: number;
    wordCount: number;
  };
}

// Fetch blog post data
async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/blog/list?slug=${slug}`, {
      cache: 'no-store' // Always fetch fresh data
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.success ? data.blogPost : null;
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params;
  const blogPost = await getBlogPost(slug);

  if (!blogPost) {
    return {
      title: 'Blog Post Not Found | WebforLeads',
      description: 'The blog post you are looking for could not be found.',
    };
  }

  return {
    title: blogPost.seo.metaTitle,
    description: blogPost.seo.metaDescription,
    keywords: blogPost.seo.keywords,
    authors: [{ name: blogPost.seo.author }],
    creator: blogPost.seo.author,
    publisher: "WebforLeads",
    robots: "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1",
    openGraph: {
      type: "article",
      locale: "en_US",
      url: blogPost.seo.canonicalUrl,
      title: blogPost.seo.ogTitle,
      description: blogPost.seo.ogDescription,
      siteName: "WebforLeads",
      images: [
        {
          url: blogPost.seo.ogImage,
          width: 1200,
          height: 630,
          alt: blogPost.title
        }
      ],
      publishedTime: blogPost.publishedAt,
      authors: [blogPost.seo.author],
      section: blogPost.seo.category,
      tags: blogPost.seo.tags
    },
    twitter: {
      card: "summary_large_image",
      title: blogPost.seo.twitterTitle,
      description: blogPost.seo.twitterDescription,
      creator: "@webforleads",
      images: [blogPost.seo.twitterImage]
    },
    alternates: {
      canonical: blogPost.seo.canonicalUrl
    },
    other: {
      'article:author': blogPost.seo.author,
      'article:section': blogPost.seo.category,
      'article:tag': blogPost.seo.tags.join(', '),
      'article:published_time': blogPost.publishedAt,
      'article:modified_time': blogPost.publishedAt,
      'twitter:label1': 'Reading time',
      'twitter:data1': `${blogPost.seo.readingTime} min read`,
      'twitter:label2': 'Category',
      'twitter:data2': blogPost.seo.category
    }
  };
}

export default async function BlogPostPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const blogPost = await getBlogPost(slug);

  if (!blogPost) {
    notFound();
  }

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BlogPosting",
            "headline": blogPost.title,
            "description": blogPost.excerpt,
            "image": blogPost.featuredImageUrl,
            "author": {
              "@type": "Organization",
              "name": blogPost.seo.author,
              "url": "https://webforleads.uk"
            },
            "publisher": {
              "@type": "Organization",
              "name": "WebforLeads",
              "url": "https://webforleads.uk",
              "logo": {
                "@type": "ImageObject",
                "url": "https://webforleads.uk/logo.png"
              }
            },
            "datePublished": blogPost.publishedAt,
            "dateModified": blogPost.publishedAt,
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": blogPost.seo.canonicalUrl
            },
            "url": blogPost.seo.canonicalUrl,
            "wordCount": blogPost.seo.wordCount,
            "timeRequired": `PT${blogPost.seo.readingTime}M`,
            "articleSection": blogPost.seo.category,
            "keywords": blogPost.seo.keywords,
            "about": {
              "@type": "Thing",
              "name": blogPost.seo.focusKeyword
            },
            "isPartOf": {
              "@type": "Blog",
              "name": "WebforLeads Blog",
              "url": "https://webforleads.uk/blog"
            }
          })
        }}
      />
      <BlogPostClient blogPost={blogPost} />
    </>
  );
}
