"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Search, Wrench, Rocket, BarChart3, CheckCircle, ArrowRight, Zap, Target, Clock, Star, Sparkles, Award, TrendingUp } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

interface Phase {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  gradient: string;
  duration: string;
  deliverables: string[];
}

const phases: Phase[] = [
  {
    id: 1,
    title: "Deep Dive & Strategy Formulation",
    description: "We begin with a comprehensive discovery phase, including stakeholder interviews, competitor benchmarking, and technical audits. The deliverable is a strategic \"Growth Blueprint\" that outlines the exact roadmap, KPIs, and projected outcomes.",
    icon: <Search className="h-6 w-6" />,
    color: "var(--blue-900)",
    gradient: "bg-gradient-to-r from-blue-900 to-blue-800",
    duration: "Week 1-2",
    deliverables: ["Strategic Growth Blueprint", "Competitor Analysis", "Technical Audit Report", "KPI Framework"]
  },
  {
    id: 2,
    title: "Asset Creation & Technical Execution",
    description: "Our UK-based designers, developers, and copywriters execute the blueprint. This is where your new website is built, your SEO foundation is laid, or your ad campaigns are architected. We maintain total transparency with a dedicated project manager and weekly progress updates.",
    icon: <Wrench className="h-6 w-6" />,
    color: "var(--orange-500)",
    gradient: "bg-gradient-to-r from-orange-500 to-orange-600",
    duration: "Week 3-8",
    deliverables: ["Website Development", "SEO Foundation", "Campaign Architecture", "Content Creation"]
  },
  {
    id: 3,
    title: "Launch, Optimisation & Acceleration",
    description: "Launch is just the starting line. We immediately begin a 90-day acceleration phase, using real-time data to refine and optimise every aspect of the project—from A/B testing landing pages to adjusting SEO tactics based on initial ranking signals.",
    icon: <Rocket className="h-6 w-6" />,
    color: "var(--green-500)",
    gradient: "bg-green-500",
    duration: "Week 9-12",
    deliverables: ["Live Launch", "A/B Testing", "Performance Optimization", "Initial Results"]
  },
  {
    id: 4,
    title: "Continuous Growth & Reporting",
    description: "This is our ongoing partnership. We manage your digital assets, drive continuous improvement, and provide a clear, jargon-free monthly performance report. You'll see exactly how we're turning your investment into leads and revenue.",
    icon: <BarChart3 className="h-6 w-6" />,
    color: "var(--blue-900)",
    gradient: "bg-gradient-to-r from-blue-900 to-blue-800",
    duration: "Ongoing",
    deliverables: ["Monthly Reports", "Continuous Optimization", "Performance Monitoring", "Strategic Adjustments"]
  }
];

export default function SectionBlueprint() {
  const [activePhase, setActivePhase] = useState<number>(1);

  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const cardHover = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.02,
      y: -4,
      transition: { duration: 0.3, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  return (
    <section className="relative py-24 lg:py-32 bg-white overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Header Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="mb-8">
            <Badge
              className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-webforleads-navy/10 to-webforleads-orange/10 border-webforleads-navy/20 text-webforleads-navy text-base font-semibold rounded-full hover:shadow-lg transition-all duration-300"
            >
              <Target className="h-5 w-5" />
              <span>Our Blueprint for Predictable Growth</span>
            </Badge>
          </motion.div>

          <motion.h2
            variants={fadeInUp}
            className="text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight leading-tight mb-8"
          >
            <span className="block text-[#1E3A8A]">From Strategy to</span>
            <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent">
              Measurable Results
            </span>
          </motion.h2>

          <motion.p
            variants={fadeInUp}
            className="text-xl lg:text-2xl text-webforleads-gray-600 max-w-4xl mx-auto leading-relaxed font-medium"
          >
            Our proven 4-phase methodology transforms your digital presence into a predictable revenue engine. Here's exactly how we do it.
          </motion.p>
        </motion.div>

        {/* Premium Timeline */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="space-y-8"
        >
          {phases.map((phase, index) => (
            <motion.div
              key={phase.id}
              variants={fadeInUp}
              initial="rest"
              whileHover="hover"
              className="group cursor-pointer"
              onClick={() => setActivePhase(phase.id)}
            >
              <Card className={`bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-2xl rounded-3xl overflow-hidden transition-all duration-500 ${
                activePhase === phase.id
                  ? 'ring-2 ring-webforleads-orange bg-gradient-to-r from-webforleads-orange/10 to-background shadow-webforleads-orange/25 scale-[1.02]'
                  : 'hover:scale-[1.01]'
              }`}>
                <CardHeader>
                  <div className="flex items-start gap-6">
                    {/* Phase Number & Icon */}
                    <motion.div
                      className={`inline-flex items-center justify-center w-20 h-20 ${phase.gradient} rounded-2xl text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      whileHover={{ rotate: 5 }}
                    >
                      {phase.icon}
                    </motion.div>

                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="secondary" className="text-xs bg-webforleads-gray-100 text-webforleads-gray-600">
                          <Clock className="h-3 w-3 mr-1" />
                          {phase.duration}
                        </Badge>
                        <Badge className="text-xs bg-webforleads-orange/10 text-webforleads-orange border-webforleads-orange/20">
                          Phase {phase.id}
                        </Badge>
                      </div>
                      <CardTitle className="text-2xl lg:text-3xl font-black text-webforleads-gray-900 mb-3">
                        {phase.title}
                      </CardTitle>
                      <CardDescription className="text-lg text-webforleads-gray-600 leading-relaxed font-medium">
                        {phase.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Deliverables */}
                  <div className="grid sm:grid-cols-2 gap-4">
                    {phase.deliverables.map((deliverable, idx) => (
                      <motion.div
                        key={idx}
                        className="flex items-center space-x-3"
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: idx * 0.1 }}
                      >
                        <CheckCircle className="h-5 w-5 text-webforleads-green flex-shrink-0" />
                        <span className="text-sm font-semibold text-webforleads-gray-700">{deliverable}</span>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>

                <CardFooter>
                  <Button
                    variant="outline"
                    className={`hover:scale-105 transition-all duration-300 border-2 transition-all duration-300 ${
                      activePhase === phase.id
                        ? 'border-webforleads-orange text-webforleads-orange hover:bg-webforleads-orange hover:text-white shadow-webforleads-orange/25'
                        : 'border-webforleads-navy/20 text-webforleads-navy hover:bg-webforleads-navy hover:text-white'
                    } px-6 py-3 rounded-xl font-bold focus:outline-none focus:ring-2 focus:ring-webforleads-orange focus:ring-offset-2`}
                  >
                    Learn More
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Premium Stats Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="mt-20"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div variants={fadeInUp}>
              <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl group bg-gradient-to-br from-webforleads-navy/5 to-background">
                <CardContent className="text-center p-8">
                  <motion.div
                    className="w-16 h-16 gradient-navy-premium rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300"
                    whileHover={{ rotate: 5 }}
                  >
                    <Clock className="h-8 w-8 text-white" />
                  </motion.div>
                  <div className="text-4xl font-black text-webforleads-gray-900 mb-2">90 Days</div>
                  <div className="text-webforleads-gray-600 font-semibold">Average Time to Results</div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl group bg-gradient-to-br from-webforleads-orange/5 to-background">
                <CardContent className="text-center p-8">
                  <motion.div
                    className="w-16 h-16 gradient-orange-premium rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300"
                    whileHover={{ rotate: 5 }}
                  >
                    <Zap className="h-8 w-8 text-white" />
                  </motion.div>
                  <div className="text-4xl font-black text-webforleads-gray-900 mb-2">400%</div>
                  <div className="text-webforleads-gray-600 font-semibold">Average ROI Increase</div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl group bg-gradient-to-br from-webforleads-green/5 to-background">
                <CardContent className="text-center p-8">
                  <motion.div
                    className="w-16 h-16 bg-webforleads-green rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300"
                    whileHover={{ rotate: 5 }}
                  >
                    <Star className="h-8 w-8 text-white" />
                  </motion.div>
                  <div className="text-4xl font-black text-webforleads-gray-900 mb-2">98%</div>
                  <div className="text-webforleads-gray-600 font-semibold">Client Satisfaction Rate</div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>

        {/* Premium CTA Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="text-center mt-20"
        >
          <Card className="gradient-navy-premium text-white relative overflow-hidden shadow-2xl border-0">
            {/* Background accent */}
            <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-radial from-webforleads-orange/20 to-transparent rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-webforleads-green/15 to-transparent rounded-full blur-3xl animate-bounce"></div>

            <CardContent className="relative p-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Badge className="inline-flex items-center px-4 py-2 bg-webforleads-orange/20 text-webforleads-orange border-webforleads-orange/30 mb-6">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Growth Blueprint
                </Badge>
              </motion.div>

              <h3 className="text-3xl lg:text-4xl font-black mb-6">
                Ready to Start Your Growth Journey?
              </h3>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed font-medium">
                Let's create your custom Growth Blueprint and start transforming your digital presence into a revenue engine.
              </p>
              <Button
                size="lg"
                className="hover:scale-105 transition-all duration-300 gradient-orange-premium text-white px-10 py-4 text-lg font-black rounded-xl shadow-xl hover:shadow-webforleads-orange/25 focus:outline-none focus:ring-2 focus:ring-webforleads-orange focus:ring-offset-2"
              >
                <Target className="h-5 w-5 mr-2" />
                Get Your Custom Blueprint
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
