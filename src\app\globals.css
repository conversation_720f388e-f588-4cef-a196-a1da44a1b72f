@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* WebforLeads Brand Colors */
  --color-webforleads-orange: #FF6B35;
  --color-webforleads-navy: #1E3A8A;
  --color-webforleads-green: #4CAF50;
  --color-webforleads-gray-50: #F9FAFB;
  --color-webforleads-gray-100: #F3F4F6;
  --color-webforleads-gray-200: #E5E7EB;
  --color-webforleads-gray-300: #D1D5DB;
  --color-webforleads-gray-400: #9CA3AF;
  --color-webforleads-gray-500: #6B7280;
  --color-webforleads-gray-600: #4B5563;
  --color-webforleads-gray-700: #374151;
  --color-webforleads-gray-800: #1F2937;
  --color-webforleads-gray-900: #111827;
}



:root {
  --radius: 0.75rem;

  /* Premium Brand Colors - Orange & Dark Blue Theme */
  --webforleads-navy: #1E3A8A;
  --webforleads-navy-light: #3B82F6;
  --webforleads-navy-dark: #1E40AF;
  --webforleads-orange: #FF6B35;
  --webforleads-orange-light: #FF8A65;
  --webforleads-orange-dark: #E55100;

  /* Enhanced shadcn/ui color system with brand integration */
  --background: oklch(0.99 0.002 106.42);
  --foreground: oklch(0.145 0.012 258.34);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0.012 258.34);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0.012 258.34);

  /* Primary uses brand navy */
  --primary: oklch(0.31 0.132 258.34);
  --primary-foreground: oklch(0.985 0.002 106.42);

  /* Secondary with subtle brand tint */
  --secondary: oklch(0.97 0.008 258.34);
  --secondary-foreground: oklch(0.31 0.132 258.34);

  /* Muted with brand undertones */
  --muted: oklch(0.965 0.006 258.34);
  --muted-foreground: oklch(0.556 0.024 258.34);

  /* Accent uses brand orange */
  --accent: oklch(0.72 0.15 41.116);
  --accent-foreground: oklch(0.985 0.002 106.42);

  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0.008 258.34);
  --input: oklch(0.922 0.008 258.34);
  --ring: oklch(0.31 0.132 258.34);

  /* Enhanced chart colors with brand palette */
  --chart-1: oklch(0.72 0.15 41.116); /* Orange */
  --chart-2: oklch(0.31 0.132 258.34); /* Navy */
  --chart-3: oklch(0.6 0.118 184.704); /* Teal */
  --chart-4: oklch(0.828 0.189 84.429); /* Green */
  --chart-5: oklch(0.769 0.188 70.08); /* Yellow */

  /* Sidebar with brand colors */
  --sidebar: oklch(0.985 0.002 106.42);
  --sidebar-foreground: oklch(0.145 0.012 258.34);
  --sidebar-primary: oklch(0.31 0.132 258.34);
  --sidebar-primary-foreground: oklch(0.985 0.002 106.42);
  --sidebar-accent: oklch(0.97 0.008 258.34);
  --sidebar-accent-foreground: oklch(0.31 0.132 258.34);
  --sidebar-border: oklch(0.922 0.008 258.34);
  --sidebar-ring: oklch(0.31 0.132 258.34);
}

.dark {
  /* Dark mode with brand color integration */
  --background: oklch(0.145 0.012 258.34);
  --foreground: oklch(0.985 0.002 106.42);
  --card: oklch(0.205 0.016 258.34);
  --card-foreground: oklch(0.985 0.002 106.42);
  --popover: oklch(0.205 0.016 258.34);
  --popover-foreground: oklch(0.985 0.002 106.42);

  /* Primary remains brand navy but lighter for dark mode */
  --primary: oklch(0.65 0.15 258.34);
  --primary-foreground: oklch(0.145 0.012 258.34);

  --secondary: oklch(0.269 0.016 258.34);
  --secondary-foreground: oklch(0.985 0.002 106.42);
  --muted: oklch(0.269 0.016 258.34);
  --muted-foreground: oklch(0.708 0.024 258.34);

  /* Accent remains brand orange */
  --accent: oklch(0.72 0.15 41.116);
  --accent-foreground: oklch(0.145 0.012 258.34);

  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 12%);
  --input: oklch(1 0 0 / 18%);
  --ring: oklch(0.65 0.15 258.34);

  /* Dark mode chart colors */
  --chart-1: oklch(0.72 0.15 41.116); /* Orange */
  --chart-2: oklch(0.65 0.15 258.34); /* Navy */
  --chart-3: oklch(0.696 0.17 162.48); /* Teal */
  --chart-4: oklch(0.627 0.265 303.9); /* Purple */
  --chart-5: oklch(0.769 0.188 70.08); /* Yellow */

  --sidebar: oklch(0.205 0.016 258.34);
  --sidebar-foreground: oklch(0.985 0.002 106.42);
  --sidebar-primary: oklch(0.65 0.15 258.34);
  --sidebar-primary-foreground: oklch(0.145 0.012 258.34);
  --sidebar-accent: oklch(0.269 0.016 258.34);
  --sidebar-accent-foreground: oklch(0.985 0.002 106.42);
  --sidebar-border: oklch(1 0 0 / 12%);
  --sidebar-ring: oklch(0.65 0.15 258.34);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced WebforLeads Design System */
  :root {
    /* Premium Brand Colors */
    --webforleads-navy: #1E3A8A;
    --webforleads-navy-50: #EFF6FF;
    --webforleads-navy-100: #DBEAFE;
    --webforleads-navy-200: #BFDBFE;
    --webforleads-navy-300: #93C5FD;
    --webforleads-navy-400: #60A5FA;
    --webforleads-navy-500: #3B82F6;
    --webforleads-navy-600: #2563EB;
    --webforleads-navy-700: #1D4ED8;
    --webforleads-navy-800: #1E40AF;
    --webforleads-navy-900: #1E3A8A;
    --webforleads-navy-950: #172554;

    --webforleads-orange: #FF6B35;
    --webforleads-orange-50: #FFF7ED;
    --webforleads-orange-100: #FFEDD5;
    --webforleads-orange-200: #FED7AA;
    --webforleads-orange-300: #FDBA74;
    --webforleads-orange-400: #FB923C;
    --webforleads-orange-500: #F97316;
    --webforleads-orange-600: #FF6B35;
    --webforleads-orange-700: #C2410C;
    --webforleads-orange-800: #9A3412;
    --webforleads-orange-900: #7C2D12;
    --webforleads-orange-950: #431407;

    /* Premium Neutrals */
    --webforleads-gray-50: #F9FAFB;
    --webforleads-gray-100: #F3F4F6;
    --webforleads-gray-200: #E5E7EB;
    --webforleads-gray-300: #D1D5DB;
    --webforleads-gray-400: #9CA3AF;
    --webforleads-gray-500: #6B7280;
    --webforleads-gray-600: #4B5563;
    --webforleads-gray-700: #374151;
    --webforleads-gray-800: #1F2937;
    --webforleads-gray-900: #111827;
    --webforleads-gray-950: #030712;

    /* Success accent */
    --webforleads-green: #10B981;
    --webforleads-green: #10B981;
    --webforleads-green-light: #34D399;
    --webforleads-green-dark: #059669;
  }
}

@layer components {
  /* Premium Glassmorphism */
  .glass-light {
    @apply bg-white/85 backdrop-blur-xl border border-webforleads-navy/8 shadow-lg shadow-webforleads-navy/5;
  }

  .glass-dark {
    @apply bg-webforleads-navy/95 backdrop-blur-xl border border-white/12 shadow-2xl shadow-black/25;
  }

  .glass-card {
    @apply bg-white/90 backdrop-blur-lg border border-webforleads-navy/6 shadow-xl shadow-webforleads-navy/8;
  }

  /* Premium Shadow System */
  .shadow-premium {
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(30, 58, 138, 0.05);
  }

  .shadow-xl {
    box-shadow:
      0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(30, 58, 138, 0.05);
  }

  .shadow-premium-xl {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(30, 58, 138, 0.05);
  }

  .shadow-premium-2xl {
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(30, 58, 138, 0.05);
  }

  .shadow-glow-orange {
    box-shadow:
      0 0 20px rgba(255, 107, 53, 0.15),
      0 0 40px rgba(255, 107, 53, 0.1);
  }

  .shadow-glow-navy {
    box-shadow:
      0 0 20px rgba(30, 58, 138, 0.15),
      0 0 40px rgba(30, 58, 138, 0.1);
  }
}

@layer utilities {
  /* Premium Button Effects */
  .btn-premium {
    @apply relative overflow-hidden transition-all duration-300 ease-out;
  }

  .btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.25),
      transparent
    );
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-premium:hover::before {
    left: 100%;
  }

  .btn-premium:hover {
    @apply scale-[1.02] shadow-xl;
  }

  /* Premium Animated Underline */
  .underline-premium {
    @apply relative;
  }

  .underline-premium::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--webforleads-orange), var(--webforleads-orange-500));
    border-radius: 2px;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .underline-premium:hover::after {
    width: 100%;
  }

  /* Premium Gradient Backgrounds */
  .gradient-navy-premium {
    background: linear-gradient(135deg, var(--webforleads-navy) 0%, var(--webforleads-navy-700) 50%, var(--webforleads-navy-800) 100%);
  }

  .gradient-orange-premium {
    background: linear-gradient(135deg, var(--webforleads-orange) 0%, var(--webforleads-orange-500) 50%, var(--webforleads-orange-700) 100%);
  }

  .gradient-mesh {
    background:
      radial-gradient(at 40% 20%, var(--webforleads-navy-100) 0px, transparent 50%),
      radial-gradient(at 80% 0%, var(--webforleads-orange-100) 0px, transparent 50%),
      radial-gradient(at 0% 50%, var(--webforleads-navy-50) 0px, transparent 50%),
      radial-gradient(at 80% 50%, var(--webforleads-orange-50) 0px, transparent 50%),
      radial-gradient(at 0% 100%, var(--webforleads-navy-100) 0px, transparent 50%),
      radial-gradient(at 80% 100%, var(--webforleads-orange-100) 0px, transparent 50%),
      radial-gradient(at 0% 0%, var(--webforleads-orange-50) 0px, transparent 50%);
  }

  /* Premium Focus States */
  .focus-premium {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-webforleads-navy/50 focus:ring-offset-background;
  }

  .focus-premium-orange {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-webforleads-orange/50 focus:ring-offset-background;
  }

  /* Premium Animations */
  .animate-gradient-premium {
    background-size: 400% 400%;
    animation: gradient-premium 8s ease infinite;
  }

  @keyframes gradient-premium {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-pulse-premium {
    animation: pulse-premium 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-premium {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  /* Premium Text Effects */
  .text-gradient-navy {
    background: linear-gradient(135deg, var(--webforleads-navy), var(--webforleads-navy-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-orange {
    background: linear-gradient(135deg, var(--webforleads-orange), var(--webforleads-orange-500));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Accessibility & Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    .btn-premium::before,
    .underline-premium::after,
    .animate-gradient-premium,
    .animate-float,
    .animate-pulse-premium {
      animation: none;
      transition: none;
    }

    .btn-premium:hover {
      transform: none;
    }
  }
}
