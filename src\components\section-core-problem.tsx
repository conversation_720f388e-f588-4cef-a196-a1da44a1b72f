"use client";

import React from "react";
import { motion } from "framer-motion";
import { AlertTriangle, Target, TrendingDown, CheckCircle, XCircle, ArrowRight, Zap, DollarSign, Clock, Sparkles, Award, TrendingUp } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

interface ProblemPoint {
  icon: React.ReactNode;
  title: string;
  description: string;
  impact: string;
  gradient: string;
}

interface SolutionPoint {
  icon: React.ReactNode;
  title: string;
  description: string;
  benefit: string;
  gradient: string;
}

const problems: ProblemPoint[] = [
  {
    icon: <TrendingDown className="h-6 w-6" />,
    title: "Invisible Online Presence",
    description: "Your competitors dominate Google's first page while your business remains buried on page 3 or beyond.",
    impact: "Lost 70% of potential customers",
    gradient: "from-red-500 to-red-600"
  },
  {
    icon: <DollarSign className="h-6 w-6" />,
    title: "Wasted Marketing Spend",
    description: "Throwing money at ineffective ads, outdated SEO tactics, and websites that don't convert visitors into customers.",
    impact: "£50K+ annually down the drain",
    gradient: "from-orange-500 to-orange-600"
  },
  {
    icon: <Clock className="h-6 w-6" />,
    title: "Slow, Outdated Websites",
    description: "Your website takes forever to load, looks unprofessional, and drives potential customers straight to competitors.",
    impact: "85% visitor bounce rate",
    gradient: "from-yellow-500 to-yellow-600"
  }
];

const solutions: SolutionPoint[] = [
  {
    icon: <Target className="h-6 w-6" />,
    title: "Strategic Digital Dominance",
    description: "We position your business as the undisputed leader in your local market through data-driven SEO and targeted campaigns.",
    benefit: "3x more qualified leads",
    gradient: "bg-gradient-to-r from-blue-900 to-blue-800"
  },
  {
    icon: <Zap className="h-6 w-6" />,
    title: "High-Converting Assets",
    description: "Every pound spent is tracked, measured, and optimized for maximum ROI with our conversion-focused approach.",
    benefit: "400% increase in ROI",
    gradient: "bg-gradient-to-r from-orange-500 to-orange-600"
  },
  {
    icon: <Award className="h-6 w-6" />,
    title: "Premium Web Experiences",
    description: "Lightning-fast, mobile-optimized websites that turn visitors into customers and customers into advocates.",
    benefit: "90% conversion improvement",
    gradient: "bg-green-500"
  }
];

export default function SectionCoreProblem() {
  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const cardHover = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.02,
      y: -8,
      transition: { duration: 0.3, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  return (
    <section className="relative py-32 lg:py-40 bg-white overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Header Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="mb-8">
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-red-50/80 via-white/90 to-[#FF6B35]/10 border border-red-300/50 text-red-700 text-base font-bold rounded-full hover:shadow-xl hover:shadow-red-500/20 transition-all duration-500 backdrop-blur-sm"
            >
              <AlertTriangle className="h-6 w-6 animate-pulse" />
              <span className="tracking-wide">BREAKING THE CYCLE OF DIGITAL WASTE</span>
            </Badge>
          </motion.div>

          <motion.h2
            variants={fadeInUp}
            className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-10"
          >
            <span className="block text-[#1E3A8A] mb-3">The Hidden Problem</span>
            <span className="block bg-gradient-to-r from-red-600 via-[#FF6B35] to-red-600 bg-clip-text text-transparent animate-gradient-premium">
              Costing You Customers
            </span>
          </motion.h2>

          <motion.p
            variants={fadeInUp}
            className="text-2xl lg:text-3xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-medium"
          >
            Does this sound familiar? You've invested in a website, dabbled in SEO, and poured money into Google Ads, but the phone isn't ringing. You're stuck with vanity metrics—clicks and impressions—that don't pay the bills. This digital guesswork is the single biggest barrier to growth for ambitious businesses in competitive markets like Bristol, Sheffield, and Liverpool.
          </motion.p>
        </motion.div>

        {/* Our Approach Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.h3
            variants={fadeInUp}
            className="text-4xl lg:text-5xl font-black text-[#1E3A8A] mb-8"
          >
            Our Approach: From Digital Presence to{" "}
            <span className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] bg-clip-text text-transparent">
              Business Performance
            </span>
          </motion.h3>

          <motion.p
            variants={fadeInUp}
            className="text-2xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-medium"
          >
            We replace guesswork with a predictable system for growth. We don't just build digital assets; we engineer customer acquisition funnels. Our process is designed to attract your ideal customer, build trust with a seamless user experience, and convert their interest into tangible revenue for your business.
          </motion.p>
        </motion.div>

        {/* Problems vs Solutions Grid */}
        <div className="grid lg:grid-cols-2 gap-16 lg:gap-20">
          
          {/* Problems Column */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <Card className="bg-white/95 backdrop-blur-xl border-0 shadow-2xl rounded-3xl overflow-hidden relative">
              {/* Premium gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 via-[#FF6B35]/10 to-red-500/20 p-px rounded-3xl">
                <div className="w-full h-full rounded-3xl bg-white/95 backdrop-blur-xl"></div>
              </div>
              
              <CardHeader className="relative p-10">
                <CardTitle className="text-4xl lg:text-5xl font-black text-[#1E3A8A] flex items-center justify-center lg:justify-start mb-4">
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-3xl flex items-center justify-center mr-4 shadow-lg"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <XCircle className="h-8 w-8 text-white" />
                  </motion.div>
                  The Problem
                </CardTitle>
                <CardDescription className="text-xl text-slate-600 font-medium leading-relaxed">
                  These critical issues are silently destroying your business growth and competitive advantage
                </CardDescription>
              </CardHeader>
            </Card>

            <div className="space-y-6 mt-8">
              {problems.map((problem, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUp}
                  initial="rest"
                  whileHover="hover"
                  className="group"
                >
                  <Card className="border-0 bg-white/95 backdrop-blur-xl shadow-2xl hover:shadow-2xl hover:shadow-red-500/10 transition-all duration-500 rounded-3xl overflow-hidden group hover:scale-[1.02] relative">
                    {/* Premium gradient border */}
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 via-[#FF6B35]/5 to-red-500/10 p-px rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="w-full h-full rounded-3xl bg-white/95"></div>
                    </div>
                    
                    <CardContent className="relative p-8">
                      <motion.div variants={cardHover} className="flex items-start space-x-6">
                        <motion.div
                          className={`flex-shrink-0 w-18 h-18 bg-gradient-to-br ${problem.gradient} rounded-3xl flex items-center justify-center text-white shadow-xl group-hover:scale-110 transition-transform duration-300`}
                          whileHover={{ rotate: 5 }}
                        >
                          {problem.icon}
                        </motion.div>
                        <div className="flex-1">
                          <h4 className="text-2xl font-bold text-[#1E3A8A] mb-4">
                            {problem.title}
                          </h4>
                          <p className="text-slate-600 mb-6 leading-relaxed text-lg font-medium">
                            {problem.description}
                          </p>
                          <Badge className="bg-gradient-to-r from-red-50 to-red-100 text-red-700 border border-red-200 hover:bg-red-100 px-4 py-2 rounded-full font-bold">
                            <AlertTriangle className="h-4 w-4 mr-2" />
                            Impact: {problem.impact}
                          </Badge>
                        </div>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Solutions Column */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <Card className="bg-white/95 backdrop-blur-xl border-0 shadow-2xl rounded-3xl overflow-hidden relative">
              {/* Premium gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-[#4CAF50]/20 via-[#FF6B35]/10 to-[#4CAF50]/20 p-px rounded-3xl">
                <div className="w-full h-full rounded-3xl bg-white/95 backdrop-blur-xl"></div>
              </div>
              
              <CardHeader className="relative p-10">
                <CardTitle className="text-4xl lg:text-5xl font-black text-[#1E3A8A] flex items-center justify-center lg:justify-start mb-4">
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-3xl flex items-center justify-center mr-4 shadow-lg"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <CheckCircle className="h-8 w-8 text-white" />
                  </motion.div>
                  Our Solution
                </CardTitle>
                <CardDescription className="text-xl text-slate-600 font-medium leading-relaxed">
                  How we transform these problems into profitable opportunities and sustainable growth
                </CardDescription>
              </CardHeader>
            </Card>

            <div className="space-y-6 mt-8">
              {solutions.map((solution, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUp}
                  initial="rest"
                  whileHover="hover"
                  className="group"
                >
                  <Card className="border-0 bg-white/95 backdrop-blur-xl shadow-2xl hover:shadow-2xl hover:shadow-[#4CAF50]/10 transition-all duration-500 rounded-3xl overflow-hidden group hover:scale-[1.02] relative">
                    {/* Premium gradient border */}
                    <div className="absolute inset-0 bg-gradient-to-r from-[#4CAF50]/10 via-[#FF6B35]/5 to-[#4CAF50]/10 p-px rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="w-full h-full rounded-3xl bg-white/95"></div>
                    </div>
                    
                    <CardContent className="relative p-8">
                      <motion.div variants={cardHover} className="flex items-start space-x-6">
                        <motion.div
                          className={`flex-shrink-0 w-18 h-18 ${solution.gradient} rounded-3xl flex items-center justify-center text-white shadow-xl group-hover:scale-110 transition-transform duration-300`}
                          whileHover={{ rotate: 5 }}
                        >
                          {solution.icon}
                        </motion.div>
                        <div className="flex-1">
                          <h4 className="text-2xl font-bold text-[#1E3A8A] mb-4">
                            {solution.title}
                          </h4>
                          <p className="text-slate-600 mb-6 leading-relaxed text-lg font-medium">
                            {solution.description}
                          </p>
                          <Badge className="bg-gradient-to-r from-[#4CAF50]/15 to-[#4CAF50]/10 text-[#4CAF50] border border-[#4CAF50]/30 hover:bg-[#4CAF50]/20 px-4 py-2 rounded-full font-bold">
                            <TrendingUp className="h-4 w-4 mr-2" />
                            Result: {solution.benefit}
                          </Badge>
                        </div>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Premium CTA Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="text-center mt-20"
        >
          <Card className="bg-gradient-to-br from-[#1E3A8A] via-[#1E40AF] to-[#1E3A8A] text-white relative overflow-hidden shadow-2xl border-0 rounded-3xl">
            {/* Enhanced background accents */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-[#FF6B35]/25 to-transparent rounded-full blur-3xl animate-pulse-premium"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-radial from-[#4CAF50]/20 to-transparent rounded-full blur-3xl animate-float"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-radial from-white/5 to-transparent rounded-full blur-3xl animate-pulse-premium" />

            <CardContent className="relative p-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center"
              >
                <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/25 to-[#FF8A65]/20 text-[#FF6B35] border border-[#FF6B35]/40 mb-8 rounded-full backdrop-blur-sm">
                  <Sparkles className="h-5 w-5 mr-2 animate-pulse" />
                  <span className="font-bold tracking-wide">TRANSFORM YOUR BUSINESS</span>
                </Badge>
              </motion.div>

              <h3 className="text-4xl lg:text-6xl font-black mb-8 text-center leading-tight">
                Ready to Break the Cycle?
              </h3>
              <p className="text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed text-center font-medium">
                Stop losing customers to competitors. Let's transform your digital presence into a revenue-generating machine that works 24/7.
              </p>
              
              <div className="text-center">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white px-12 py-6 text-xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/30 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 animate-gradient-premium border-2 border-[#FF6B35]/20"
                  >
                    <Target className="h-6 w-6 mr-3" />
                    Get Your Free Digital Audit
                    <ArrowRight className="h-6 w-6 ml-3" />
                  </Button>
                </motion.div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
