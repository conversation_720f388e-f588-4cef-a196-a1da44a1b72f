"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import AppointmentForm from "@/components/forms/appointment-form";
import {
  Phone, Mail, MapPin, ArrowRight, CheckCircle,
  MessageSquare, Calendar, Building, Coffee, Shield
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const contactMethods = [
  {
    icon: <Phone className="h-6 w-6" />,
    title: "Phone",
    description: "Speak directly with our team",
    contact: "020 8123 4567",
    action: "Call Now",
    available: "Mon-Fri, 9AM-6PM"
  },
  {
    icon: <Mail className="h-6 w-6" />,
    title: "Email",
    description: "Send us a detailed message",
    contact: "<EMAIL>",
    action: "Send Email",
    available: "24/7 Response"
  },
  {
    icon: <MessageSquare className="h-6 w-6" />,
    title: "Live Chat",
    description: "Instant support during business hours",
    contact: "Available on website",
    action: "Start Chat",
    available: "Mon-Fri, 9AM-6PM"
  }
];

const officeFeatures = [
  {
    icon: <Building className="h-5 w-5" />,
    title: "Modern Office Space",
    description: "Professional meeting rooms and collaborative spaces"
  },
  {
    icon: <Coffee className="h-5 w-5" />,
    title: "Comfortable Environment",
    description: "Relaxed atmosphere with refreshments available"
  },
  {
    icon: <Shield className="h-5 w-5" />,
    title: "Secure & Private",
    description: "Confidential discussions in a secure environment"
  }
];

export default function ContactClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF] overflow-hidden min-h-screen">
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">
            
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-white/90 text-[#FF6B35] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <MessageSquare className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">GET IN TOUCH</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-white mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Let's Transform
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Your Business Together
                      </span>
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-white leading-relaxed max-w-3xl font-bold">
                    Ready to discuss your project? Get in touch for a{" "}
                    <span className="text-[#FF6B35] relative">
                      free consultation and strategy session
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-blue-100 leading-relaxed max-w-3xl font-medium">
                    Whether you need a new website, want to improve your SEO, or require comprehensive digital marketing, our team is here to help you achieve your goals.
                  </p>
                </motion.div>
              </motion.div>
            </motion.div>

            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Multiple Ways to
                <span className="block text-[#FF6B35]">Connect With Us</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Choose the communication method that works best for you. We're here to help in whatever way is most convenient.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {contactMethods.map((method, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white">
                      {method.icon}
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-3">{method.title}</h3>
                    <p className="text-gray-600 mb-4">{method.description}</p>
                    
                    <div className="space-y-3 mb-6">
                      <div className="text-lg font-semibold text-[#1E3A8A]">{method.contact}</div>
                      <div className="text-sm text-gray-500">{method.available}</div>
                    </div>

                    <Button className="w-full bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                      {method.action}
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Office Visit Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
            >
              <div className="space-y-8">
                <div className="space-y-6">
                  <div className="inline-flex items-center space-x-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-full">
                    <Building className="h-4 w-4 text-orange-500" />
                    <span className="text-orange-700 font-semibold">Visit Our Office</span>
                  </div>

                  <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                    Meet Us in Person for a
                    <span className="block text-orange-600">Strategy Session</span>
                  </h2>

                  <p className="text-xl text-gray-600 leading-relaxed">
                    Sometimes the best conversations happen face-to-face. Visit our modern office space for an in-depth consultation about your digital strategy.
                  </p>
                </div>

                <div className="space-y-4">
                  {officeFeatures.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-xl flex items-center justify-center text-white flex-shrink-0">
                        {feature.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
                        <p className="text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex items-center space-x-4 p-6 bg-slate-50 rounded-2xl">
                  <MapPin className="h-6 w-6 text-[#FF6B35] flex-shrink-0" />
                  <div>
                    <div className="font-semibold text-gray-900">Office Location</div>
                    <div className="text-gray-600">London, United Kingdom</div>
                    <div className="text-sm text-gray-500 mt-1">Exact address provided upon appointment booking</div>
                  </div>
                </div>

                <Button className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Office Visit
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
              className="relative"
            >
              <div className="aspect-square bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-3xl p-8 flex items-center justify-center">
                <div className="text-center text-white">
                  <Building className="h-24 w-24 mx-auto mb-6 opacity-80" />
                  <h3 className="text-2xl font-bold mb-4">Professional Office Space</h3>
                  <p className="text-blue-200">Modern facilities designed for productive meetings and collaborative sessions.</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
