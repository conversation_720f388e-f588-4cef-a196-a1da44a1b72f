"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import AppointmentForm from "@/components/forms/appointment-form";
import {
  MapPin, ArrowRight, CheckCircle, TrendingUp, Users, Award, Star,
  Zap, Globe, Rocket, Shield, Building,
  Phone, Mail, Search, MousePointer, Smartphone, Share2
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const allUKCities = [
  { id: "london", name: "London", slug: "london", clients: 150, projects: 320, growth: "+45%", featured: true, region: "South East" },
  { id: "birmingham", name: "Birmingham", slug: "birmingham", clients: 85, projects: 180, growth: "+38%", featured: true, region: "West Midlands" },
  { id: "manchester", name: "Manchester", slug: "manchester", clients: 92, projects: 195, growth: "+42%", featured: true, region: "North West" },
  { id: "leeds", name: "Leeds", slug: "leeds", clients: 67, projects: 145, growth: "+35%", featured: true, region: "Yorkshire" },
  { id: "glasgow", name: "Glasgow", slug: "glasgow", clients: 54, projects: 115, growth: "+40%", featured: true, region: "Scotland" },
  { id: "edinburgh", name: "Edinburgh", slug: "edinburgh", clients: 48, projects: 98, growth: "+33%", featured: true, region: "Scotland" },
  { id: "bristol", name: "Bristol", slug: "bristol", clients: 41, projects: 87, growth: "+29%", featured: true, region: "South West" },
  { id: "liverpool", name: "Liverpool", slug: "liverpool", clients: 39, projects: 82, growth: "+31%", featured: true, region: "North West" },
  { id: "newcastle", name: "Newcastle upon Tyne", slug: "newcastle", clients: 35, projects: 74, growth: "+28%", featured: false, region: "North East" },
  { id: "nottingham", name: "Nottingham", slug: "nottingham", clients: 32, projects: 68, growth: "+26%", featured: false, region: "East Midlands" },
  { id: "sheffield", name: "Sheffield", slug: "sheffield", clients: 29, projects: 61, growth: "+24%", featured: false, region: "Yorkshire" },
  { id: "cardiff", name: "Cardiff", slug: "cardiff", clients: 27, projects: 57, growth: "+22%", featured: false, region: "Wales" },
  { id: "leicester", name: "Leicester", slug: "leicester", clients: 24, projects: 52, growth: "+20%", featured: false, region: "East Midlands" },
  { id: "coventry", name: "Coventry", slug: "coventry", clients: 22, projects: 48, growth: "+18%", featured: false, region: "West Midlands" },
  { id: "bradford", name: "Bradford", slug: "bradford", clients: 20, projects: 44, growth: "+16%", featured: false, region: "Yorkshire" },
  { id: "brighton", name: "Brighton and Hove", slug: "brighton", clients: 18, projects: 40, growth: "+14%", featured: false, region: "South East" },
  { id: "plymouth", name: "Plymouth", slug: "plymouth", clients: 16, projects: 36, growth: "+12%", featured: false, region: "South West" },
  { id: "wolverhampton", name: "Wolverhampton", slug: "wolverhampton", clients: 14, projects: 32, growth: "+10%", featured: false, region: "West Midlands" },
  { id: "southampton", name: "Southampton", slug: "southampton", clients: 12, projects: 28, growth: "+8%", featured: false, region: "South East" },
  { id: "portsmouth", name: "Portsmouth", slug: "portsmouth", clients: 10, projects: 24, growth: "+6%", featured: false, region: "South East" }
];

const services = [
  {
    icon: <Globe className="h-6 w-6" />,
    title: "Web Design & Development",
    description: "Custom websites that convert visitors into customers"
  },
  {
    icon: <Search className="h-6 w-6" />,
    title: "Search Engine Optimisation",
    description: "Dominate local search results and attract more customers"
  },
  {
    icon: <MousePointer className="h-6 w-6" />,
    title: "Google Ads & PPC",
    description: "Profitable advertising campaigns with guaranteed ROI"
  },
  {
    icon: <Share2 className="h-6 w-6" />,
    title: "Social Media Marketing",
    description: "Build your brand and engage with local customers"
  }
];

const regions = [
  "All Regions",
  "London",
  "South East", 
  "South West",
  "West Midlands",
  "East Midlands", 
  "North West",
  "North East",
  "Yorkshire",
  "Scotland",
  "Wales"
];

export default function LocationsPage() {
  const [selectedRegion, setSelectedRegion] = useState("All Regions");
  
  const filteredCities = selectedRegion === "All Regions" 
    ? allUKCities 
    : allUKCities.filter(city => city.region === selectedRegion);

  const featuredCities = allUKCities.filter(city => city.featured);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <MapPin className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">UK-WIDE COVERAGE</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Local Insight. National Impact.
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Unfair Advantage.
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "280px" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Winning in Wolverhampton requires a different approach than winning in Westminster. We understand the{" "}
                    <span className="text-[#FF6B35] relative">
                      local nuances
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                    {" "}and create hyper-relevant campaigns.
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Our local expertise combined with national reach gives your business the competitive edge needed to dominate your market across the UK.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#4CAF50]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Building className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#4CAF50] transition-colors duration-200">
                      50+ UK Cities
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#1E3A8A]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Users className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#1E3A8A] transition-colors duration-200">
                      Local Expertise
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#FF6B35]/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <TrendingUp className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      National Impact
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-12"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-2xl lg:text-3xl font-bold text-[#111827] mb-4"
            >
              Digital Marketing & SEO Services Available In:
            </motion.h2>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {services.map((service, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="p-6 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-xl flex items-center justify-center mx-auto mb-4 text-white">
                    {service.icon}
                  </div>
                  <h3 className="text-lg font-bold text-[#111827] mb-2">{service.title}</h3>
                  <p className="text-[#6B7280] text-sm">{service.description}</p>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Featured Cities */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
            >
              Major UK Cities We Serve
            </motion.h2>
            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] max-w-3xl mx-auto"
            >
              Our expert team delivers exceptional results across all major UK cities and towns.
            </motion.p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4"
          >
            {featuredCities.map((city) => (
              <motion.div key={city.id} variants={fadeInUp}>
                <Link href={`/locations/${city.slug}`}>
                  <Card className="p-4 border-0 bg-white shadow-lg shadow-black/5 rounded-xl hover:shadow-xl hover:scale-105 transition-all duration-300 text-center group cursor-pointer">
                    <h3 className="font-semibold text-[#111827] mb-2 group-hover:text-[#FF6B35] transition-colors">{city.name}</h3>
                    <div className="text-sm text-[#6B7280] mb-2">{city.clients} clients</div>
                    <div className="text-xs font-semibold text-[#4CAF50]">{city.growth} growth</div>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Complete Cities List */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
            >
              Complete UK Coverage
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-[#6B7280] max-w-3xl mx-auto mb-8"
            >
              Professional digital marketing services available across all major UK cities and towns.
            </motion.p>

            {/* Region Filter */}
            <motion.div variants={fadeInUp} className="flex flex-wrap justify-center gap-2 mb-8">
              {regions.map((region) => (
                <Button
                  key={region}
                  variant={selectedRegion === region ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedRegion(region)}
                  className={`rounded-full transition-all duration-300 ${
                    selectedRegion === region
                      ? 'bg-[#FF6B35] text-white hover:bg-[#FF6B35]/90'
                      : 'border-[#1E3A8A]/20 text-[#1E3A8A] hover:bg-[#1E3A8A] hover:text-white'
                  }`}
                >
                  {region}
                </Button>
              ))}
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
          >
            {filteredCities.map((city) => (
              <motion.div key={city.id} variants={fadeInUp}>
                <Link href={`/locations/${city.slug}`}>
                  <Card className="p-4 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-xl hover:shadow-xl hover:scale-105 transition-all duration-300 group cursor-pointer">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-[#111827] group-hover:text-[#FF6B35] transition-colors">
                        {city.name}
                      </h3>
                      <div className="w-2 h-2 bg-[#4CAF50] rounded-full opacity-60 group-hover:opacity-100 transition-opacity"></div>
                    </div>

                    <div className="space-y-1 text-sm text-[#6B7280]">
                      <div>Web Design & SEO</div>
                      <div className="text-xs">
                        <span className="font-semibold text-[#111827]">{city.clients}</span> clients •
                        <span className="font-semibold text-[#4CAF50] ml-1">{city.growth}</span>
                      </div>
                    </div>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </motion.div>

          {/* SEO-Rich Footer Text */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
            className="mt-16 p-8 bg-gradient-to-br from-[#F9FAFB] to-white rounded-3xl border border-[#1E3A8A]/10"
          >
            <p className="text-center text-[#6B7280] leading-relaxed">
              <strong className="text-[#111827]">Professional Digital Marketing Services</strong> including
              <span className="text-[#FF6B35] font-semibold"> Web Design, SEO, Google Ads, and Social Media Marketing </span>
              available across all UK locations. Our expert team delivers
              <span className="text-[#1E3A8A] font-semibold"> measurable results and ROI-focused campaigns </span>
              tailored to your local market and business objectives.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Local Expertise Section */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">

            {/* Local Insights */}
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
            >
              <h3 className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6">
                Why Local Expertise Matters
              </h3>
              <p className="text-lg text-[#6B7280] mb-8 leading-relaxed">
                Every UK city has its unique digital landscape. From London's competitive financial sector to Manchester's thriving tech scene, we understand the local nuances that make the difference between a campaign that works and one that dominates.
              </p>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Search className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-[#111827] mb-2">Local Search Behavior Analysis</h4>
                    <p className="text-[#6B7280]">We research how customers in your specific city search for services, what terms they use, and when they're most active online.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-xl flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-[#111827] mb-2">Competitive Landscape Mapping</h4>
                    <p className="text-[#6B7280]">We identify gaps in your local market that your competitors are missing, giving you strategic advantages.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#4CAF50] to-[#4CAF50]/80 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-[#111827] mb-2">Local Customer Insights</h4>
                    <p className="text-[#6B7280]">Understanding local demographics, preferences, and buying patterns to create campaigns that truly resonate.</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Stats Card */}
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
            >
              <Card className="p-8 lg:p-10 border-0 bg-white shadow-lg shadow-black/5 rounded-3xl">
                <h4 className="text-2xl font-bold text-[#111827] mb-8">UK-Wide Impact</h4>

                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-[#FF6B35] mb-2">20+</div>
                    <div className="text-[#6B7280]">UK Cities Served</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-[#4CAF50] mb-2">500+</div>
                    <div className="text-[#6B7280]">Local Businesses</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-[#FF6B35] mb-2">1200+</div>
                    <div className="text-[#6B7280]">Successful Projects</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-[#4CAF50] mb-2">98%</div>
                    <div className="text-[#6B7280]">Client Retention</div>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-[#6B7280] mb-6">
                    From Scotland to the South Coast, we're helping UK businesses dominate their local markets.
                  </p>
                  <Button className="bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white rounded-xl">
                    Find Your Local Expert
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827]"
            >
              Ready to Dominate Your Local Market?
            </motion.h2>

            <motion.p
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Whether you're in London's competitive landscape or looking to dominate a smaller market, we have the local expertise and national resources to make it happen.
            </motion.p>

            <motion.div variants={fadeInUp}>
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-12 py-6 text-xl font-bold rounded-2xl transition-all duration-300 hover:scale-105 shadow-2xl shadow-[#FF6B35]/25"
              >
                <MapPin className="h-6 w-6 mr-3" />
                Start Your Local Campaign
                <ArrowRight className="h-6 w-6 ml-3" />
              </Button>
            </motion.div>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-8 text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-[#4CAF50]" />
                <span>Free local market analysis</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-[#4CAF50]" />
                <span>No long-term contracts</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-[#4CAF50]" />
                <span>Results guaranteed</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
