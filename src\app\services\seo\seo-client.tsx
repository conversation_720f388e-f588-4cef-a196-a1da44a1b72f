"use client";

import React from "react";
import { motion } from "framer-motion";
import <PERSON> from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Search, ArrowRight, CheckCircle, TrendingUp, Users, Award, Star,
  Target, Zap, BarChart3, Globe, Rocket, Shield, MapPin,
  FileText, Link as LinkIcon, Eye, Clock, ChevronUp, Phone,
  Mail, Calendar, Play, Download, ExternalLink, Lightbulb,
  Gauge, Trophy, Building, MessageSquare, ChevronRight, Sparkles,
  Brain, Monitor, Smartphone, Settings
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 }
};

// Premium stats for billion-dollar feel
const stats = [
  { number: "400%", label: "Average Traffic Increase", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "£1.2M+", label: "Revenue Generated", icon: <Target className="h-6 w-6" /> },
  { number: "92%", label: "First Page Rankings", icon: <Trophy className="h-6 w-6" /> },
  { number: "150+", label: "SEO Success Stories", icon: <Rocket className="h-6 w-6" /> }
];

// Premium SEO features
const seoFeatures = [
  {
    icon: <Brain className="h-8 w-8" />,
    title: "Advanced SEO Strategy",
    description: "Data-driven SEO strategies that combine technical expertise with content optimization to dominate search rankings.",
    highlight: "Strategy-Driven"
  },
  {
    icon: <Gauge className="h-8 w-8" />,
    title: "Technical SEO Excellence",
    description: "Comprehensive technical audits and optimizations that improve site speed, crawlability, and search engine visibility.",
    highlight: "Technical Focus"
  },
  {
    icon: <Target className="h-8 w-8" />,
    title: "Local SEO Domination",
    description: "Dominate local search results with optimized Google Business profiles, local citations, and geo-targeted content.",
    highlight: "Local Expert"
  },
  {
    icon: <BarChart3 className="h-8 w-8" />,
    title: "Analytics & Reporting",
    description: "Detailed performance tracking with monthly reports showing keyword rankings, traffic growth, and ROI metrics.",
    highlight: "Data-Driven"
  }
];

export default function SEOClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-white/90 text-[#FF6B35] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Search className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">PROFESSIONAL SEO SERVICES</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      SEO Services That
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Dominate Search Rankings
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Leading SEO Company Specializing in{" "}
                    <span className="text-[#FF6B35] relative">
                      Organic Traffic Growth & Search Engine Optimization
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Professional SEO services that increase organic traffic, improve search rankings, and drive qualified leads. Our expert SEO strategies deliver measurable results and long-term growth for your business.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <TrendingUp className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Proven Results
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Search className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Expert SEO Team
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <CheckCircle className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      White Hat SEO
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>

          {/* Premium Stats */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={scaleIn}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-[#1E3A8A] mb-2">{stat.number}</div>
                <div className="text-lg text-slate-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium SEO Features Section */}
      <section className="py-32 mt-16 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/5 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-[#1E3A8A]/5 to-transparent rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <div className="inline-flex items-center space-x-2 px-4 py-2 bg-[#FF6B35]/10 border border-[#FF6B35]/20 rounded-full">
                <Sparkles className="h-4 w-4 text-[#FF6B35]" />
                <span className="text-[#FF6B35] font-semibold">Premium SEO Features</span>
              </div>

              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                SEO Services That
                <span className="block text-[#FF6B35]">Drive Real Results</span>
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our comprehensive SEO strategies are designed to increase organic traffic, improve search rankings, and generate qualified leads for your business.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {seoFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <CardContent className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white flex-shrink-0">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#1E3A8A]/10 text-[#1E3A8A] text-xs font-semibold rounded-full mb-2">
                          {feature.highlight}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                      </div>
                    </div>

                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </CardContent>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>



      {/* Premium Pricing Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <TrendingUp className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">SEO PACKAGES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Choose Your
                <span className="block text-[#FF6B35]">SEO Growth Package</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From local SEO to enterprise optimization, we have the perfect package to boost your search rankings.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16"
          >
            {[
              {
                name: "Local SEO",
                price: "£1,200",
                period: "/month",
                description: "Perfect for local businesses",
                features: [
                  "Local keyword optimization",
                  "Google My Business optimization",
                  "Local citation building",
                  "On-page SEO optimization",
                  "Monthly ranking reports",
                  "Review management"
                ],
                highlight: false,
                cta: "Boost Local Rankings"
              },
              {
                name: "Growth SEO",
                price: "£2,500",
                period: "/month",
                description: "For businesses ready to scale",
                features: [
                  "Everything in Local SEO",
                  "National keyword targeting",
                  "Content strategy & creation",
                  "Link building campaigns",
                  "Technical SEO audits",
                  "Competitor analysis"
                ],
                highlight: true,
                cta: "Scale SEO Growth"
              },
              {
                name: "Enterprise SEO",
                price: "Custom",
                period: "",
                description: "Comprehensive SEO for large sites",
                features: [
                  "Everything in Growth SEO",
                  "Multi-location optimization",
                  "Advanced technical SEO",
                  "Dedicated SEO team",
                  "Custom reporting",
                  "Priority support"
                ],
                highlight: false,
                cta: "Enterprise SEO"
              }
            ].map((pkg, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
                className={`relative ${pkg.highlight ? 'lg:-mt-8' : ''}`}
              >
                <Card className={`h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group ${pkg.highlight ? 'ring-2 ring-[#FF6B35] ring-opacity-50' : ''}`}>
                  {pkg.highlight && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] text-white px-4 py-1 rounded-full shadow-lg">
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold text-[#1E3A8A] mb-2">{pkg.name}</h3>
                      <div className="mb-4">
                        <span className="text-4xl font-black text-[#FF6B35]">{pkg.price}</span>
                        <span className="text-slate-500">{pkg.period}</span>
                      </div>
                      <p className="text-slate-600">{pkg.description}</p>
                    </div>

                    <ul className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-[#FF6B35] flex-shrink-0" />
                          <span className="text-slate-700">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <Button 
                      onClick={() => {
                        const el = document.getElementById('appointment-form');
                        if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }}
                      className={`w-full py-3 rounded-2xl font-bold transition-all duration-300 ${
                        pkg.highlight 
                          ? 'bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF8A65] hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl' 
                          : 'bg-[#1E3A8A] hover:bg-[#1E40AF] text-white'
                      }`}
                    >
                      {pkg.cta}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Process Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Globe className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">SEO PROCESS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Our Proven
                <span className="block text-[#FF6B35]">SEO Process</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From audit to optimization, our systematic approach ensures sustainable organic growth and higher rankings.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                step: "01",
                title: "SEO Audit & Analysis",
                description: "Comprehensive analysis of your website's current SEO performance, technical issues, and opportunities.",
                icon: <Globe className="h-8 w-8" />
              },
              {
                step: "02", 
                title: "Strategy Development",
                description: "Create a customized SEO strategy based on keyword research, competitor analysis, and business goals.",
                icon: <TrendingUp className="h-8 w-8" />
              },
              {
                step: "03",
                title: "Implementation",
                description: "Execute on-page optimization, technical fixes, content creation, and link building campaigns.",
                icon: <Rocket className="h-8 w-8" />
              },
              {
                step: "04",
                title: "Monitor & Optimize",
                description: "Continuous monitoring, reporting, and optimization to maintain and improve search rankings.",
                icon: <BarChart3 className="h-8 w-8" />
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] flex items-center justify-center text-white mx-auto mb-4">
                        {process.icon}
                      </div>
                      <div className="text-6xl font-black text-[#1E3A8A]/10 mb-4">{process.step}</div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-[#1E3A8A] mb-4 group-hover:text-[#FF6B35] transition-colors">
                      {process.title}
                    </h3>
                    
                    <p className="text-slate-600 leading-relaxed">
                      {process.description}
                    </p>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Testimonials Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                SEO Results That
                <span className="block text-[#FF6B35]">Drive Organic Growth</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Discover how our SEO strategies have transformed businesses with increased rankings, organic traffic, and sustainable growth.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {[
              {
                quote: "Organic traffic increased by 340% in 8 months, with 15 keywords ranking on page 1. Our lead generation from SEO has completely transformed our business growth.",
                name: "Emma Johnson",
                role: "Marketing Director",
                company: "TechSolutions Ltd",
                industry: "Technology",
                metric: "340%",
                metricLabel: "Traffic Increase",
                avatar: "EJ"
              },
              {
                quote: "From page 3 to position #1 for our main keywords. Revenue from organic search grew by £180K annually. The technical SEO audit was game-changing.",
                name: "Michael Chen",
                role: "CEO",
                company: "GreenEnergy Pro",
                industry: "Renewable Energy",
                metric: "£180K",
                metricLabel: "Annual Revenue",
                avatar: "MC"
              },
              {
                quote: "Local SEO optimization resulted in 280% more local leads and 95% improvement in Google My Business visibility. Absolutely phenomenal results.",
                name: "Sarah Davis",
                role: "Owner",
                company: "Elite Dental Care",
                industry: "Healthcare",
                metric: "280%",
                metricLabel: "Local Leads",
                avatar: "SD"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-1 text-[#FF6B35] mb-4">
                      {[...Array(5)].map((_, s) => <Star key={s} className="h-4 w-4 fill-current" />)}
                    </div>
                    
                    <blockquote className="text-slate-700 leading-relaxed mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] flex items-center justify-center text-white font-bold text-sm">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="text-sm font-bold text-[#1E3A8A]">{testimonial.name}</div>
                          <div className="text-xs text-slate-500">{testimonial.role}</div>
                          <div className="text-xs text-[#FF6B35] font-semibold">{testimonial.company}</div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-black text-[#FF6B35]">{testimonial.metric}</div>
                        <div className="text-xs text-slate-500 font-semibold">{testimonial.metricLabel}</div>
                      </div>
                    </div>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium FAQs Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <TrendingUp className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                SEO
                <span className="block text-[#FF6B35]">Expert Knowledge</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Get answers to the most common questions about SEO strategies, ranking factors, and building sustainable organic growth.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {[
              {
                category: "SEO Strategy",
                icon: <TrendingUp className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you develop an effective SEO strategy?",
                    a: "We start with comprehensive keyword research, competitor analysis, and technical audits. Our strategy includes on-page optimization, content planning, link building, and local SEO tactics tailored to your industry and target audience."
                  },
                  {
                    q: "What's your approach to keyword research and targeting?",
                    a: "We use advanced tools like SEMrush, Ahrefs, and Google Keyword Planner to identify high-value keywords with optimal search volume and competition. We focus on search intent, long-tail keywords, and semantic keyword clusters for comprehensive coverage."
                  }
                ]
              },
              {
                category: "Technical SEO",
                icon: <Settings className="h-6 w-6" />,
                questions: [
                  {
                    q: "How important is technical SEO for rankings?",
                    a: "Technical SEO is crucial as it ensures search engines can crawl, index, and understand your website. We optimize site speed, mobile responsiveness, URL structure, schema markup, and fix crawl errors to create a solid foundation for rankings."
                  },
                  {
                    q: "Do you handle Core Web Vitals and page speed optimization?",
                    a: "Absolutely. We optimize Core Web Vitals (LCP, FID, CLS) through image optimization, code minification, caching strategies, and server improvements. Page speed is a critical ranking factor that directly impacts user experience and conversions."
                  }
                ]
              },
              {
                category: "Content & Rankings",
                icon: <Globe className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you create SEO-optimized content that ranks?",
                    a: "We create comprehensive, user-focused content that answers search intent. Our process includes keyword integration, topic clusters, internal linking strategies, and regular content updates to maintain relevance and authority."
                  },
                  {
                    q: "How long does it take to see SEO results?",
                    a: "Initial improvements can be seen in 3-6 months, with significant results typically appearing after 6-12 months. SEO is a long-term strategy that builds compound growth over time, with consistent improvements in rankings and traffic."
                  }
                ]
              },
              {
                category: "Reporting & Analytics",
                icon: <BarChart3 className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you track and measure SEO performance?",
                    a: "We use Google Analytics, Search Console, and professional SEO tools to track rankings, organic traffic, click-through rates, and conversions. Monthly reports include keyword performance, technical health scores, and actionable recommendations."
                  },
                  {
                    q: "What's included in your SEO service package?",
                    a: "Our comprehensive SEO service includes technical audits, keyword research, on-page optimization, content strategy, link building, local SEO, monthly reporting, and ongoing optimization based on performance data and algorithm updates."
                  }
                ]
              }
            ].map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={fadeInUp}
                className="space-y-6"
              >
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF6B35]/80 flex items-center justify-center text-white">
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">{category.category}</h3>
                </div>

                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <motion.div
                      key={faqIndex}
                      variants={fadeInUp}
                      whileHover={{ y: -2 }}
                      transition={{ type: "spring", stiffness: 300, damping: 25 }}
                    >
                      <Card className="border-0 rounded-2xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                        <CardContent className="p-8">
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 rounded-full bg-[#FF6B35]/10 flex items-center justify-center flex-shrink-0 mt-1">
                              <div className="w-2 h-2 rounded-full bg-[#FF6B35]"></div>
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                                {faq.q}
                              </h4>
                              <p className="text-slate-600 leading-relaxed">
                                {faq.a}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                        
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="appointment-form" className="py-24 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <Badge className="px-4 py-1 bg-white/10 text-white border border-white/20 rounded-full">Get Started</Badge>
              <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">Ready to Dominate Search Results?</h2>
              <p className="text-slate-300 text-lg max-w-xl">Let's build an SEO strategy that drives sustainable organic growth and positions your business as an industry leader.</p>
            </div>
            <div>
              <AppointmentForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
