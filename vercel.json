{"buildCommand": "npm run build", "framework": "nextjs", "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1", "NODE_OPTIONS": "--max-old-space-size=4096"}}, "functions": {"src/app/api/blog/generate/route.ts": {"maxDuration": 300}, "src/app/api/bulk-pages/start-generation/route.ts": {"maxDuration": 300}, "src/app/api/cron/generate-blog/route.ts": {"maxDuration": 300}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "crons": [{"path": "/api/cron/generate-blog", "schedule": "0 9 * * *"}]}