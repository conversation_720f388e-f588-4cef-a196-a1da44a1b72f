"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Shield, ArrowRight, CheckCircle, Lock, Eye, 
  AlertTriangle, Info, Clock, Mail, Phone, Database
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const sections = [
  {
    id: "information-collection",
    title: "1. Information We Collect",
    content: [
      "Personal Information: Name, email address, phone number, company details, and billing information when you contact us or use our services.",
      "Website Usage Data: IP address, browser type, pages visited, time spent on pages, and referral sources through cookies and analytics tools.",
      "Communication Records: Records of our communications with you, including emails, phone calls, and meeting notes.",
      "Project Data: Information related to your business, marketing goals, and campaign performance as necessary to provide our services."
    ]
  },
  {
    id: "information-use",
    title: "2. How We Use Your Information",
    content: [
      "Service Delivery: To provide, maintain, and improve our digital marketing services.",
      "Communication: To respond to inquiries, provide customer support, and send service-related notifications.",
      "Marketing: To send newsletters, promotional materials, and relevant business insights (with your consent).",
      "Legal Compliance: To comply with legal obligations and protect our rights and interests.",
      "Analytics: To analyze website usage and improve our services and user experience."
    ]
  },
  {
    id: "information-sharing",
    title: "3. Information Sharing and Disclosure",
    content: [
      "We do not sell, trade, or rent your personal information to third parties.",
      "Service Providers: We may share information with trusted third-party service providers who assist in delivering our services (e.g., hosting, analytics, payment processing).",
      "Legal Requirements: We may disclose information when required by law or to protect our rights, property, or safety.",
      "Business Transfers: In the event of a merger, acquisition, or sale, your information may be transferred as part of the business assets.",
      "Consent: We may share information with your explicit consent for specific purposes."
    ]
  },
  {
    id: "data-security",
    title: "4. Data Security",
    content: [
      "We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.",
      "All data transmission is encrypted using SSL/TLS protocols.",
      "Access to personal information is restricted to authorized personnel only.",
      "We regularly review and update our security practices to ensure ongoing protection.",
      "However, no method of transmission over the internet is 100% secure, and we cannot guarantee absolute security."
    ]
  },
  {
    id: "cookies",
    title: "5. Cookies and Tracking Technologies",
    content: [
      "Essential Cookies: Required for basic website functionality and security.",
      "Analytics Cookies: Help us understand how visitors use our website to improve user experience.",
      "Marketing Cookies: Used to deliver relevant advertisements and track campaign effectiveness.",
      "You can control cookie preferences through your browser settings, though this may affect website functionality.",
      "We use Google Analytics and other tools to analyze website traffic and user behavior."
    ]
  },
  {
    id: "your-rights",
    title: "6. Your Rights (GDPR)",
    content: [
      "Access: Request a copy of the personal information we hold about you.",
      "Rectification: Request correction of inaccurate or incomplete personal information.",
      "Erasure: Request deletion of your personal information (subject to legal obligations).",
      "Portability: Request transfer of your personal information to another service provider.",
      "Objection: Object to processing of your personal information for marketing purposes.",
      "Restriction: Request restriction of processing in certain circumstances.",
      "To exercise these rights, please contact us using the information provided below."
    ]
  },
  {
    id: "retention",
    title: "7. Data Retention",
    content: [
      "We retain personal information only as long as necessary to fulfill the purposes for which it was collected.",
      "Client project data is typically retained for 7 years after project completion for legal and business purposes.",
      "Marketing communications data is retained until you unsubscribe or request deletion.",
      "Website analytics data is retained for 26 months in accordance with Google Analytics default settings.",
      "We regularly review and delete data that is no longer necessary."
    ]
  },
  {
    id: "updates",
    title: "8. Policy Updates",
    content: [
      "We may update this privacy policy from time to time to reflect changes in our practices or legal requirements.",
      "We will notify you of any material changes via email or prominent notice on our website.",
      "The updated policy will be effective from the date of publication.",
      "We encourage you to review this policy periodically to stay informed about how we protect your information."
    ]
  }
];

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-24 lg:py-32 bg-gradient-to-b from-white to-[#F9FAFB] overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5" />
          <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-[#4CAF50]/10 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-br from-[#FF6B35]/10 to-transparent rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="text-center space-y-8"
          >
            <motion.div variants={fadeInUp}>
              <Badge 
                variant="outline" 
                className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-[#1E3A8A]/5 to-[#4CAF50]/5 border-[#1E3A8A]/20 text-[#1E3A8A] text-base font-semibold rounded-full"
              >
                <Shield className="h-5 w-5" />
                <span>Data Protection</span>
              </Badge>
            </motion.div>

            <motion.h1 
              variants={fadeInUp}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-[#111827] leading-tight"
            >
              <span className="block">Privacy</span>
              <span className="block bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] bg-clip-text text-transparent">
                Policy
              </span>
            </motion.h1>

            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Your privacy is important to us. This policy explains how we collect, use, and protect 
              your personal information in compliance with GDPR and UK data protection laws.
            </motion.p>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-6 text-sm text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>Last updated: January 2024</span>
              </div>
              <div className="flex items-center space-x-2">
                <Lock className="h-4 w-4" />
                <span>GDPR Compliant</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Privacy Principles */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-12"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-2xl lg:text-3xl font-bold text-[#111827] mb-8"
            >
              Our Privacy Principles
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[
                { icon: <Lock className="h-6 w-6" />, title: "Secure", description: "Your data is encrypted and protected" },
                { icon: <Eye className="h-6 w-6" />, title: "Transparent", description: "Clear about what we collect and why" },
                { icon: <CheckCircle className="h-6 w-6" />, title: "Compliant", description: "GDPR and UK law compliant" },
                { icon: <Database className="h-6 w-6" />, title: "Minimal", description: "We only collect what we need" }
              ].map((principle, index) => (
                <motion.div key={index} variants={fadeInUp}>
                  <Card className="p-6 border-0 bg-gradient-to-br from-[#F9FAFB] to-white shadow-lg shadow-black/5 rounded-2xl text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-xl flex items-center justify-center mx-auto mb-4 text-white">
                      {principle.icon}
                    </div>
                    <h3 className="text-lg font-bold text-[#111827] mb-2">{principle.title}</h3>
                    <p className="text-[#6B7280] text-sm">{principle.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-12"
          >
            {sections.map((section, index) => (
              <motion.div key={section.id} variants={fadeInUp}>
                <Card className="p-8 border-0 bg-white shadow-lg shadow-black/5 rounded-2xl">
                  <h2 className="text-2xl font-bold text-[#111827] mb-6">{section.title}</h2>
                  <div className="space-y-4">
                    {section.content.map((paragraph, idx) => (
                      <p key={idx} className="text-[#6B7280] leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Data Rights Notice */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
          >
            <Card className="p-8 border-0 bg-gradient-to-br from-[#4CAF50]/5 to-white shadow-lg shadow-black/5 rounded-2xl border-l-4 border-l-[#4CAF50]">
              <div className="flex items-start space-x-4">
                <Info className="h-6 w-6 text-[#4CAF50] flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-lg font-bold text-[#111827] mb-2">Your Data Rights</h3>
                  <p className="text-[#6B7280] leading-relaxed mb-4">
                    Under GDPR, you have the right to access, correct, delete, or transfer your personal data. 
                    You can also object to processing or request restrictions on how we use your information.
                  </p>
                  <p className="text-[#6B7280] leading-relaxed">
                    To exercise any of these rights, please contact our Data Protection Officer using the 
                    contact information below. We will respond to your request within 30 days.
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-3xl lg:text-4xl font-bold text-[#111827]"
            >
              Questions About Your Privacy?
            </motion.h2>
            
            <motion.p 
              variants={fadeInUp}
              className="text-xl text-[#6B7280] leading-relaxed"
            >
              Our Data Protection Officer is here to help with any privacy-related questions or requests.
            </motion.p>

            <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-10 py-4 text-lg font-semibold rounded-xl transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <Mail className="h-5 w-5 mr-2" />
                Contact Data Protection Officer
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                className="border-[#1E3A8A]/20 text-[#1E3A8A] hover:bg-[#1E3A8A] hover:text-white px-10 py-4 text-lg font-semibold rounded-xl transition-all duration-300"
              >
                <Phone className="h-5 w-5 mr-2" />
                Call: 020 8123 4567
              </Button>
            </motion.div>

            <motion.div variants={fadeInUp} className="flex items-center justify-center space-x-8 text-[#6B7280]">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-[#4CAF50]" />
                <span>GDPR Compliant</span>
              </div>
              <div className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-[#4CAF50]" />
                <span>Secure Processing</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-[#4CAF50]" />
                <span>Your Rights Protected</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
