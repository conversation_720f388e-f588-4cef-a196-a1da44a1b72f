import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Base URL for your website
const BASE_URL = 'https://webforleads.uk';

// Static pages configuration
const STATIC_PAGES = [
  {
    url: '',
    priority: 1.0,
    changefreq: 'weekly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/about',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services',
    priority: 0.9,
    changefreq: 'weekly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services/web-design',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services/seo',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services/app-development',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services/digital-marketing',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services/social-media',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/services/google-ads',
    priority: 0.8,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/blog',
    priority: 0.9,
    changefreq: 'daily',
    lastmod: new Date().toISOString()
  },
  {
    url: '/contact',
    priority: 0.7,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/locations',
    priority: 0.6,
    changefreq: 'monthly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/privacy',
    priority: 0.3,
    changefreq: 'yearly',
    lastmod: new Date().toISOString()
  },
  {
    url: '/terms',
    priority: 0.3,
    changefreq: 'yearly',
    lastmod: new Date().toISOString()
  }
];

// Define types for sitemap entries
interface SitemapEntry {
  url: string;
  priority: number;
  changefreq: string;
  lastmod: string;
}

// Get all blog posts from storage
async function getBlogPosts(): Promise<SitemapEntry[]> {
  try {
    const blogStorageDir = path.join(process.cwd(), 'blog-storage');
    const blogPosts: SitemapEntry[] = [];

    // Check if blog storage directory exists
    if (fs.existsSync(blogStorageDir)) {
      const files = fs.readdirSync(blogStorageDir);

      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(blogStorageDir, file);
          const stat = fs.statSync(filePath);

          try {
            // Read the blog post JSON to get the slug and published date
            const content = fs.readFileSync(filePath, 'utf-8');
            const blogPost = JSON.parse(content);

            // Use published date as lastmod, fallback to file modification time
            const lastmod = blogPost.publishedAt || stat.mtime.toISOString();

            blogPosts.push({
              url: `/blog/${blogPost.slug}`,
              priority: 0.7,
              changefreq: 'weekly',
              lastmod
            });
          } catch (parseError) {
            console.warn(`Failed to parse blog post file ${file}:`, parseError);
            // Skip invalid JSON files
            continue;
          }
        }
      }
    }

    return blogPosts;
  } catch (error) {
    console.error('Error reading blog posts:', error);
    return [];
  }
}

// Get all area service pages
async function getAreaServicePages(): Promise<SitemapEntry[]> {
  try {
    const servicePages: SitemapEntry[] = [];
    const appDir = path.join(process.cwd(), 'src/app');
    
    // Service directories to scan
    const serviceDirs = [
      'web-design',
      'seo-services', 
      'google-ads',
      'social-media',
      'app-development',
      'digital-marketing'
    ];

    for (const serviceDir of serviceDirs) {
      const servicePath = path.join(appDir, serviceDir);
      
      if (fs.existsSync(servicePath)) {
        const files = fs.readdirSync(servicePath);
        
        for (const file of files) {
          const filePath = path.join(servicePath, file);
          const stat = fs.statSync(filePath);
          
          // Skip non-directories and main service pages
          if (!stat.isDirectory() || file === 'page.tsx') {
            continue;
          }

          // Get file modification time
          const lastmod = stat.mtime.toISOString();
          
          servicePages.push({
            url: `/${serviceDir}/${file}`,
            priority: 0.6,
            changefreq: 'monthly',
            lastmod
          });
        }
      }
    }

    return servicePages;
  } catch (error) {
    console.error('Error reading area service pages:', error);
    return [];
  }
}

// Generate sitemap XML
function generateSitemapXML(pages: SitemapEntry[]) {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `  <url>
    <loc>${BASE_URL}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  return sitemap;
}

export async function GET() {
  try {
    console.log('🗺️ Generating dynamic sitemap...');
    
    // Collect all pages
    const allPages = [
      ...STATIC_PAGES,
      ...(await getBlogPosts()),
      ...(await getAreaServicePages())
    ];

    // Sort by priority (highest first)
    allPages.sort((a, b) => b.priority - a.priority);

    // Generate XML
    const sitemapXML = generateSitemapXML(allPages);

    const blogPages = allPages.filter(p => p.url.startsWith('/blog/'));
    const servicePages = allPages.filter(p => p.url.includes('-') && !p.url.startsWith('/blog/'));

    console.log(`✅ Sitemap generated with ${allPages.length} pages`);
    console.log(`📊 Static pages: ${STATIC_PAGES.length}`);
    console.log(`📝 Blog posts: ${blogPages.length}`);
    console.log(`🏢 Area service pages: ${servicePages.length}`);

    if (blogPages.length > 0) {
      console.log('📝 Blog post URLs found:');
      blogPages.forEach(page => console.log(`   - ${page.url}`));
    } else {
      console.log('⚠️ No blog posts found in sitemap');
    }

    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('💥 Error generating sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
