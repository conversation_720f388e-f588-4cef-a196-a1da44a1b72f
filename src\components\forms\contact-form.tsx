"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { CheckCircle, Send, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  message: string;
  service: string;
}

interface ContactFormProps {
  title?: string;
  subtitle?: string;
  className?: string;
  onSuccess?: () => void;
}

export default function ContactForm({ 
  title = "Send Us a Message",
  subtitle = "Fill out the form below and we'll get back to you within 2 hours.",
  className = "",
  onSuccess
}: ContactFormProps) {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
    service: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/send-contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send message');
      }

      setIsSubmitted(true);
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
    setError(null);
  };

  const resetForm = () => {
    setIsSubmitted(false);
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      message: '',
      service: ''
    });
    setError(null);
  };

  return (
    <div className={className}>
      <Card className="bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 rounded-3xl overflow-hidden hover:border-[#FF6B35]/30 transition-all duration-500">
        <CardContent className="p-10">
          {!isSubmitted ? (
            <>
              <div className="mb-10">
                <h3 className="text-3xl lg:text-4xl font-black text-[#1E3A8A] mb-4">{title}</h3>
                <p className="text-slate-600 text-lg font-medium">{subtitle}</p>
              </div>

              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-red-600 text-sm font-medium">{error}</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-base font-bold text-[#1E3A8A] mb-3">Name *</label>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Your full name"
                      required
                      className="h-14 border-2 border-slate-300 focus:border-[#FF6B35] focus:ring-[#FF6B35] rounded-xl text-base font-medium"
                    />
                  </div>
                  <div>
                    <label className="block text-base font-bold text-[#1E3A8A] mb-3">Email *</label>
                    <Input
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                      className="h-14 border-2 border-slate-300 focus:border-[#FF6B35] focus:ring-[#FF6B35] rounded-xl text-base font-medium"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">Phone</label>
                    <Input
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+44 20 1234 5678"
                      className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">Company</label>
                    <Input
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      placeholder="Your company name"
                      className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Service Interest</label>
                  <select
                    name="service"
                    value={formData.service}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-orange-500"
                  >
                    <option value="">Select a service</option>
                    <option value="seo">Local SEO</option>
                    <option value="ppc">Google Ads</option>
                    <option value="web-design">Web Design</option>
                    <option value="social-media">Social Media</option>
                    <option value="full-service">Full Service Package</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Message *</label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder="Tell us about your business and goals..."
                    rows={4}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-orange-500 focus:outline-none"
                  />
                </div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full h-16 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white text-xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/40 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 border-2 border-[#FF6B35]/20"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-4"></div>
                        Sending Message...
                      </>
                    ) : (
                      <>
                        <Send className="h-6 w-6 mr-4" />
                        Send Message
                        <ArrowRight className="h-6 w-6 ml-4" />
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center mx-auto mb-8 shadow-2xl shadow-[#4CAF50]/25">
                <CheckCircle className="h-12 w-12 text-white" />
              </div>
              <h3 className="text-3xl font-black text-[#1E3A8A] mb-6">Message Sent Successfully!</h3>
              <p className="text-slate-600 text-lg font-medium mb-8">
                Thank you for reaching out. We'll get back to you within 2 hours with a personalized response.
              </p>
              <Button
                onClick={resetForm}
                className="border-2 border-[#FF6B35] text-[#FF6B35] hover:bg-[#FF6B35] hover:text-white px-8 py-4 text-lg font-bold rounded-2xl transition-all duration-500"
              >
                Send Another Message
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
