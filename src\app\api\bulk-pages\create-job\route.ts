import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { saveBulkJob, type BulkGenerationJob, type GeneratedPage } from '@/lib/bulk-pages-storage';

// Create slug from keyword and location
function createSlug(keyword: string, location: string): string {
  const combined = `${keyword} ${location}`;
  return combined
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Create page title from keyword and location
function createPageTitle(keyword: string, location: string): string {
  // Capitalize first letter of each word
  const capitalizeWords = (str: string) => {
    return str.replace(/\b\w/g, l => l.toUpperCase());
  };
  
  return `Professional ${capitalizeWords(keyword)} Services in ${capitalizeWords(location)} | WebforLeads`;
}

export async function POST(request: NextRequest) {
  try {
    console.log('\n🚀 STARTING BULK JOB CREATION PROCESS');
    console.log('⏰ Timestamp:', new Date().toISOString());

    const body = await request.json();
    const { name, templateId, templateName, keywords, locations } = body;

    // Validation
    if (!name || !templateId || !templateName || !keywords || !locations) {
      return NextResponse.json(
        { success: false, error: 'Name, template, keywords, and locations are required' },
        { status: 400 }
      );
    }

    if (!Array.isArray(keywords) || !Array.isArray(locations)) {
      return NextResponse.json(
        { success: false, error: 'Keywords and locations must be arrays' },
        { status: 400 }
      );
    }

    if (keywords.length === 0 || locations.length === 0) {
      return NextResponse.json(
        { success: false, error: 'At least one keyword and one location are required' },
        { status: 400 }
      );
    }

    console.log('📝 Job Details:');
    console.log('🔖 Name:', name);
    console.log('🎨 Template:', templateName, `(${templateId})`);
    console.log('🔑 Keywords:', keywords.length, 'items');
    console.log('📍 Locations:', locations.length, 'items');

    // Generate job ID
    const jobId = uuidv4();
    console.log('🆔 Generated Job ID:', jobId);

    // Create pages array
    const pages: GeneratedPage[] = [];
    let pageIndex = 0;

    for (const keyword of keywords) {
      for (const location of locations) {
        const pageId = uuidv4();
        const slug = createSlug(keyword, location);
        const title = createPageTitle(keyword, location);

        pages.push({
          id: pageId,
          keyword: keyword.trim(),
          location: location.trim(),
          slug,
          title,
          status: 'pending',
          createdAt: new Date().toISOString(),
          progress: 0
        });

        pageIndex++;
      }
    }

    console.log('📄 Generated Pages:', pages.length);
    console.log('📊 Sample Pages:');
    pages.slice(0, 3).forEach((page, index) => {
      console.log(`  ${index + 1}. ${page.title}`);
      console.log(`     Slug: ${page.slug}`);
      console.log(`     Keyword: ${page.keyword}, Location: ${page.location}`);
    });

    // Create bulk generation job
    const job: BulkGenerationJob = {
      id: jobId,
      name: name.trim(),
      templateId: templateId.trim(),
      templateName: templateName.trim(),
      keywords: keywords.map((k: string) => k.trim()),
      locations: locations.map((l: string) => l.trim()),
      status: 'pending',
      progress: 0,
      totalPages: pages.length,
      completedPages: 0,
      createdAt: new Date().toISOString(),
      pages
    };

    // Save job to storage
    console.log('💾 Saving bulk generation job...');
    saveBulkJob(job);

    console.log('✅ BULK JOB CREATION COMPLETED SUCCESSFULLY');
    console.log('🎯 Job Summary:');
    console.log('   - Job ID:', job.id);
    console.log('   - Name:', job.name);
    console.log('   - Template:', job.templateName);
    console.log('   - Total Pages:', job.totalPages);
    console.log('   - Status:', job.status);
    console.log('   - Created:', job.createdAt);

    return NextResponse.json({
      success: true,
      message: 'Bulk generation job created successfully',
      job: {
        id: job.id,
        name: job.name,
        totalPages: job.totalPages,
        status: job.status,
        createdAt: job.createdAt
      }
    });

  } catch (error) {
    console.error('💥 BULK JOB CREATION ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create bulk generation job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bulk job creation API endpoint. Use POST to create a new bulk generation job.',
    endpoints: {
      'POST /api/bulk-pages/create-job': 'Create a new bulk generation job with keywords and locations'
    }
  });
}
