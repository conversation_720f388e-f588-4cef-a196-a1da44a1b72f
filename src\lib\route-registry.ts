import fs from 'fs';
import path from 'path';

// Route registry for tracking all generated pages
export interface GeneratedRoute {
  slug: string;
  title: string;
  templateId: string;
  templateName: string;
  keyword: string;
  location: string;
  filePath: string;
  createdAt: string;
  jobId: string;
  status: 'active' | 'inactive';
}

// Get the route registry storage directory
function getRouteRegistryDir(): string {
  const storageDir = path.join(process.cwd(), 'route-registry');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Save route to registry
export function registerRoute(route: GeneratedRoute): void {
  const storageDir = getRouteRegistryDir();
  const filename = `${route.slug}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    console.log('📝 Registering route...');
    console.log('🔗 Slug:', route.slug);
    console.log('📁 File:', filename);

    fs.writeFileSync(filepath, JSON.stringify(route, null, 2));
    console.log('✅ Route registered successfully');
  } catch (error) {
    console.error('❌ Error registering route:', error);
    throw new Error('Failed to register route');
  }
}

// Unregister route from registry
export function unregisterRoute(slug: string): boolean {
  const storageDir = getRouteRegistryDir();
  const filename = `${slug}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath);
      console.log('🗑️ Unregistered route:', slug);
      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Error unregistering route:', error);
    return false;
  }
}

// Get all registered routes
export function getAllRegisteredRoutes(): GeneratedRoute[] {
  const storageDir = getRouteRegistryDir();
  const routes: GeneratedRoute[] = [];

  try {
    const files = fs.readdirSync(storageDir);
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const filepath = path.join(storageDir, file);
        const content = fs.readFileSync(filepath, 'utf-8');
        const route = JSON.parse(content) as GeneratedRoute;
        routes.push(route);
      }
    }

    // Sort by created date (newest first)
    routes.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return routes;
  } catch (error) {
    console.error('❌ Error reading registered routes:', error);
    return [];
  }
}

// Get routes by job ID
export function getRoutesByJobId(jobId: string): GeneratedRoute[] {
  const allRoutes = getAllRegisteredRoutes();
  return allRoutes.filter(route => route.jobId === jobId);
}

// Update route status
export function updateRouteStatus(slug: string, status: 'active' | 'inactive'): boolean {
  const storageDir = getRouteRegistryDir();
  const filename = `${slug}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf-8');
      const route = JSON.parse(content) as GeneratedRoute;
      route.status = status;
      
      fs.writeFileSync(filepath, JSON.stringify(route, null, 2));
      console.log(`✅ Updated route status: ${slug} -> ${status}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Error updating route status:', error);
    return false;
  }
}
