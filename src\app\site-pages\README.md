# Service Page Template System

This folder contains a comprehensive template system for creating inner service pages that replicate the web design service page functionality while being easily customizable for different services.

## 📁 Files Overview

- **`service-template.tsx`** - Main template component with all service page functionality
- **`README.md`** - This documentation file

## 🎯 Template Features

### ✅ Complete Service Page Replication
- **Hero Section** with customizable content and appointment form
- **Main Features Section** highlighting service benefits
- **Pricing Packages** with customizable tiers
- **Featured Testimonial** with client success story
- **FAQ Categories** with expandable questions
- **Contact Information** and call-to-action sections

### ✅ SEO Optimization
- Customizable meta titles and descriptions
- Service-specific keywords integration
- Structured content hierarchy
- Schema markup ready for local business

### ✅ Design Consistency
- Matches existing service page design patterns exactly
- Consistent color scheme (Navy Blue #1E3A8A + Orange #FF6B35)
- Same animation and interaction patterns
- Responsive design for all devices

### ✅ Easy Customization
- Single configuration object for all customizations
- Service-specific content variables
- Pricing packages and features
- FAQ categories and questions

## 🚀 Quick Start Guide

### 1. Basic Usage

```tsx
import ServiceTemplate from '../site-pages/service-template';

export default function MyServicePage() {
  const config = {
    serviceName: "Your Service",
    serviceSlug: "your-service",
    heroTitle: "Your Service Title",
    heroSubtitle: "Your Service Subtitle",
    // ... other config options
  };

  return <ServiceTemplate config={config} />;
}
```

### 2. Configuration Options

```tsx
interface ServiceConfig {
  // Service Details
  serviceName: string;           // e.g., "SEO Services"
  serviceSlug: string;           // e.g., "seo-services"
  
  // SEO & Meta
  metaTitle: string;             // SEO title
  metaDescription: string;       // SEO description
  keywords: string[];            // Array of keywords
  
  // Hero Section
  heroTitle: string;             // Main headline
  heroSubtitle: string;          // Secondary headline
  heroDescription: string;       // Hero description
  heroBadgeText: string;         // Badge text
  
  // Features & Benefits
  mainFeatures: Array<{
    icon: React.ReactNode;       // Lucide icon component
    title: string;               // Feature title
    description: string;         // Feature description
    highlight: string;           // Highlight badge text
  }>;
  
  // Service Packages
  packages: Array<{
    name: string;                // Package name
    price: string;               // Package price
    period: string;              // Billing period (optional)
    description: string;         // Package description
    features: string[];          // Array of features
    highlight: boolean;          // Is this the featured package?
    cta: string;                 // Call-to-action button text
  }>;
  
  // Testimonials
  featuredTestimonial: {
    quote: string;               // Testimonial text
    name: string;                // Client name
    role: string;                // Client role
    company: string;             // Client company
    rating: number;              // Star rating (1-5)
  };
  
  // FAQ Categories
  faqCategories: Array<{
    category: string;            // Category name
    icon: React.ReactNode;       // Category icon
    questions: Array<{
      q: string;                 // Question
      a: string;                 // Answer
    }>;
  }>;
}
```

## 📋 Implementation Steps

### Step 1: Create New Service Page
```bash
# Create new page directory
mkdir src/app/your-service-name

# Create page.tsx file
touch src/app/your-service-name/page.tsx
```

### Step 2: Configure Template
```tsx
// src/app/seo-services/page.tsx
import { Metadata } from 'next';
import ServiceTemplate from '../site-pages/service-template';
import { Search, TrendingUp, Target, BarChart3 } from "lucide-react";

const seoConfig = {
  serviceName: "SEO Services",
  serviceSlug: "seo-services",
  
  metaTitle: "Professional SEO Services | Search Engine Optimization Company",
  metaDescription: "Leading SEO company providing comprehensive search engine optimization services to improve your website rankings and drive organic traffic.",
  keywords: ["SEO services", "search engine optimization", "SEO company"],
  
  heroTitle: "Professional SEO Services That Rank",
  heroSubtitle: "Search Engine Optimization & Digital Visibility",
  heroDescription: "Dominate search results with our proven SEO strategies...",
  heroBadgeText: "PROVEN SEO RESULTS",
  
  mainFeatures: [
    {
      icon: <Search className="h-8 w-8" />,
      title: "Technical SEO Audit",
      description: "Comprehensive analysis of your website's technical foundation...",
      highlight: "Technical"
    },
    // ... more features
  ],
  
  packages: [
    {
      name: "SEO Starter",
      price: "£1,500",
      period: "month",
      description: "Perfect for small businesses starting their SEO journey",
      features: [
        "Keyword research & strategy",
        "On-page optimization",
        "Technical SEO audit",
        "Monthly reporting"
      ],
      highlight: false,
      cta: "Start SEO Journey"
    },
    // ... more packages
  ],
  
  // ... rest of configuration
};

export const metadata: Metadata = {
  title: seoConfig.metaTitle,
  description: seoConfig.metaDescription,
  keywords: seoConfig.keywords.join(", "),
};

export default function SEOServicesPage() {
  return <ServiceTemplate config={seoConfig} />;
}
```

### Step 3: Test and Deploy
1. Run development server: `npm run dev`
2. Navigate to `/your-service-name`
3. Verify all content displays correctly
4. Test form functionality
5. Check responsive design

## 🎨 Design System

### Color Palette
- **Primary Navy**: `#1E3A8A`
- **Secondary Navy**: `#1E40AF`
- **Primary Orange**: `#FF6B35`
- **Secondary Orange**: `#FF8A65`
- **Background**: `white` and `slate-50/50`

### Typography
- **Headings**: Font-black, tracking-tight
- **Body**: Font-medium, leading-relaxed
- **Buttons**: Font-bold/black

### Components
- **Badge**: Gradient backgrounds with icons
- **Card**: White background, rounded-3xl, shadow-2xl
- **Button**: Gradient orange, hover effects
- **Form**: Clean inputs with focus states

## 📊 Available Service Templates

### Current Example
- **Web Design Template** - `/web-design-template`

### Potential Services to Create
- SEO Services
- Digital Marketing
- App Development
- E-commerce Development
- Branding & Design
- Content Marketing
- Social Media Marketing
- PPC Advertising

## 🔧 Customization Guide

### Adding New Sections
1. Add section to template component
2. Include in configuration interface
3. Update documentation
4. Test across different configurations

### Modifying Existing Sections
1. Edit the relevant section in `service-template.tsx`
2. Ensure configuration variables are properly used
3. Maintain design consistency
4. Update documentation

### Icon Usage
Use Lucide React icons for consistency:
```tsx
import { Search, TrendingUp, Target } from "lucide-react";

// In your config
icon: <Search className="h-8 w-8" />
```

## 📈 Performance Optimization

### Built-in Optimizations
- Framer Motion animations
- Lazy loading for images
- Optimized component structure
- Minimal re-renders

### Recommendations
- Use Next.js Image component for images
- Implement proper caching strategies
- Monitor Core Web Vitals
- Regular performance audits

## 🚀 Deployment

### Production Checklist
- [ ] All configurations tested
- [ ] SEO metadata verified
- [ ] Form submissions working
- [ ] Analytics tracking implemented
- [ ] Performance optimized
- [ ] Mobile responsive confirmed

### Monitoring
- Track page performance
- Monitor conversion rates
- Analyze user behavior
- A/B test different configurations

## 📞 Support

For questions or issues with the template system:
1. Check this documentation
2. Review the web-design-template example
3. Test with default configuration
4. Contact development team

---

**Last Updated**: August 2025
**Version**: 1.0.0
**Compatibility**: Next.js 14+, React 18+
