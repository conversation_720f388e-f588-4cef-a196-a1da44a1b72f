import fs from 'fs';
import path from 'path';

export interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    canonicalUrl: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterTitle: string;
    twitterDescription: string;
    twitterImage: string;
    author: string;
    category: string;
    tags: string[];
    readingTime: number;
    wordCount: number;
  };
}

// Get the blog storage directory
function getBlogStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'blog-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Save blog post to file system
export function saveBlogPost(blogPost: BlogPost): void {
  const storageDir = getBlogStorageDir();
  const filename = `${blogPost.slug}-${Date.now()}.json`;
  const filepath = path.join(storageDir, filename);

  try {
    console.log('💾 Saving blog post...');
    console.log('🔖 Title:', blogPost.title);
    console.log('🔗 Slug:', blogPost.slug);
    console.log('🕒 PublishedAt:', blogPost.publishedAt);
    console.log('🧠 SEO keys present:', Object.keys(blogPost.seo));
    console.log('🖼️ Images count:', Object.keys(blogPost.images || {}).length);
    console.log('📁 Path:', filepath);

    const payload = JSON.stringify(blogPost, null, 2);
    fs.writeFileSync(filepath, payload);

    const stats = fs.statSync(filepath);
    console.log('✅ Saved blog post file');
    console.log('📏 File size (bytes):', stats.size);
    console.log('📅 File mtime:', stats.mtime.toISOString());
  } catch (error) {
    console.error('❌ Failed to save blog post:', error);
    throw error;
  }
}

// Get existing blog titles for blacklist
export function getExistingBlogTitles(): string[] {
  try {
    const storageDir = getBlogStorageDir();

    if (!fs.existsSync(storageDir)) {
      return [];
    }

    const files = fs.readdirSync(storageDir);
    const existingTitles: string[] = [];

    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filepath = path.join(storageDir, file);
          const content = fs.readFileSync(filepath, 'utf-8');
          const blogPost = JSON.parse(content) as BlogPost;
          existingTitles.push(blogPost.title);
        } catch (error) {
          console.error(`Error reading blog post file ${file}:`, error);
          // Continue with other files
        }
      }
    }

    console.log(`📚 Found ${existingTitles.length} existing blog post titles for blacklist`);
    if (existingTitles.length > 0) {
      console.log('🔍 Sample existing titles:', existingTitles.slice(0, 3).map(title => `"${title}"`).join(', '));
    }
    return existingTitles;
  } catch (error) {
    console.error('Error getting existing blog titles:', error);
    return [];
  }
}
