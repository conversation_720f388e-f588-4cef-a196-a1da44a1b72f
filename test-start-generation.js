// Test script to debug the start-generation API
const testData = {
  jobId: "d2bcce44-9161-4771-b1d6-4818a51d53dc" // Use the job ID from the previous test
};

fetch('http://localhost:3000/api/bulk-pages/start-generation', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(testData)
})
.then(response => {
  console.log('Response status:', response.status);
  return response.json();
})
.then(data => {
  console.log('Response data:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
