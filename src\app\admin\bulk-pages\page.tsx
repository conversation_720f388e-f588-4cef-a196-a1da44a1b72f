"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Zap, Loader2, RefreshCw, BarChart3, Globe, MapPin,
  CheckCircle, AlertCircle, Clock, Target, Rocket,
  FileText, Settings, Trash2, Download, Upload,
  Play, Pause, Square, Eye, Edit, Copy, ExternalLink,
  BookTemplate, Layers, Sparkles
} from "lucide-react";
import { SERVICE_TEMPLATES, getTemplateById, type TemplateConfig } from '../../site-pages/template-registry';

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.21, 1, 0.81, 1] as const
    }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1, delayChildren: 0.1 }
  }
};

// Interfaces moved to @/lib/bulk-pages-storage.ts

// BulkGenerationJob interface moved to @/lib/bulk-pages-storage.ts

export default function BulkPagesAdminPage() {
  // State management
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [jobName, setJobName] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [keywords, setKeywords] = useState('');
  const [locations, setLocations] = useState('');
  const [isCreatingJob, setIsCreatingJob] = useState(false);

  // Progress tracking
  const [isGenerating, setIsGenerating] = useState(false);
  const [, setCurrentJobId] = useState<string | null>(null);
  const [generationProgress, setGenerationProgress] = useState({
    completed: 0,
    total: 0,
    currentPage: '',
    status: 'pending' as 'pending' | 'generating' | 'completed' | 'error'
  });



  // Create new bulk generation job
  const createBulkJob = async () => {
    if (!jobName.trim() || !selectedTemplate || !keywords.trim() || !locations.trim()) {
      setError('Please fill in all fields including template selection');
      return;
    }

    try {
      setIsCreatingJob(true);
      setError(null);
      setSuccess(null);

      const keywordList = keywords.split('\n').map(k => k.trim()).filter(k => k);
      const locationList = locations.split('\n').map(l => l.trim()).filter(l => l);

      const selectedTemplateConfig = getTemplateById(selectedTemplate);

      const response = await fetch('/api/bulk-pages/create-job', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: jobName,
          templateId: selectedTemplate,
          templateName: selectedTemplateConfig?.name || 'Unknown Template',
          keywords: keywordList,
          locations: locationList,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        const jobId = data.job.id;
        setSuccess(`Bulk job created successfully! ${data.job.totalPages} pages will be generated using ${selectedTemplateConfig?.name} template. Starting generation...`);

        // Clear form
        setJobName('');
        setSelectedTemplate('');
        setKeywords('');
        setLocations('');

        // Automatically start generation
        try {
          setIsGenerating(true);
          setCurrentJobId(jobId);
          setGenerationProgress({
            completed: 0,
            total: data.job.totalPages,
            currentPage: 'Starting generation...',
            status: 'generating'
          });

          const startResponse = await fetch('/api/bulk-pages/start-generation', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ jobId }),
          });

          const startData = await startResponse.json();

          if (startData.success) {
            setSuccess(`Generation started automatically! ${data.job.totalPages} pages are being generated using ${selectedTemplateConfig?.name} template.`);
            // Start polling for progress
            startProgressPolling(jobId);
          } else {
            setError(`Job created but failed to start generation: ${startData.error}`);
            setIsGenerating(false);
            setCurrentJobId(null);
          }
        } catch (startErr) {
          setError('Job created but failed to start generation automatically. Please try again.');
          setIsGenerating(false);
          setCurrentJobId(null);
          console.error('Error starting generation:', startErr);
        }
      } else {
        setError(data.error || 'Failed to create bulk job');
      }
    } catch (err) {
      setError('Failed to create bulk job');
      console.error('Error creating bulk job:', err);
    } finally {
      setIsCreatingJob(false);
    }
  };

  // Start progress polling
  const startProgressPolling = (jobId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/bulk-pages/progress?jobId=${jobId}`);
        const data = await response.json();

        if (data.success) {
          setGenerationProgress({
            completed: data.completedPages || 0,
            total: data.totalPages || 0,
            currentPage: data.currentPage || 'Generating...',
            status: data.status || 'generating'
          });

          // Stop polling if job is completed
          if (data.status === 'completed' || data.status === 'error') {
            clearInterval(pollInterval);
            setIsGenerating(false);
            setCurrentJobId(null);

            if (data.status === 'completed') {
              setSuccess(`🎉 Generation completed! ${data.completedPages} pages have been successfully generated.`);
            } else {
              setError(`Generation failed: ${data.error || 'Unknown error'}`);
            }
          }
        }
      } catch (err) {
        console.error('Error polling progress:', err);
        clearInterval(pollInterval);
        setIsGenerating(false);
        setCurrentJobId(null);
      }
    }, 2000); // Poll every 2 seconds

    // Clean up interval after 30 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      setIsGenerating(false);
      setCurrentJobId(null);
    }, 30 * 60 * 1000);
  };









  // Template selection helpers
  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = getTemplateById(templateId);
    if (template) {
      // Auto-populate with default keywords if current keywords are empty
      if (!keywords.trim()) {
        setKeywords(template.defaultKeywords.slice(0, 5).join('\n'));
      }
      // Auto-populate with example locations if current locations are empty
      if (!locations.trim()) {
        setLocations(template.exampleLocations.slice(0, 10).join('\n'));
      }
    }
  };

  const getSelectedTemplateConfig = () => {
    return selectedTemplate ? getTemplateById(selectedTemplate) : null;
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Header */}
      <section className="py-12 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="text-center"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/20 via-white/10 to-white/20 text-white border border-white/20 rounded-full">
                <Rocket className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                <span className="font-bold text-sm tracking-wide">BULK PAGE GENERATION SYSTEM</span>
              </Badge>

              <h1 className="text-4xl lg:text-6xl font-black text-white leading-tight">
                Bulk Service Pages
                <span className="block text-[#FF6B35]">Generation Admin</span>
              </h1>

              <p className="text-xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
                Create thousands of location-based service pages automatically using AI-powered content generation with your service template.
              </p>
            </motion.div>


          </motion.div>
        </div>
      </section>

      {/* Error/Success Messages */}
      {(error || success) && (
        <section className="py-4">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4"
              >
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                  <p className="text-red-700 font-medium">{error}</p>
                  <Button
                    onClick={() => setError(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-red-500 hover:text-red-700"
                  >
                    ×
                  </Button>
                </div>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-50 border border-green-200 rounded-xl p-4 mb-4"
              >
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <p className="text-green-700 font-medium">{success}</p>
                  <Button
                    onClick={() => setSuccess(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-green-500 hover:text-green-700"
                  >
                    ×
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        </section>
      )}

      {/* Live Progress Tracking */}
      {isGenerating && (
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8"
            >
              <div className="text-center mb-6">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <Loader2 className="h-8 w-8 animate-spin text-[#1E3A8A]" />
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">Generation in Progress</h3>
                </div>
                <p className="text-gray-600">
                  {generationProgress.currentPage}
                </p>
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    Progress: {generationProgress.completed} of {generationProgress.total} pages
                  </span>
                  <span className="text-sm text-gray-500">
                    {Math.round((generationProgress.completed / generationProgress.total) * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-[#1E3A8A] to-[#FF6B35] h-3 rounded-full transition-all duration-500 ease-out"
                    style={{
                      width: `${Math.round((generationProgress.completed / generationProgress.total) * 100)}%`
                    }}
                  ></div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-[#1E3A8A]">{generationProgress.completed}</div>
                  <div className="text-sm text-gray-600">Completed</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-[#FF6B35]">{generationProgress.total - generationProgress.completed}</div>
                  <div className="text-sm text-gray-600">Remaining</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600">{generationProgress.total}</div>
                  <div className="text-sm text-gray-600">Total</div>
                </div>
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500">
                  ⚡ Pages are being generated automatically. This page will update in real-time.
                </p>
              </div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Create New Job Form */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-[#1E3A8A] mb-4">Create & Start Bulk Generation</h2>
                    <p className="text-gray-600">Generate hundreds of location-based service pages automatically - generation starts immediately after creation</p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Job Name
                      </label>
                      <Input
                        type="text"
                        value={jobName}
                        onChange={(e) => setJobName(e.target.value)}
                        placeholder="e.g., Web Design UK Cities Q4 2024"
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Service Template
                      </label>
                      <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select a service template..." />
                        </SelectTrigger>
                        <SelectContent>
                          {SERVICE_TEMPLATES.map((template) => (
                            <SelectItem key={template.id} value={template.id}>
                              <div className="flex items-center space-x-3">
                                <BookTemplate className="h-4 w-4 text-[#1E3A8A]" />
                                <div>
                                  <div className="font-medium">{template.name}</div>
                                  <div className="text-xs text-gray-500">{template.category}</div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {getSelectedTemplateConfig() && (
                        <div className="mt-3 p-4 bg-slate-50 rounded-lg">
                          <div className="flex items-start space-x-3">
                            <Layers className="h-5 w-5 text-[#FF6B35] mt-0.5" />
                            <div>
                              <h4 className="font-medium text-gray-900">{getSelectedTemplateConfig()?.name}</h4>
                              <p className="text-sm text-gray-600 mt-1">{getSelectedTemplateConfig()?.description}</p>
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span>Category: {getSelectedTemplateConfig()?.category}</span>
                                <span>•</span>
                                <span>Pricing: {getSelectedTemplateConfig()?.pricing.starter} - {getSelectedTemplateConfig()?.pricing.enterprise}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Keywords (one per line)
                        </label>
                        <Textarea
                          value={keywords}
                          onChange={(e) => setKeywords(e.target.value)}
                          placeholder={`web design
website development
digital marketing
SEO services
app development`}
                          rows={8}
                          className="w-full"
                        />
                        <p className="text-sm text-gray-500 mt-2">
                          {keywords.split('\n').filter(k => k.trim()).length} keywords
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Locations (one per line)
                        </label>
                        <Textarea
                          value={locations}
                          onChange={(e) => setLocations(e.target.value)}
                          placeholder={`London
Manchester
Birmingham
Leeds
Liverpool
Bristol
Sheffield
Edinburgh
Glasgow
Cardiff`}
                          rows={8}
                          className="w-full"
                        />
                        <p className="text-sm text-gray-500 mt-2">
                          {locations.split('\n').filter(l => l.trim()).length} locations
                        </p>
                      </div>
                    </div>

                    <div className="bg-slate-50 rounded-xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">Generation Preview</h3>
                          <p className="text-gray-600">
                            This will create{' '}
                            <span className="font-bold text-[#FF6B35]">
                              {keywords.split('\n').filter(k => k.trim()).length * locations.split('\n').filter(l => l.trim()).length}
                            </span>{' '}
                            pages
                            {getSelectedTemplateConfig() && (
                              <span className="text-sm text-gray-500 block mt-1">
                                using {getSelectedTemplateConfig()?.name} template
                              </span>
                            )}
                          </p>
                        </div>
                        <Target className="h-8 w-8 text-[#1E3A8A]" />
                      </div>

                      {getSelectedTemplateConfig() && keywords.trim() && locations.trim() && (
                        <div className="border-t pt-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Example Generated Pages:</h4>
                          <div className="space-y-2">
                            {keywords.split('\n').filter(k => k.trim()).slice(0, 2).map((keyword, ki) =>
                              locations.split('\n').filter(l => l.trim()).slice(0, 2).map((location, li) => (
                                <div key={`${ki}-${li}`} className="text-xs bg-white rounded px-3 py-2 border">
                                  <div className="font-medium text-gray-900">
                                    /{keyword.trim().toLowerCase().replace(/\s+/g, '-')}-{location.trim().toLowerCase().replace(/\s+/g, '-')}
                                  </div>
                                  <div className="text-gray-500 mt-1">
                                    Professional {keyword.trim()} Services in {location.trim()} | WebforLeads
                                  </div>
                                </div>
                              ))
                            )}
                            {(keywords.split('\n').filter(k => k.trim()).length * locations.split('\n').filter(l => l.trim()).length) > 4 && (
                              <div className="text-xs text-gray-500 italic">
                                + {(keywords.split('\n').filter(k => k.trim()).length * locations.split('\n').filter(l => l.trim()).length) - 4} more pages...
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={createBulkJob}
                      disabled={isCreatingJob || !jobName.trim() || !selectedTemplate || !keywords.trim() || !locations.trim()}
                      className="w-full bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white py-4 text-lg font-bold rounded-xl"
                    >
                      {isCreatingJob ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Creating & Starting Generation...
                        </>
                      ) : (
                        <>
                          <Zap className="h-5 w-5 mr-2" />
                          Create & Start Generation
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>


    </div>
  );
}
