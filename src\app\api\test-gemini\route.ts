import { NextRequest, NextResponse } from 'next/server';

// Environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_TEXT_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

export async function GET() {
  try {
    console.log('🧪 Testing Gemini API connection...');
    console.log('🔑 API Key exists:', !!GEMINI_API_KEY);
    console.log('🔑 API Key length:', GEMINI_API_KEY?.length || 0);

    if (!GEMINI_API_KEY) {
      return NextResponse.json({
        success: false,
        error: 'GEMINI_API_KEY environment variable not set'
      }, { status: 500 });
    }

    const testPrompt = 'Generate a simple JSON object with a "message" field containing "Hello World"';

    const response = await fetch(`${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: testPrompt }]
        }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000,
        }
      }),
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response status text:', response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Gemini API error response:', errorText);
      return NextResponse.json({
        success: false,
        error: `Gemini API error: ${response.status} ${response.statusText}`,
        details: errorText
      }, { status: 500 });
    }

    const data = await response.json();
    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

    console.log('✅ Gemini API test successful');
    console.log('📝 Generated text:', generatedText);

    return NextResponse.json({
      success: true,
      message: 'Gemini API is working correctly',
      apiKeyConfigured: true,
      generatedText,
      fullResponse: data
    });

  } catch (error) {
    console.error('💥 Gemini API test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to test Gemini API',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
