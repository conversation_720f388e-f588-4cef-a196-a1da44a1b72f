"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, ChevronDown, Calendar, ArrowRight, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface NavItem {
  label: string;
  href: string;
  hasDropdown?: boolean;
  dropdownItems?: { label: string; href: string }[];
}

const navItems: NavItem[] = [
  {
    label: "Services",
    href: "/services",
    hasDropdown: true,
    dropdownItems: [
      { label: "Web Design & Development", href: "/services/web-design" },
      { label: "Search Engine Optimisation", href: "/services/seo" },
      { label: "Google Ads Management", href: "/services/google-ads" },
      { label: "Social Media Marketing", href: "/services/social-media" },
      { label: "Software & Mobile App Development", href: "/services/app-development" },
    ],
  },
  { label: "Our Blueprint", href: "/blueprint" },
  { label: "Blog", href: "/blog" },
  { label: "Locations", href: "/locations" },
  { label: "About", href: "/about" },
  { label: "Contact", href: "/contact" },
];

export default function HeaderNew() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const fadeInDown = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  return (
    <motion.header
      initial="hidden"
      animate="visible"
      variants={fadeInDown}
      className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-6xl px-4"
    >
      {/* Rounded Container with White Mirror Background */}
      <div className={`relative transition-all duration-500 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-xl shadow-2xl shadow-[#1E3A8A]/10 border border-[#1E3A8A]/10'
          : 'bg-white/90 backdrop-blur-lg shadow-xl shadow-[#1E3A8A]/5 border border-white/20'
      } rounded-3xl`}>
        
        {/* Premium Mirror Effect Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/60 via-white/40 to-white/60 opacity-50" />
        <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5 opacity-30" />
        
        {/* Subtle Border Glow */}
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-[#1E3A8A]/20 via-[#FF6B35]/20 to-[#1E3A8A]/20 opacity-0 hover:opacity-100 transition-opacity duration-700 pointer-events-none" style={{ padding: '1px' }}>
          <div className="w-full h-full bg-white/95 rounded-3xl" />
        </div>

        <div className="relative px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-18">

            {/* Premium Logo */}
            <motion.div variants={fadeInDown} className="flex-shrink-0">
              <Link href="/" className="flex items-center space-x-3 group">
                <div className="relative">
                  <div className="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-[#1E3A8A] to-[#1E3A8A]/80 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-[#1E3A8A]/25 transition-all duration-300 group-hover:scale-105">
                    <span className="text-white font-bold text-lg lg:text-xl">W</span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A] to-[#1E3A8A]/80 rounded-2xl opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300" />
                </div>
                <div className="hidden sm:block">
                  <div className="space-y-0.5">
                    <span className="text-xl lg:text-2xl font-bold text-[#1E3A8A] block leading-none">
                      WebforLeads
                    </span>
                    <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-[#FF6B35]/10 text-[#FF6B35] border-[#FF6B35]/20">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Premium
                    </Badge>
                  </div>
                </div>
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <motion.nav
              variants={staggerContainer}
              className="hidden lg:flex items-center space-x-1"
            >
              {navItems.map((item) => (
                <motion.div
                  key={item.label}
                  variants={fadeInDown}
                  className="relative group"
                  onMouseEnter={() => item.hasDropdown && setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  <Link
                    href={item.href}
                    className="flex items-center space-x-1 px-3 py-2 text-[#1E3A8A] hover:text-[#FF6B35] font-medium transition-all duration-300 rounded-xl hover:bg-[#FF6B35]/5 focus:outline-none focus:ring-2 focus:ring-[#FF6B35]/50"
                  >
                    <span>{item.label}</span>
                    {item.hasDropdown && (
                      <ChevronDown className="h-4 w-4 transition-transform duration-300 group-hover:rotate-180" />
                    )}
                  </Link>

                  {/* Premium Dropdown */}
                  {item.hasDropdown && (
                    <AnimatePresence>
                      {activeDropdown === item.label && (
                        <motion.div
                          initial={{ opacity: 0, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.95 }}
                          transition={{ duration: 0.2, ease: [0.21, 1, 0.81, 1] as const }}
                          className="absolute top-full left-0 mt-3 w-72 bg-white/95 backdrop-blur-xl border border-[#1E3A8A]/10 rounded-2xl shadow-2xl py-3 z-[60]"
                        >
                          <div className="space-y-1 px-3">
                            {item.dropdownItems?.map((dropdownItem, index) => (
                              <motion.div
                                key={dropdownItem.label}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.05 }}
                              >
                                <Link
                                  href={dropdownItem.href}
                                  className="flex items-center justify-between p-3 text-[#1E3A8A] hover:text-[#FF6B35] hover:bg-[#FF6B35]/5 rounded-xl transition-all duration-300 group focus:outline-none focus:ring-2 focus:ring-[#FF6B35]/50"
                                >
                                  <span className="font-medium">{dropdownItem.label}</span>
                                  <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1" />
                                </Link>
                              </motion.div>
                            ))}
                          </div>
                          <Separator className="my-2 mx-3" />
                          <div className="px-3">
                            <div className="text-xs text-[#1E3A8A]/60 px-3 py-2">
                              Premium digital solutions
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </motion.div>
              ))}
            </motion.nav>

            {/* Premium CTA Button */}
            <motion.div variants={fadeInDown} className="hidden lg:flex items-center">
              <Button
                asChild
                className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white px-6 py-2.5 rounded-xl font-semibold shadow-lg hover:shadow-[#FF6B35]/25 hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#FF6B35]/50 focus:ring-offset-2"
              >
                <Link href="/contact">
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Free Call
                </Link>
              </Button>
            </motion.div>

            {/* Mobile Menu Button */}
            <motion.button
              variants={fadeInDown}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2.5 rounded-xl bg-[#1E3A8A]/5 hover:bg-[#FF6B35]/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#FF6B35]/50"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="h-5 w-5 text-[#1E3A8A]" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-5 w-5 text-[#1E3A8A]" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: [0.21, 1, 0.81, 1] as const }}
              className="lg:hidden border-t border-[#1E3A8A]/10 bg-white/95 backdrop-blur-xl rounded-b-3xl"
            >
              <div className="px-6 py-4">
                <nav className="space-y-2">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.label}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        href={item.href}
                        className="flex items-center justify-between p-3 text-[#1E3A8A] hover:text-[#FF6B35] hover:bg-[#FF6B35]/5 rounded-xl transition-all duration-300 font-medium focus:outline-none focus:ring-2 focus:ring-[#FF6B35]/50"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <span>{item.label}</span>
                        {item.hasDropdown && <ChevronDown className="h-4 w-4" />}
                      </Link>
                      
                      {item.hasDropdown && (
                        <div className="ml-4 mt-2 space-y-1">
                          {item.dropdownItems?.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.label}
                              href={dropdownItem.href}
                              className="block p-2.5 text-[#1E3A8A]/70 hover:text-[#FF6B35] hover:bg-[#FF6B35]/5 rounded-lg transition-all duration-300"
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              {dropdownItem.label}
                            </Link>
                          ))}
                        </div>
                      )}
                    </motion.div>
                  ))}
                  
                  <Separator className="my-4" />

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: navItems.length * 0.1 }}
                    className="pt-2"
                  >
                    <Button
                      asChild
                      className="w-full bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white py-3 rounded-xl font-semibold shadow-lg hover:scale-105 transition-all duration-300"
                    >
                      <Link href="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                        <Calendar className="h-4 w-4 mr-2" />
                        Book Free Call
                      </Link>
                    </Button>
                  </motion.div>
                </nav>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
}