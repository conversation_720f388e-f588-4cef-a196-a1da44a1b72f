import fs from 'fs';
import path from 'path';

// Ensure the blog-images directory exists
export function ensureImageDirectory() {
  const imageDir = path.join(process.cwd(), 'public', 'blog-images');
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
  }
  return imageDir;
}

// Save base64 image as PNG file and return the filename
export function saveBase64Image(base64Data: string, prefix: string = 'image'): string {
  try {
    // Ensure directory exists
    const imageDir = ensureImageDirectory();
    
    // Generate unique filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 8);
    const filename = `${prefix}-${timestamp}-${randomId}.png`;
    const filePath = path.join(imageDir, filename);
    
    // Convert base64 to buffer and save
    const imageBuffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(filePath, imageBuffer);
    
    console.log(`✅ Image saved: ${filename} (${imageBuffer.length} bytes)`);
    return filename;
  } catch (error) {
    console.error('❌ Error saving image:', error);
    throw new Error('Failed to save image');
  }
}

// Get image URL for serving
export function getImageUrl(filename: string): string {
  return `/api/images/${filename}`;
}

// Clean up old images (optional - for maintenance)
export function cleanupOldImages(daysOld: number = 30) {
  try {
    const imageDir = path.join(process.cwd(), 'public', 'blog-images');
    if (!fs.existsSync(imageDir)) return;
    
    const files = fs.readdirSync(imageDir);
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    
    files.forEach(file => {
      const filePath = path.join(imageDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime.getTime() < cutoffTime) {
        fs.unlinkSync(filePath);
        console.log(`🗑️ Cleaned up old image: ${file}`);
      }
    });
  } catch (error) {
    console.error('❌ Error cleaning up images:', error);
  }
}
