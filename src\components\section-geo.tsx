"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, Users, TrendingUp, Award, Star, ArrowRight, CheckCircle, Sparkles, Target, Building } from "lucide-react";


interface City {
  id: string;
  name: string;
  slug: string;
  clients: number;
  projects: number;
  growth: string;
  featured: boolean;
}

const allUKCities: City[] = [
  { id: "london", name: "London", slug: "london", clients: 150, projects: 320, growth: "+45%", featured: true },
  { id: "birmingham", name: "Birmingham", slug: "birmingham", clients: 85, projects: 180, growth: "+38%", featured: true },
  { id: "manchester", name: "Manchester", slug: "manchester", clients: 92, projects: 195, growth: "+42%", featured: true },
  { id: "leeds", name: "Leeds", slug: "leeds", clients: 67, projects: 145, growth: "+35%", featured: true },
  { id: "glasgow", name: "Glasgow", slug: "glasgow", clients: 54, projects: 115, growth: "+40%", featured: true },
  { id: "edinburgh", name: "Edinburgh", slug: "edinburgh", clients: 48, projects: 98, growth: "+33%", featured: true },
  { id: "bristol", name: "Bristol", slug: "bristol", clients: 41, projects: 87, growth: "+29%", featured: true },
  { id: "liverpool", name: "Liverpool", slug: "liverpool", clients: 39, projects: 82, growth: "+31%", featured: true },
  { id: "newcastle", name: "Newcastle upon Tyne", slug: "newcastle", clients: 35, projects: 74, growth: "+28%", featured: false },
  { id: "nottingham", name: "Nottingham", slug: "nottingham", clients: 32, projects: 68, growth: "+26%", featured: false },
  { id: "sheffield", name: "Sheffield", slug: "sheffield", clients: 29, projects: 61, growth: "+24%", featured: false },
  { id: "cardiff", name: "Cardiff", slug: "cardiff", clients: 27, projects: 57, growth: "+22%", featured: false },
  { id: "leicester", name: "Leicester", slug: "leicester", clients: 24, projects: 52, growth: "+20%", featured: false },
  { id: "coventry", name: "Coventry", slug: "coventry", clients: 22, projects: 48, growth: "+18%", featured: false },
  { id: "bradford", name: "Bradford", slug: "bradford", clients: 20, projects: 44, growth: "+16%", featured: false },
  { id: "brighton", name: "Brighton and Hove", slug: "brighton", clients: 18, projects: 40, growth: "+14%", featured: false },
  { id: "plymouth", name: "Plymouth", slug: "plymouth", clients: 16, projects: 36, growth: "+12%", featured: false },
  { id: "wolverhampton", name: "Wolverhampton", slug: "wolverhampton", clients: 14, projects: 32, growth: "+10%", featured: false },
  { id: "southampton", name: "Southampton", slug: "southampton", clients: 12, projects: 28, growth: "+8%", featured: false },
  { id: "portsmouth", name: "Portsmouth", slug: "portsmouth", clients: 10, projects: 24, growth: "+6%", featured: false }
];

export default function SectionGeo() {
  const [selectedCity, setSelectedCity] = useState<string | null>("london");
  const [mapStats, setMapStats] = useState({ clients: 0, projects: 0, cities: 0 });

  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const cardHover = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.02,
      y: -4,
      transition: { duration: 0.3, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  // Animated stats counter
  useEffect(() => {
    const timer = setTimeout(() => {
      setMapStats({ clients: 500, projects: 1200, cities: 22 });
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const selectedCityData = allUKCities.find(city => city.id === selectedCity);
  const featuredCities = allUKCities.filter(city => city.featured);
  const allCities = allUKCities;

  return (
    <section className="relative py-24 lg:py-32 bg-white overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Header Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="mb-8">
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm"
            >
              <MapPin className="h-6 w-6 animate-pulse text-[#FF6B35]" />
              <span className="tracking-wide">UK-WIDE DIGITAL MARKETING</span>
            </Badge>
          </motion.div>

          <motion.h2
            variants={fadeInUp}
            className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-10"
          >
            <span className="block text-[#1E3A8A] mb-3">Local Insight. National Impact.</span>
            <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
              Unfair Advantage.
            </span>
          </motion.h2>

          <motion.p
            variants={fadeInUp}
            className="text-2xl lg:text-3xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-medium"
          >
            Winning in Wolverhampton requires a different approach than winning in Westminster. We understand the local nuances. Our "local-first" strategy means we research the specific search behaviours and competitive landscapes of your city. This allows us to create hyper-relevant campaigns that resonate with local customers and outmanoeuvre your direct competitors.
          </motion.p>
        </motion.div>

        {/* Premium Stats Grid */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20"
        >
          <motion.div variants={fadeInUp}>
            <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl group">
              <CardContent className="text-center p-8">
                <motion.div
                  className="w-16 h-16 gradient-navy-premium rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  <Users className="h-8 w-8 text-white" />
                </motion.div>
                <div className="text-4xl font-black text-webforleads-gray-900 mb-2">{mapStats.clients}+</div>
                <div className="text-webforleads-gray-600 font-semibold">UK Businesses Served</div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={fadeInUp}>
            <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl group">
              <CardContent className="text-center p-8">
                <motion.div
                  className="w-16 h-16 gradient-orange-premium rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  <TrendingUp className="h-8 w-8 text-white" />
                </motion.div>
                <div className="text-4xl font-black text-webforleads-gray-900 mb-2">{mapStats.projects}+</div>
                <div className="text-webforleads-gray-600 font-semibold">Successful Projects</div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={fadeInUp}>
            <Card className="bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl group">
              <CardContent className="text-center p-8">
                <motion.div
                  className="w-16 h-16 gradient-navy-premium rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  <MapPin className="h-8 w-8 text-white" />
                </motion.div>
                <div className="text-4xl font-black text-webforleads-gray-900 mb-2">{mapStats.cities}+</div>
                <div className="text-webforleads-gray-600 font-semibold">Cities & Towns</div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* UK Cities Coverage Section - Premium Redesign */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="mb-20"
        >
          {/* Premium Header */}
          <motion.div variants={fadeInUp} className="text-center mb-16">
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm mb-8"
            >
              <MapPin className="h-6 w-6 animate-pulse text-[#FF6B35]" />
              <span className="tracking-wide">UK-WIDE COVERAGE</span>
            </Badge>

            <h3 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
              <span className="block text-[#1E3A8A] mb-3">Digital Marketing & SEO</span>
              <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                Services Available In:
              </span>
            </h3>

            <p className="text-xl lg:text-2xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-medium">
              We provide comprehensive digital marketing services across all major UK cities and towns. Our local expertise combined with national reach gives you the unfair advantage.
            </p>
          </motion.div>

          {/* Major Cities Grid - Premium Design */}
          <motion.div variants={fadeInUp} className="mb-20">
            <div className="text-center mb-12">
              <h4 className="text-3xl lg:text-4xl font-black text-[#1E3A8A] mb-6">Major UK Cities</h4>
              <Badge className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 to-[#1E3A8A]/15 text-[#FF6B35] border border-[#FF6B35]/30 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 rounded-full backdrop-blur-sm text-lg font-bold">
                <Sparkles className="h-6 w-6 mr-3 fill-[#FF6B35] text-[#FF6B35] animate-pulse" />
                PREMIUM COVERAGE
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {featuredCities.map((city) => (
                <motion.div
                  key={city.id}
                  variants={fadeInUp}
                  initial="rest"
                  whileHover="hover"
                  className="group cursor-pointer"
                  onClick={() => setSelectedCity(city.id)}
                >
                  <Card className={`bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 rounded-3xl overflow-hidden transition-all duration-500 hover:scale-[1.02] ${
                    selectedCity === city.id
                      ? 'ring-4 ring-[#FF6B35] bg-gradient-to-br from-[#FF6B35]/10 to-background shadow-[#FF6B35]/30 border-[#FF6B35]/50'
                      : 'hover:border-[#FF6B35]/30'
                  }`}>
                    <CardContent className="p-8 text-center">
                      <motion.div variants={cardHover}>
                        <div className="w-16 h-16 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-2xl shadow-[#1E3A8A]/25 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-8 w-8 text-white" />
                        </div>
                        <h5 className="text-2xl font-black text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors duration-300">{city.name}</h5>
                        <div className="text-lg text-slate-600 mb-4 flex items-center justify-center gap-2 font-semibold">
                          <Users className="h-5 w-5 text-[#FF6B35]" />
                          {city.clients} clients
                        </div>
                        <Badge className="bg-gradient-to-r from-[#4CAF50]/15 to-[#1E3A8A]/15 text-[#4CAF50] border border-[#4CAF50]/30 hover:shadow-lg hover:shadow-[#4CAF50]/25 transition-all duration-500 rounded-full backdrop-blur-sm text-base font-bold px-4 py-2">
                          <TrendingUp className="h-4 w-4 mr-2 fill-[#4CAF50] text-[#4CAF50]" />
                          {city.growth} growth
                        </Badge>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Complete UK Cities List - Premium Design */}
          <motion.div variants={fadeInUp}>
            <Card className="bg-gradient-to-br from-[#1E3A8A] via-[#1E40AF] to-[#1E3A8A] text-white relative overflow-hidden shadow-2xl border-0 rounded-3xl">
              {/* Background accent */}
              <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-[#FF6B35]/30 to-transparent rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-radial from-[#4CAF50]/20 to-transparent rounded-full blur-3xl"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

              <CardHeader className="text-center relative p-12">
                <Badge className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#FF6B35]/20 via-white/10 to-[#FF6B35]/20 text-[#FF6B35] border border-[#FF6B35]/30 mb-8 text-lg font-bold rounded-full backdrop-blur-sm">
                  <MapPin className="h-6 w-6 mr-3 animate-pulse" />
                  NATIONWIDE COVERAGE
                </Badge>
                <CardTitle className="text-4xl lg:text-5xl font-black mb-6">Complete UK Coverage</CardTitle>
                <CardDescription className="text-white/90 text-xl font-medium">
                  Professional digital marketing services available nationwide
                </CardDescription>
              </CardHeader>

              <CardContent className="p-12 lg:p-16 relative">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
                  {allUKCities.map((city, index) => (
                    <motion.div
                      key={city.id}
                      variants={fadeInUp}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.02 }}
                      className="group"
                    >
                      <Card className="p-4 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:border-[#FF6B35]/50 hover:shadow-lg hover:shadow-[#FF6B35]/20 transition-all duration-300 cursor-pointer group-hover:scale-105 hover:bg-white/20">
                        <CardContent className="p-0">
                          <div className="flex items-center justify-between">
                            <div>
                              <h6 className="font-bold text-white text-base group-hover:text-[#FF6B35] transition-colors">
                                {city.name}
                              </h6>
                              <div className="text-sm text-white/70 mt-1 flex items-center gap-2">
                                <Target className="h-4 w-4 text-[#FF6B35]" />
                                Web Design & SEO
                              </div>
                            </div>
                            <div className="w-3 h-3 bg-[#4CAF50] rounded-full opacity-80 group-hover:opacity-100 transition-opacity shadow-lg"></div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                <div className="border-t border-white/20 pt-8 mt-8"></div>

                {/* SEO-Rich Footer Text */}
                <div className="text-center">
                  <p className="text-white/90 text-xl leading-relaxed font-medium">
                    <strong className="text-white font-bold">Professional Digital Marketing Services</strong> including
                    <span className="text-[#FF6B35] font-bold"> Web Design, SEO, Google Ads, and Social Media Marketing </span>
                    available across all UK locations. Our expert team delivers
                    <span className="text-[#4CAF50] font-bold"> measurable results and ROI-focused campaigns </span>
                    tailored to your local market and business objectives.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Local Expertise Showcase */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="mb-20"
        >
          <div className="grid lg:grid-cols-2 gap-16 items-center">

            {/* Local Insights */}
            <motion.div
              variants={fadeInUp}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
            >
              <h3 className="text-3xl lg:text-4xl font-black text-webforleads-gray-900 mb-6">
                Why Local Expertise Matters
              </h3>
              <p className="text-lg text-webforleads-gray-600 mb-8 leading-relaxed font-medium">
                Every UK city has its unique digital landscape. From London's competitive financial sector to Manchester's thriving tech scene, we understand the local nuances that make the difference between a campaign that works and one that dominates.
              </p>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 gradient-navy-premium rounded-xl flex items-center justify-center flex-shrink-0">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-webforleads-gray-900 mb-2">Local Search Behavior Analysis</h4>
                    <p className="text-webforleads-gray-600 font-medium">We research how customers in your specific city search for services, what terms they use, and when they're most active online.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 gradient-orange-premium rounded-xl flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-webforleads-gray-900 mb-2">Competitive Landscape Mapping</h4>
                    <p className="text-webforleads-gray-600 font-medium">We identify gaps in your local market that your competitors are missing, giving you strategic advantages.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-webforleads-green rounded-xl flex items-center justify-center flex-shrink-0">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-webforleads-gray-900 mb-2">Local Customer Insights</h4>
                    <p className="text-webforleads-gray-600 font-medium">Understanding local demographics, preferences, and buying patterns to create campaigns that truly resonate.</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Interactive Stats */}
            <motion.div
              variants={fadeInUp}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
            >
              <div className="gradient-navy-premium rounded-3xl p-8 lg:p-10 text-white relative overflow-hidden">
                {/* Background accent */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-webforleads-orange/20 to-transparent rounded-full blur-2xl"></div>

                <div className="relative">
                  <h4 className="text-2xl font-black mb-8">UK-Wide Impact</h4>

                  <div className="grid grid-cols-2 gap-6 mb-8">
                    <div className="text-center">
                      <div className="text-4xl font-black text-webforleads-orange mb-2">{mapStats.cities}+</div>
                      <div className="text-white/80 font-semibold">UK Cities Served</div>
                    </div>
                    <div className="text-center">
                      <div className="text-4xl font-black text-webforleads-green mb-2">{mapStats.clients}+</div>
                      <div className="text-white/80 font-semibold">Local Businesses</div>
                    </div>
                    <div className="text-center">
                      <div className="text-4xl font-black text-webforleads-orange mb-2">{mapStats.projects}+</div>
                      <div className="text-white/80 font-semibold">Successful Projects</div>
                    </div>
                    <div className="text-center">
                      <div className="text-4xl font-black text-webforleads-green mb-2">98%</div>
                      <div className="text-white/80 font-semibold">Client Retention</div>
                    </div>
                  </div>

                  <div className="text-center">
                    <p className="text-white/90 mb-6 font-medium">
                      From Scotland to the South Coast, we're helping UK businesses dominate their local markets.
                    </p>
                    <Button
                      className="border-2 border-white/30 bg-transparent text-white hover:bg-white hover:text-webforleads-navy transition-all duration-300 font-bold px-6 py-3 rounded-xl"
                    >
                      Find Your Local Expert
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Location-Focused CTA Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="text-center"
        >
          <div className="p-12 rounded-3xl gradient-navy-premium text-white relative overflow-hidden">
            {/* Background accent */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-webforleads-orange/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-br from-webforleads-green/20 to-transparent rounded-full blur-3xl"></div>

            <div className="relative">
              <h3 className="text-3xl lg:text-4xl font-black mb-6">
                Ready to Dominate Your Local Market?
              </h3>
              <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed font-medium">
                Whether you're in London's competitive landscape or looking to dominate a smaller market, we have the local expertise and national resources to make it happen. Join hundreds of UK businesses that trust us to drive their digital growth and local market dominance.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <Button
                  size="lg"
                  className="gradient-orange-premium text-white px-10 py-4 text-lg font-black rounded-xl transition-all duration-300 hover:scale-105 shadow-lg"
                >
                  <MapPin className="h-5 w-5 mr-2" />
                  Start Your Local Campaign
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>

                <Button
                  size="lg"
                  className="border-2 border-white/30 bg-transparent text-white hover:bg-white hover:text-webforleads-navy px-10 py-4 text-lg font-bold rounded-xl transition-all duration-300"
                >
                  <Users className="h-5 w-5 mr-2" />
                  View Local Case Studies
                </Button>
              </div>

              <div className="text-sm text-white/70">
                ✓ Free local market analysis  ✓ No long-term contracts  ✓ Results guaranteed
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
