"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Check<PERSON><PERSON>cle2, Loader2, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface FormData {
  name: string;
  website: string;
  email: string;
  phone: string;
  service: string;
}

interface AppointmentFormProps {
  title?: string;
  subtitle?: string;
  className?: string;
  onSuccess?: () => void;
}

export default function AppointmentForm({ 
  title = "Get Your FREE Growth Blueprint",
  subtitle = "Receive a no-obligation analysis of your website, competitor insights, and a clear action plan to dominate your market.",
  className = "",
  onSuccess
}: AppointmentFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    website: "",
    email: "",
    phone: "",
    service: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/send-appointment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit appointment request');
      }

      setIsSubmitted(true);
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setIsSubmitted(false);
    setFormData({
      name: "",
      website: "",
      email: "",
      phone: "",
      service: "",
    });
    setError(null);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Ultra-Premium form container */}
      <Card className="bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-hidden rounded-3xl relative">
        {/* Premium gradient border */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#1E3A8A]/20 via-[#FF6B35]/10 to-[#1E3A8A]/20 p-px rounded-3xl">
          <div className="w-full h-full rounded-3xl bg-white/95 backdrop-blur-xl"></div>
        </div>
        
        {/* Floating orb accents */}
        <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-radial from-[#FF6B35]/10 to-transparent rounded-full blur-xl animate-pulse-premium" />
        <div className="absolute bottom-4 left-4 w-16 h-16 bg-gradient-radial from-[#1E3A8A]/10 to-transparent rounded-full blur-xl animate-float" />

        <CardContent className="relative p-10 lg:p-12">
        {!isSubmitted ? (
          <>
          <div className="text-center mb-10">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/15 to-[#1E3A8A]/15 text-[#1E3A8A] border border-[#FF6B35]/30 mb-8 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 rounded-full backdrop-blur-sm">
                <TrendingUp className="h-5 w-5 text-[#FF6B35] mr-2 animate-pulse" />
                <span className="font-bold text-sm tracking-wide">FREE GROWTH ANALYSIS</span>
              </Badge>
            </motion.div>
            <h3 className="text-4xl lg:text-5xl font-black text-[#1E3A8A] mb-6 leading-tight">
              {title.includes("FREE") ? (
                <>
                  Get Your FREE
                  <span className="block bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] bg-clip-text text-transparent">
                    Growth Blueprint
                  </span>
                </>
              ) : (
                <span className="bg-gradient-to-r from-[#1E3A8A] to-[#FF6B35] bg-clip-text text-transparent">
                  {title}
                </span>
              )}
            </h3>
            <p className="text-slate-600 text-xl leading-relaxed font-medium">
              {subtitle}
            </p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <p className="text-red-600 text-sm font-medium">{error}</p>
            </div>
          )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-semibold text-gray-900">
                    Your Name
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className="h-14 border-2 border-slate-200 focus:border-[#FF6B35] focus:ring-4 focus:ring-[#FF6B35]/20 rounded-2xl text-lg font-medium bg-white/80 backdrop-blur-sm transition-all duration-300 hover:border-[#1E3A8A]/30"
                    placeholder="John Smith"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-900">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="h-14 border-2 border-slate-200 focus:border-[#FF6B35] focus:ring-4 focus:ring-[#FF6B35]/20 rounded-2xl text-lg font-medium bg-white/80 backdrop-blur-sm transition-all duration-300 hover:border-[#1E3A8A]/30"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website" className="text-sm font-semibold text-gray-900">
                  Company Website
                </Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  className="h-14 border-2 border-slate-200 focus:border-[#FF6B35] focus:ring-4 focus:ring-[#FF6B35]/20 rounded-2xl text-lg font-medium bg-white/80 backdrop-blur-sm transition-all duration-300 hover:border-[#1E3A8A]/30"
                  placeholder="https://yourcompany.com"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-semibold text-gray-900">
                  Phone Number
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  className="h-14 border-2 border-slate-200 focus:border-[#FF6B35] focus:ring-4 focus:ring-[#FF6B35]/20 rounded-2xl text-lg font-medium bg-white/80 backdrop-blur-sm transition-all duration-300 hover:border-[#1E3A8A]/30"
                  placeholder="+44 7700 900000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="service" className="text-sm font-semibold text-gray-900">
                  Which service are you interested in?
                </Label>
                <Select value={formData.service} onValueChange={(value) => handleInputChange("service", value)}>
                  <SelectTrigger className="h-14 border-2 border-slate-200 focus:border-[#FF6B35] focus:ring-4 focus:ring-[#FF6B35]/20 rounded-2xl text-lg font-medium bg-white/80 backdrop-blur-sm transition-all duration-300 hover:border-[#1E3A8A]/30">
                    <SelectValue placeholder="Select a service" />
                  </SelectTrigger>
                  <SelectContent className="rounded-2xl border-2 border-slate-200 bg-white/95 backdrop-blur-xl">
                    <SelectItem value="all-services" className="rounded-lg">All Services</SelectItem>
                    <SelectItem value="web-design" className="rounded-lg">Web Design</SelectItem>
                    <SelectItem value="seo" className="rounded-lg">SEO</SelectItem>
                    <SelectItem value="google-ads" className="rounded-lg">Google Ads</SelectItem>
                    <SelectItem value="smm" className="rounded-lg">SMM</SelectItem>
                    <SelectItem value="app-software-dev" className="rounded-lg">App/Software Dev</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full h-16 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white text-xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/30 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 animate-gradient-premium border-2 border-[#FF6B35]/20"
                >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    Analyzing Your Business...
                  </>
                ) : (
                  <>
                    <TrendingUp className="h-5 w-5 mr-2" />
                    GET MY FREE PLAN NOW
                  </>
                )}
                </Button>
              </motion.div>
            </form>

            <Separator className="my-6" />

            <div className="text-center p-4 rounded-xl bg-green-500/5 border border-green-500/20">
              <p className="text-sm text-gray-600 font-medium flex items-center justify-center gap-2">
                <span className="text-green-500">🔒</span>
                100% confidential. We'll contact you within 24 hours.
              </p>
            </div>
          </>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <div className="w-24 h-24 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
              <CheckCircle2 className="h-12 w-12 text-white" />
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Thank You!
            </h3>
            <p className="text-gray-600 text-lg mb-8 leading-relaxed">
              We've received your information and will be in touch within 24 hours with your personalized marketing audit.
            </p>
            <Button
              onClick={resetForm}
              variant="outline"
              className="border-2 border-blue-900/20 hover:border-blue-900/40 text-blue-900 hover:bg-blue-900/5 px-8 py-3 rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
            >
              Submit Another Request
            </Button>
          </motion.div>
        )}
        </CardContent>
      </Card>
    </div>
  );
}
