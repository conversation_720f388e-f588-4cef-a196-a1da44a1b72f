"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  ArrowRight, CheckCircle, TrendingUp, Users, Target,
  BarChart3, Palette, Rocket, Smartphone, Search,
  Sparkles, Trophy, Brush, Brain, Gauge, Star, Award,
  HelpCircle, MessageCircle
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";

// ============================================================================
// CUSTOMIZABLE CONFIGURATION - EDIT THESE VALUES FOR DIFFERENT SERVICES
// ============================================================================

interface ServiceConfig {
  // Service Details
  serviceName: string;
  serviceSlug: string;

  // SEO & Meta
  metaTitle: string;
  metaDescription: string;
  keywords: string[];

  // Hero Section
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  heroBadgeText: string;

  // Stats Section
  stats: Array<{
    number: string;
    label: string;
    icon: React.ReactNode;
  }>;

  // Premium Design Features (Main Features)
  designFeatures: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
    highlight: string;
  }>;

  // Features Grid (Secondary Features)
  features: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
  }>;

  // Service Packages
  packages: Array<{
    name: string;
    price: string;
    period: string;
    description: string;
    features: string[];
    highlight: boolean;
    cta: string;
  }>;

  // Process Steps
  process: Array<{
    step: string;
    title: string;
    description: string;
    duration: string;
  }>;

  // Featured Testimonial
  featuredTestimonial: {
    quote: string;
    name: string;
    role: string;
    company: string;
    rating: number;
    result: string;
    resultLabel: string;
  };

  // Additional Testimonials
  testimonials: Array<{
    quote: string;
    name: string;
    role: string;
    company: string;
    industry: string;
    metric: string;
    metricLabel: string;
    avatar: string;
  }>;

  // FAQ Categories
  faqCategories: Array<{
    category: string;
    icon: React.ReactNode;
    questions: Array<{
      q: string;
      a: string;
    }>;
  }>;

  // Final CTA Section
  finalCTA: {
    badge: string;
    title: string;
    description: string;
  };

  // Structured Data
  structuredData: {
    serviceName: string;
    description: string;
    priceRange: string;
    areaServed: string;
    aggregateRating: {
      ratingValue: string;
      reviewCount: string;
    };
  };
}

// DEFAULT CONFIGURATION - CUSTOMIZE FOR EACH SERVICE
const defaultConfig: ServiceConfig = {
  serviceName: "Web Design",
  serviceSlug: "web-design",

  metaTitle: "Professional Web Design Services | Custom Website Design Company | Responsive Web Design",
  metaDescription: "Leading web design company creating stunning, responsive websites that convert visitors into customers. Custom web design services including UI/UX design, mobile-first development, and conversion optimization.",
  keywords: ["web design", "website design", "custom web design", "responsive web design", "UI/UX design"],

  heroTitle: "Professional Web Design Services That Convert",
  heroSubtitle: "Custom Websites, Mobile-First Design & Conversion Optimization",
  heroDescription: "Transform your business with stunning, high-performing websites that convert visitors into customers. Our expert team creates custom designs that reflect your brand and drive results.",
  heroBadgeText: "AWARD-WINNING WEB DESIGN",

  stats: [
    { number: "500%", label: "Average Conversion Increase", icon: <TrendingUp className="h-6 w-6" /> },
    { number: "£1.8M+", label: "Revenue Generated", icon: <Target className="h-6 w-6" /> },
    { number: "96%", label: "Client Satisfaction Rate", icon: <Users className="h-6 w-6" /> },
    { number: "24hrs", label: "Design Turnaround", icon: <Rocket className="h-6 w-6" /> }
  ],

  designFeatures: [
    {
      icon: <Brain className="h-8 w-8" />,
      title: "Conversion Psychology Design",
      description: "Every element is strategically placed using proven psychological principles to maximize user engagement and conversions.",
      highlight: "Psychology-Driven"
    },
    {
      icon: <Gauge className="h-8 w-8" />,
      title: "Lightning-Fast Performance",
      description: "Sub-2-second load times guaranteed with advanced optimization techniques and premium hosting infrastructure.",
      highlight: "Speed Optimized"
    },
    {
      icon: <Smartphone className="h-8 w-8" />,
      title: "Mobile-First Excellence",
      description: "Designed for mobile perfection first, then scaled up to create seamless experiences across all devices.",
      highlight: "Mobile-First"
    },
    {
      icon: <Search className="h-8 w-8" />,
      title: "SEO-Engineered Architecture",
      description: "Built with advanced SEO architecture to dominate search rankings from day one of launch.",
      highlight: "SEO-Ready"
    }
  ],

  features: [
    {
      icon: <Palette className="h-6 w-6" />,
      title: "Custom Design & Branding",
      description: "Unique designs that reflect your brand identity and stand out from template-based competitors."
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: "Mobile-First Development",
      description: "Responsive designs that work flawlessly across all devices, ensuring optimal user experience."
    },
    {
      icon: <TrendingUp className="h-6 w-6" />,
      title: "Conversion Optimization",
      description: "Every element strategically placed to guide visitors toward taking action and becoming customers."
    },
    {
      icon: <Rocket className="h-6 w-6" />,
      title: "Lightning-Fast Performance",
      description: "Optimized for speed with advanced caching, compression, and modern development practices."
    },
    {
      icon: <Search className="h-6 w-6" />,
      title: "SEO-Optimized Structure",
      description: "Built with clean code and SEO best practices to help you rank higher in search results."
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Advanced Analytics",
      description: "Comprehensive tracking setup to monitor performance and user behavior from day one."
    }
  ],

  packages: [
    {
      name: "Conversion Catalyst",
      price: "£3,500",
      period: "",
      description: "Perfect for businesses ready to transform their online presence",
      features: [
        "Custom conversion-focused design",
        "Mobile-first responsive development",
        "Advanced SEO optimization",
        "Performance optimization",
        "2 weeks delivery",
        "30-day support included"
      ],
      highlight: false,
      cta: "Transform My Website"
    },
    {
      name: "Revenue Accelerator",
      price: "£7,500",
      period: "",
      description: "For businesses serious about market domination",
      features: [
        "Everything in Conversion Catalyst",
        "Advanced analytics integration",
        "A/B testing setup",
        "Custom CMS development",
        "E-commerce functionality",
        "90-day optimization support"
      ],
      highlight: true,
      cta: "Accelerate My Revenue"
    },
    {
      name: "Enterprise Dominator",
      price: "Custom",
      period: "",
      description: "Bespoke solutions for industry leaders",
      features: [
        "Everything in Revenue Accelerator",
        "Custom application development",
        "Advanced integrations",
        "Dedicated project team",
        "White-glove service",
        "Ongoing optimization"
      ],
      highlight: false,
      cta: "Get Custom Quote"
    }
  ],

  process: [
    {
      step: "01",
      title: "Discovery & Strategy",
      description: "We dive deep into your business goals, target audience, and competitive landscape to create a strategic foundation.",
      duration: "Week 1"
    },
    {
      step: "02",
      title: "Design & Prototyping",
      description: "Custom designs and interactive prototypes that bring your vision to life before development begins.",
      duration: "Week 2-3"
    },
    {
      step: "03",
      title: "Development & Testing",
      description: "Clean, modern code development with rigorous testing across all devices and browsers.",
      duration: "Week 4-5"
    },
    {
      step: "04",
      title: "Launch & Optimization",
      description: "Smooth launch with ongoing monitoring and optimization to ensure peak performance.",
      duration: "Week 6+"
    }
  ],

  featuredTestimonial: {
    quote: "WebforLeads transformed our online presence completely. Our new website increased conversions by 340% and perfectly captures our brand essence. The team's attention to detail and strategic approach exceeded all expectations.",
    name: "Sarah Mitchell",
    role: "CEO & Founder",
    company: "TechVision Solutions",
    rating: 5,
    result: "340%",
    resultLabel: "Conversion Increase"
  },

  testimonials: [
    {
      quote: "The new website increased our conversion rate from 2.1% to 8.7% within the first month. The user experience is flawless and the design perfectly captures our brand.",
      name: "Marcus Chen",
      role: "Head of Growth",
      company: "FinanceForward",
      industry: "FinTech",
      metric: "315%",
      metricLabel: "Conversion Boost",
      avatar: "MC"
    },
    {
      quote: "Our online sales doubled in just 6 weeks after launch. The mobile experience is incredible and our customers love the new checkout process.",
      name: "Emma Rodriguez",
      role: "E-commerce Director",
      company: "StyleHub",
      industry: "Fashion",
      metric: "200%",
      metricLabel: "Sales Increase",
      avatar: "ER"
    },
    {
      quote: "The SEO improvements were immediate. We're now ranking #1 for our main keywords and organic traffic has increased by 400%.",
      name: "David Park",
      role: "Marketing Manager",
      company: "GreenTech Solutions",
      industry: "Technology",
      metric: "400%",
      metricLabel: "Traffic Growth",
      avatar: "DP"
    }
  ],

  faqCategories: [
    {
      category: "Process & Timeline",
      icon: <Rocket className="h-6 w-6" />,
      questions: [
        {
          q: "How long does a typical web design project take?",
          a: "Most projects are completed in 4-6 weeks, depending on complexity and scope. Simple brochure sites can be done in 2-3 weeks, while complex e-commerce or custom applications may take 8-12 weeks. We provide detailed timelines during our initial consultation."
        },
        {
          q: "What information do you need to get started?",
          a: "We'll need your brand guidelines, existing content, high-quality images, and a clear understanding of your business goals and target audience. Don't worry if you don't have everything ready - we can help you gather and create what's needed."
        },
        {
          q: "Can I see the design before development starts?",
          a: "Absolutely! We create detailed mockups and interactive prototypes so you can see exactly how your website will look and function before we begin development. This ensures you're completely satisfied with the design."
        }
      ]
    },
    {
      category: "Services & Features",
      icon: <Palette className="h-6 w-6" />,
      questions: [
        {
          q: "Do you provide content writing and SEO services?",
          a: "Yes! We offer comprehensive content strategy, copywriting, and technical SEO optimization. Our team includes experienced copywriters and SEO specialists who ensure your site ranks well and converts visitors into customers."
        },
        {
          q: "Will my website be mobile-friendly and responsive?",
          a: "Absolutely. All our websites are built with a mobile-first approach, ensuring perfect functionality across all devices. We test on multiple screen sizes and optimize for Core Web Vitals to guarantee excellent user experience."
        },
        {
          q: "Do you provide ongoing maintenance and support?",
          a: "Yes, we offer comprehensive maintenance packages including regular updates, security monitoring, performance optimization, and technical support. All our packages include initial support periods with options to extend."
        }
      ]
    },
    {
      category: "Technical & Pricing",
      icon: <BarChart3 className="h-6 w-6" />,
      questions: [
        {
          q: "What platform do you build websites on?",
          a: "We use modern technologies like React, Next.js, and WordPress depending on your needs. We choose the best platform based on your specific requirements, scalability needs, and technical preferences."
        },
        {
          q: "Do you offer payment plans for larger projects?",
          a: "Yes, we offer flexible payment plans for projects over £5,000. Typically, we require 50% upfront and the remainder upon completion, but we can customize payment schedules to fit your budget."
        },
        {
          q: "Can I update the website content myself?",
          a: "Absolutely. We build user-friendly content management systems that allow you to easily update text, images, and other content without technical knowledge. We also provide training and documentation."
        }
      ]
    }
  ],

  finalCTA: {
    badge: "Get Started",
    title: "Let's Plan Your Next Big Win",
    description: "Share your goals and we'll propose a high-impact roadmap with clear timelines and pricing."
  },

  structuredData: {
    serviceName: "Professional Web Design Services",
    description: "Custom web design and development services creating responsive, conversion-focused websites for businesses of all sizes.",
    priceRange: "£3000-£15000",
    areaServed: "United Kingdom",
    aggregateRating: {
      ratingValue: "4.9",
      reviewCount: "127"
    }
  }
};

// ============================================================================
// REUSABLE ANIMATIONS
// ============================================================================

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

// ============================================================================
// MAIN TEMPLATE COMPONENT
// ============================================================================

interface ServiceTemplateProps {
  config?: Partial<ServiceConfig>;
}

export default function ServiceTemplate({ config: customConfig }: ServiceTemplateProps) {
  const config = { ...defaultConfig, ...customConfig };

  return (
    <div className="min-h-screen bg-white">
      {/* SEO Meta Tags */}
      <head>
        <title>{config.metaTitle}</title>
        <meta name="description" content={config.metaDescription} />
        <meta name="keywords" content={config.keywords.join(", ")} />
      </head>

      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">
            
            {/* Left Column - Content */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Sparkles className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">{config.heroBadgeText}</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black tracking-tight leading-[1.1] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      {config.heroTitle}
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent">
                        {config.heroSubtitle}
                      </span>
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    {config.heroDescription}
                  </p>
                </motion.div>
              </motion.div>

              {/* Key Benefits */}
              <motion.div variants={fadeInUp} className="space-y-4">
                {config.designFeatures.slice(0, 3).map((feature, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-4 group cursor-pointer"
                    whileHover={{ x: 4 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] rounded-xl flex items-center justify-center text-white"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <CheckCircle className="h-5 w-5" />
                    </motion.div>
                    <div>
                      <span className="font-bold text-gray-900 group-hover:text-[#FF6B35] transition-colors duration-200">
                        {feature.title}
                      </span>
                      <span className="text-gray-600 ml-2">— {feature.description}</span>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>

            {/* Right Column - Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm 
                title={`Get Your FREE ${config.serviceName} Quote`}
                subtitle={`Receive a no-obligation quote for your ${config.serviceName.toLowerCase()} project with detailed pricing and timeline.`}
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Features Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#1E3A8A]/10 via-[#FF6B35]/5 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#1E3A8A]/20 rounded-full">
                <Target className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">WHY CHOOSE US</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                {config.serviceName} Features That
                <span className="block text-[#FF6B35]">Drive Results</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Our {config.serviceName.toLowerCase()} services are designed with one goal in mind: delivering measurable results for your business.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {config.designFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <CardContent className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center text-white flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                          <Badge className="px-3 py-1 bg-[#FF6B35]/10 text-[#FF6B35] border border-[#FF6B35]/20 text-xs font-bold rounded-full">
                            {feature.highlight}
                          </Badge>
                        </div>
                        <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                      </div>
                    </div>
                  </CardContent>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Pricing Packages Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-[#FF6B35]/5 to-[#1E3A8A]/5 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">PRICING PACKAGES</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Choose Your
                <span className="block text-[#FF6B35]">{config.serviceName} Package</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Transparent pricing with no hidden fees. Choose the package that best fits your business needs and budget.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-3 gap-8"
          >
            {config.packages.map((pkg, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className={`relative h-full border-0 rounded-3xl overflow-hidden transition-all duration-500 ${
                  pkg.highlight
                    ? 'bg-gradient-to-b from-[#1E3A8A] to-[#1E3A8A]/90 text-white shadow-2xl shadow-[#1E3A8A]/25 scale-105'
                    : 'bg-white shadow-xl shadow-gray-900/5 hover:shadow-2xl hover:shadow-gray-900/10'
                }`}>
                  {pkg.highlight && (
                    <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] text-white text-center py-3 font-bold text-sm tracking-wide">
                      MOST POPULAR
                    </div>
                  )}

                  <CardContent className={`p-8 ${pkg.highlight ? 'pt-16' : ''}`}>
                    <div className="text-center mb-8">
                      <h3 className={`text-2xl font-bold mb-2 ${pkg.highlight ? 'text-white' : 'text-[#1E3A8A]'}`}>
                        {pkg.name}
                      </h3>
                      <div className={`text-4xl font-black mb-2 ${pkg.highlight ? 'text-white' : 'text-[#1E3A8A]'}`}>
                        {pkg.price}
                        {pkg.period && <span className="text-lg font-medium text-gray-500">/{pkg.period}</span>}
                      </div>
                      <p className={`${pkg.highlight ? 'text-blue-200' : 'text-gray-600'} leading-relaxed`}>
                        {pkg.description}
                      </p>
                    </div>

                    <div className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <CheckCircle className={`h-5 w-5 flex-shrink-0 ${pkg.highlight ? 'text-[#FF6B35]' : 'text-green-500'}`} />
                          <span className={`${pkg.highlight ? 'text-blue-100' : 'text-gray-700'} font-medium`}>
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>

                    <Button
                      onClick={() => {
                        // Add visual feedback
                        const button = document.activeElement as HTMLButtonElement;
                        if (button) {
                          button.style.transform = 'scale(0.95)';
                          setTimeout(() => {
                            button.style.transform = '';
                          }, 150);
                        }

                        // Scroll to appointment form
                        const appointmentSection = document.getElementById('appointment-form');
                        if (appointmentSection) {
                          appointmentSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                          });
                        }
                      }}
                      className={`w-full py-4 rounded-xl font-bold transition-all duration-300 ${
                        pkg.highlight
                          ? 'bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF6B35]/90 hover:to-[#FF8A65]/90 text-white shadow-lg hover:shadow-xl'
                          : 'bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white shadow-lg hover:shadow-xl hover:scale-105'
                      }`}
                    >
                      {pkg.cta}
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {config.stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-blue-200 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Features Grid Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Complete {config.serviceName}
                <span className="block text-[#FF6B35]">Services</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From concept to launch, we provide everything you need for a successful {config.serviceName.toLowerCase()} presence.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {config.features.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 bg-white shadow-lg shadow-gray-900/5 rounded-2xl overflow-hidden group hover:shadow-xl hover:shadow-gray-900/10 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/80 rounded-xl flex items-center justify-center mb-6 text-white group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Rocket className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">OUR PROCESS</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                How We Deliver
                <span className="block text-[#FF6B35]">Exceptional Results</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Our proven process ensures your {config.serviceName.toLowerCase()} project is delivered on time, on budget, and exceeds expectations.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {config.process.map((step, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 bg-white shadow-lg shadow-gray-900/5 rounded-2xl overflow-hidden group hover:shadow-xl hover:shadow-gray-900/10 transition-all duration-300">
                  <CardContent className="p-8 text-center">
                    <div className="text-4xl font-black text-[#FF6B35] mb-4">{step.step}</div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                    <p className="text-gray-600 leading-relaxed mb-4">{step.description}</p>
                    <Badge className="px-3 py-1 bg-[#1E3A8A]/10 text-[#1E3A8A] border border-[#1E3A8A]/20 text-xs font-bold rounded-full">
                      {step.duration}
                    </Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-32 bg-white relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Transforming Businesses
                <span className="block text-[#FF6B35]">Across Industries</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                See how our {config.serviceName.toLowerCase()} solutions have helped businesses achieve remarkable growth and success.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16"
          >
            {/* Featured Testimonial */}
            <motion.div variants={fadeInUp} className="lg:col-span-2">
              <Card className="border-0 rounded-3xl shadow-2xl bg-gradient-to-br from-white via-white to-[#FF6B35]/5 overflow-hidden">
                <CardContent className="p-10 lg:p-12">
                  <div className="flex flex-col lg:flex-row items-start gap-8">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 text-[#FF6B35] mb-6">
                        {[...Array(config.featuredTestimonial.rating)].map((_, s) => <Star key={s} className="h-6 w-6 fill-current" />)}
                      </div>
                      <blockquote className="text-2xl lg:text-3xl font-bold text-[#1E3A8A] leading-relaxed mb-6">
                        "{config.featuredTestimonial.quote}"
                      </blockquote>
                      <div className="flex items-center gap-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center text-white font-bold text-xl">
                          {config.featuredTestimonial.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <div className="font-bold text-[#1E3A8A] text-lg">{config.featuredTestimonial.name}</div>
                          <div className="text-slate-600">{config.featuredTestimonial.role}</div>
                          <div className="text-[#FF6B35] font-semibold">{config.featuredTestimonial.company}</div>
                        </div>
                      </div>
                    </div>
                    <div className="text-center lg:text-right">
                      <div className="text-5xl font-black text-[#FF6B35] mb-2">{config.featuredTestimonial.result}</div>
                      <div className="text-[#1E3A8A] font-bold">{config.featuredTestimonial.resultLabel}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>

          {/* Additional Testimonials Grid */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {config.testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-1 text-[#FF6B35] mb-4">
                      {[...Array(5)].map((_, s) => <Star key={s} className="h-4 w-4 fill-current" />)}
                    </div>

                    <blockquote className="text-slate-700 leading-relaxed mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center text-white font-bold">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="font-bold text-[#1E3A8A]">{testimonial.name}</div>
                          <div className="text-sm text-slate-600">{testimonial.role}</div>
                          <div className="text-sm text-[#FF6B35] font-semibold">{testimonial.company}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-black text-[#FF6B35]">{testimonial.metric}</div>
                        <div className="text-xs text-[#1E3A8A] font-bold">{testimonial.metricLabel}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <HelpCircle className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Got Questions About
                <span className="block text-[#FF6B35]">{config.serviceName}?</span>
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Find answers to the most common questions about our {config.serviceName.toLowerCase()} services.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {config.faqCategories.map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={fadeInUp}
                className="space-y-6"
              >
                <div className="flex items-center space-x-3 mb-8">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-xl flex items-center justify-center text-white">
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">{category.category}</h3>
                </div>

                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <Card key={faqIndex} className="border-0 bg-slate-50/50 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-lg font-bold text-[#1E3A8A] mb-3">{faq.q}</h4>
                        <p className="text-slate-600 leading-relaxed">{faq.a}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Help Section */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="mt-20 text-center"
          >
            <motion.div variants={fadeInUp}>
              <Card className="border-0 rounded-3xl shadow-xl bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] text-white overflow-hidden">
                <CardContent className="p-10">
                  <div className="max-w-2xl mx-auto">
                    <h3 className="text-2xl font-bold mb-4">Still Have Questions?</h3>
                    <p className="text-slate-300 mb-6 leading-relaxed">
                      Our team is here to help! Schedule a free consultation to discuss your specific {config.serviceName.toLowerCase()} needs and get personalized answers to all your questions.
                    </p>
                    <Button className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF6B35]/90 hover:to-[#FF8A65]/90 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                      <MessageCircle className="h-5 w-5 mr-2" />
                      Get Free Consultation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="appointment-form" className="py-24 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <Badge className="px-4 py-1 bg-white/10 text-white border border-white/20 rounded-full">{config.finalCTA.badge}</Badge>
              <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">{config.finalCTA.title}</h2>
              <p className="text-slate-300 text-lg max-w-xl">{config.finalCTA.description}</p>
            </div>
            <div>
              <AppointmentForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
