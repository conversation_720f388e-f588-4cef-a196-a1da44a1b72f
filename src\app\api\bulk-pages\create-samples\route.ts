import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { saveBulkJob } from '@/lib/bulk-pages-storage';

// Sample data for each template
const SAMPLE_TEMPLATES = [
  {
    templateId: 'web-design',
    templateName: 'Web Design Services',
    keyword: 'web design',
    location: 'London'
  },
  {
    templateId: 'seo-services',
    templateName: 'SEO Services',
    keyword: 'SEO services',
    location: 'Manchester'
  },
  {
    templateId: 'google-ads-services',
    templateName: 'Google Ads Services',
    keyword: 'Google Ads management',
    location: 'Birmingham'
  },
  {
    templateId: 'social-media-marketing',
    templateName: 'Social Media Marketing',
    keyword: 'social media marketing',
    location: 'Leeds'
  },
  {
    templateId: 'app-development',
    templateName: 'App Development',
    keyword: 'app development',
    location: 'Liverpool'
  }
];

// Create slug from keyword and location
function createSlug(keyword: string, location: string): string {
  const combined = `${keyword} ${location}`;
  return combined
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Create page title from keyword and location
function createPageTitle(keyword: string, location: string): string {
  // Capitalize first letter of each word
  const capitalizeWords = (str: string) => {
    return str.replace(/\b\w/g, l => l.toUpperCase());
  };
  
  return `Professional ${capitalizeWords(keyword)} Services in ${capitalizeWords(location)} | WebforLeads`;
}

export async function POST() {
  try {
    console.log('\n🎯 CREATING SAMPLE PAGES FOR ALL TEMPLATES');
    console.log('⏰ Timestamp:', new Date().toISOString());

    const results = [];

    for (const template of SAMPLE_TEMPLATES) {
      console.log(`\n📝 Creating sample for ${template.templateName}...`);
      
      // Generate job ID
      const jobId = uuidv4();
      const slug = createSlug(template.keyword, template.location);
      const title = createPageTitle(template.keyword, template.location);

      // Create page data
      const page = {
        id: uuidv4(),
        keyword: template.keyword,
        location: template.location,
        slug,
        title,
        status: 'pending' as const,
        createdAt: new Date().toISOString(),
        progress: 0
      };

      // Create bulk generation job
      const job = {
        id: jobId,
        name: `Sample ${template.templateName} - ${template.location}`,
        templateId: template.templateId,
        templateName: template.templateName,
        keywords: [template.keyword],
        locations: [template.location],
        status: 'pending' as const,
        progress: 0,
        totalPages: 1,
        completedPages: 0,
        createdAt: new Date().toISOString(),
        pages: [page]
      };

      // Save job to storage
      saveBulkJob(job);

      console.log(`✅ Sample job created for ${template.templateName}`);
      console.log(`   - Job ID: ${job.id}`);
      console.log(`   - Slug: ${slug}`);
      console.log(`   - Title: ${title}`);

      results.push({
        templateId: template.templateId,
        templateName: template.templateName,
        jobId: job.id,
        slug,
        title,
        keyword: template.keyword,
        location: template.location
      });
    }

    console.log('\n🎉 ALL SAMPLE JOBS CREATED SUCCESSFULLY');
    console.log(`📊 Total samples: ${results.length}`);

    return NextResponse.json({
      success: true,
      message: `Created ${results.length} sample jobs successfully`,
      samples: results,
      nextSteps: [
        'Go to /admin/bulk-pages to see the sample jobs',
        'Click "Start" on each job to generate the sample pages',
        'Sample pages will be created in /area-pages/ directory'
      ]
    });

  } catch (error) {
    console.error('💥 SAMPLE CREATION ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create sample jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Sample page creation API endpoint. Use POST to create sample jobs for all templates.',
    templates: SAMPLE_TEMPLATES.map(t => ({
      templateId: t.templateId,
      templateName: t.templateName,
      samplePage: `${t.keyword.toLowerCase().replace(/\s+/g, '-')}-${t.location.toLowerCase()}`
    })),
    endpoints: {
      'POST /api/bulk-pages/create-samples': 'Create sample jobs for all templates'
    }
  });
}
