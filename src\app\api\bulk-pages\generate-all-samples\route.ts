import { NextResponse } from 'next/server';

// Sample data for each template
const SAMPLE_TEMPLATES = [
  {
    templateId: 'web-design',
    templateName: 'Web Design Services',
    keyword: 'web design',
    location: 'London',
    expectedSlug: 'web-design-london'
  },
  {
    templateId: 'seo-services',
    templateName: 'SEO Services',
    keyword: 'SEO services',
    location: 'Manchester',
    expectedSlug: 'seo-services-manchester'
  },
  {
    templateId: 'google-ads-services',
    templateName: 'Google Ads Services',
    keyword: 'Google Ads management',
    location: 'Birmingham',
    expectedSlug: 'google-ads-management-birmingham'
  },
  {
    templateId: 'social-media-marketing',
    templateName: 'Social Media Marketing',
    keyword: 'social media marketing',
    location: 'Leeds',
    expectedSlug: 'social-media-marketing-leeds'
  },
  {
    templateId: 'app-development',
    templateName: 'App Development',
    keyword: 'app development',
    location: 'Liverpool',
    expectedSlug: 'app-development-liverpool'
  }
];

export async function POST() {
  try {
    console.log('\n🚀 GENERATING ALL SAMPLE PAGES');
    console.log('⏰ Timestamp:', new Date().toISOString());

    const results = [];
    const baseUrl = 'http://localhost:3001';

    for (const template of SAMPLE_TEMPLATES) {
      console.log(`\n📝 Processing ${template.templateName}...`);
      
      try {
        // Step 1: Create job
        console.log(`🔄 Creating job for ${template.templateName}...`);
        const createResponse = await fetch(`${baseUrl}/api/bulk-pages/create-job`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: `Sample ${template.templateName} - ${template.location}`,
            templateId: template.templateId,
            templateName: template.templateName,
            keywords: [template.keyword],
            locations: [template.location]
          }),
        });

        const createData = await createResponse.json();
        
        if (!createData.success) {
          throw new Error(`Failed to create job: ${createData.error}`);
        }

        const jobId = createData.job.id;
        console.log(`✅ Job created: ${jobId}`);

        // Step 2: Start generation
        console.log(`🔄 Starting generation for ${template.templateName}...`);
        const startResponse = await fetch(`${baseUrl}/api/bulk-pages/start-generation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jobId: jobId
          }),
        });

        const startData = await startResponse.json();
        
        if (!startData.success) {
          throw new Error(`Failed to start generation: ${startData.error}`);
        }

        console.log(`✅ Generation started for ${template.templateName}`);

        results.push({
          templateId: template.templateId,
          templateName: template.templateName,
          jobId: jobId,
          keyword: template.keyword,
          location: template.location,
          expectedSlug: template.expectedSlug,
          expectedUrl: `/area-pages/${template.expectedSlug}`,
          status: 'generation_started'
        });

        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`❌ Error processing ${template.templateName}:`, error);
        results.push({
          templateId: template.templateId,
          templateName: template.templateName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log('\n🎉 ALL SAMPLE GENERATIONS INITIATED');
    console.log(`📊 Total templates processed: ${results.length}`);
    console.log(`✅ Successful: ${results.filter(r => r.status === 'generation_started').length}`);
    console.log(`❌ Failed: ${results.filter(r => r.status === 'error').length}`);

    return NextResponse.json({
      success: true,
      message: `Initiated generation for ${results.filter(r => r.status === 'generation_started').length} sample pages`,
      results,
      expectedPages: results
        .filter(r => r.status === 'generation_started')
        .map(r => ({
          templateName: r.templateName,
          url: r.expectedUrl,
          slug: r.expectedSlug,
          keyword: r.keyword,
          location: r.location
        })),
      note: 'Pages are being generated in the background. Check /admin/bulk-pages for progress. Pages will be available at the URLs listed above once generation is complete.'
    });

  } catch (error) {
    console.error('💥 GENERATE ALL SAMPLES ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate sample pages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Generate all sample pages API endpoint. Use POST to create and generate sample pages for all templates.',
    templates: SAMPLE_TEMPLATES.map(t => ({
      templateId: t.templateId,
      templateName: t.templateName,
      expectedUrl: `/area-pages/${t.expectedSlug}`,
      keyword: t.keyword,
      location: t.location
    })),
    endpoints: {
      'POST /api/bulk-pages/generate-all-samples': 'Create and generate sample pages for all templates'
    }
  });
}
