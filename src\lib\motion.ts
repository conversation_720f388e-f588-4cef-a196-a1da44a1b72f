import { Variants } from "framer-motion";

// Reduced motion check
export const prefersReducedMotion = () => {
  if (typeof window !== "undefined") {
    return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
  }
  return false;
};

// Base animation variants
export const fadeIn: Variants = {
  hidden: { 
    opacity: 0,
    transition: { duration: prefersReducedMotion() ? 0 : 0.3 }
  },
  show: { 
    opacity: 1,
    transition: { duration: prefersReducedMotion() ? 0 : 0.3 }
  }
};

export const fadeInUp: Variants = {
  hidden: { 
    opacity: 0, 
    y: prefersReducedMotion() ? 0 : 20,
    transition: { duration: prefersReducedMotion() ? 0 : 0.3 }
  },
  show: { 
    opacity: 1, 
    y: 0,
    transition: { duration: prefersReducedMotion() ? 0 : 0.5, ease: "easeOut" }
  }
};

export const fadeInSide: Variants = {
  hidden: { 
    opacity: 0, 
    x: prefersReducedMotion() ? 0 : -20,
    transition: { duration: prefersReducedMotion() ? 0 : 0.3 }
  },
  show: { 
    opacity: 1, 
    x: 0,
    transition: { duration: prefersReducedMotion() ? 0 : 0.5, ease: "easeOut" }
  }
};

export const scaleIn: Variants = {
  hidden: { 
    opacity: 0, 
    scale: prefersReducedMotion() ? 1 : 0.95,
    transition: { duration: prefersReducedMotion() ? 0 : 0.3 }
  },
  show: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: prefersReducedMotion() ? 0 : 0.4, ease: "easeOut" }
  }
};

// Stagger animations
export const staggerChildren: Variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: prefersReducedMotion() ? 0 : 0.1,
      delayChildren: prefersReducedMotion() ? 0 : 0.2
    }
  }
};

export const staggerGrid: Variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: prefersReducedMotion() ? 0 : 0.15,
      delayChildren: prefersReducedMotion() ? 0 : 0.3
    }
  }
};

export const staggerTimeline: Variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: prefersReducedMotion() ? 0 : 0.2,
      delayChildren: prefersReducedMotion() ? 0 : 0.4
    }
  }
};

// Kinetic text animation
export const kineticText: Variants = {
  hidden: { 
    opacity: 0,
    y: prefersReducedMotion() ? 0 : 50,
    transition: { duration: prefersReducedMotion() ? 0 : 0.3 }
  },
  show: { 
    opacity: 1,
    y: 0,
    transition: { 
      duration: prefersReducedMotion() ? 0 : 0.8, 
      ease: [0.25, 0.46, 0.45, 0.94] 
    }
  }
};

// Accordion variants
export const accordionVariants: Variants = {
  closed: { 
    height: 0, 
    opacity: 0,
    transition: { 
      duration: prefersReducedMotion() ? 0 : 0.3,
      ease: "easeInOut"
    }
  },
  open: { 
    height: "auto", 
    opacity: 1,
    transition: { 
      duration: prefersReducedMotion() ? 0 : 0.3,
      ease: "easeInOut"
    }
  }
};

// In-view animation options
export const inViewOnce = {
  once: true,
  amount: 0.3
};

export const inViewPartial = {
  once: true,
  amount: 0.1
};
