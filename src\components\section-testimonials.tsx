"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, TrendingUp, Users, Award, CheckCircle } from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CEO",
    company: "Mitchell & Associates",
    location: "London",
    image: "/testimonials/sarah.jpg",
    rating: 5,
    quote: "WebforLeads transformed our digital presence completely. We went from 2-3 leads per month to 25+ qualified leads. Their local SEO strategy for London has been phenomenal.",
    results: "1,200% increase in leads",
    industry: "Legal Services",
    featured: true
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Founder",
    company: "Thompson Plumbing",
    location: "Manchester",
    image: "/testimonials/james.jpg",
    rating: 5,
    quote: "The ROI has been incredible. Every pound spent with WebforLeads has returned at least £8 in revenue. Their Google Ads management is simply outstanding.",
    results: "800% ROI",
    industry: "Home Services",
    featured: true
  },
  {
    id: 3,
    name: "Emma Williams",
    role: "Marketing Director",
    company: "TechStart Solutions",
    location: "Birmingham",
    image: "/testimonials/emma.jpg",
    rating: 5,
    quote: "Professional, results-driven, and always available. WebforLeads doesn't just deliver leads - they deliver the right leads that convert into long-term customers.",
    results: "300% revenue growth",
    industry: "Technology",
    featured: true
  },
  {
    id: 4,
    name: "Michael Chen",
    role: "Owner",
    company: "Chen's Restaurant Group",
    location: "Leeds",
    image: "/testimonials/michael.jpg",
    rating: 5,
    quote: "Our online bookings increased by 400% in just 6 months. The local SEO work they did for our Leeds locations has been game-changing for our business.",
    results: "400% booking increase",
    industry: "Hospitality",
    featured: false
  },
  {
    id: 5,
    name: "Lisa Parker",
    role: "Director",
    company: "Parker Dental Practice",
    location: "Bristol",
    image: "/testimonials/lisa.jpg",
    rating: 5,
    quote: "We've been working with WebforLeads for 2 years now. Consistent results, transparent reporting, and genuine partnership. Couldn't recommend them more highly.",
    results: "250% patient growth",
    industry: "Healthcare",
    featured: false
  },
  {
    id: 6,
    name: "David Roberts",
    role: "Managing Director",
    company: "Roberts Construction",
    location: "Glasgow",
    image: "/testimonials/david.jpg",
    rating: 5,
    quote: "From website redesign to ongoing SEO, WebforLeads has been instrumental in our growth. We're now the go-to construction company in Glasgow.",
    results: "500% online visibility",
    industry: "Construction",
    featured: false
  }
];

const stats = [
  { number: "98%", label: "Client Satisfaction", icon: <Star className="h-5 w-5" /> },
  { number: "500+", label: "Success Stories", icon: <Users className="h-5 w-5" /> },
  { number: "£25M+", label: "Revenue Generated", icon: <TrendingUp className="h-5 w-5" /> },
  { number: "22+", label: "UK Cities", icon: <Award className="h-5 w-5" /> }
];

export default function SectionTestimonials() {
  const featuredTestimonials = testimonials.filter(t => t.featured);

  return (
    <section className="py-32 bg-white relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="space-y-6">
            <div className="inline-flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-[#4CAF50]/15 via-white/80 to-[#1E3A8A]/10 border border-[#4CAF50]/30 rounded-full backdrop-blur-sm hover:shadow-lg transition-all duration-300">
              <CheckCircle className="h-5 w-5 text-[#4CAF50] animate-pulse" />
              <span className="text-[#1E3A8A] font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
            </div>
            
            <h2 className="text-5xl lg:text-7xl font-black text-[#1E3A8A] leading-tight mb-6">
              Real Results from
              <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                Real Businesses
              </span>
            </h2>
            
            <p className="text-2xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-medium">
              Don't just take our word for it. Here's what UK business owners say about working with WebforLeads and achieving extraordinary growth.
            </p>
          </motion.div>
        </motion.div>

        {/* Stats Bar */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {stats.map((stat, index) => (
            <motion.div 
              key={index} 
              variants={fadeInUp}
              className="text-center"
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-3xl flex items-center justify-center mx-auto mb-6 text-white shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {stat.icon}
              </motion.div>
              <div className="text-4xl lg:text-5xl font-black text-[#1E3A8A] mb-3">{stat.number}</div>
              <div className="text-slate-600 font-bold text-lg">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Featured Testimonials */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {featuredTestimonials.map((testimonial, index) => (
            <motion.div 
              key={testimonial.id} 
              variants={fadeInUp}
              whileHover={{ y: -8 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
            >
              <Card className="relative h-full border-0 bg-white/95 backdrop-blur-xl shadow-2xl rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-[#FF6B35]/10 transition-all duration-500 hover:scale-[1.02]">
                {/* Enhanced Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/8 via-transparent to-[#1E3A8A]/8 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                
                {/* Premium border accent */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#1E3A8A]/10 via-[#FF6B35]/5 to-[#1E3A8A]/10 p-px rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className="w-full h-full rounded-3xl bg-white/95"></div>
                </div>
                
                <CardContent className="relative p-8">
                  {/* Enhanced Quote Icon */}
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-3xl flex items-center justify-center mb-8 text-white shadow-lg group-hover:scale-110 transition-transform duration-300"
                    whileHover={{ rotate: 5 }}
                  >
                    <Quote className="h-8 w-8" />
                  </motion.div>
                  
                  {/* Rating */}
                  <div className="flex space-x-1 mb-8">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <motion.div
                        key={i}
                        whileHover={{ scale: 1.2, rotate: 15 }}
                        transition={{ delay: i * 0.1 }}
                      >
                        <Star className="h-6 w-6 fill-[#FF6B35] text-[#FF6B35]" />
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Quote */}
                  <blockquote className="text-slate-700 text-xl leading-relaxed mb-10 italic font-medium">
                    "{testimonial.quote}"
                  </blockquote>
                  
                  {/* Results Badge */}
                  <div className="mb-8">
                    <Badge className="bg-gradient-to-r from-[#4CAF50]/15 to-[#4CAF50]/10 text-[#4CAF50] border border-[#4CAF50]/30 font-bold px-4 py-2 rounded-full text-sm">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      {testimonial.results}
                    </Badge>
                  </div>
                  
                  {/* Author */}
                  <div className="flex items-center space-x-5">
                    <motion.div
                      className="w-16 h-16 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center text-white font-black text-lg shadow-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </motion.div>
                    <div>
                      <div className="font-black text-xl text-[#1E3A8A]">{testimonial.name}</div>
                      <div className="text-[#FF6B35] font-bold text-lg">{testimonial.role}</div>
                      <div className="text-slate-600 font-medium">{testimonial.company} • {testimonial.location}</div>
                    </div>
                  </div>
                </CardContent>

                {/* Enhanced Decorative Border */}
                <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700 origin-left rounded-b-3xl" />
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="text-center"
        >
          <div className="inline-flex items-center space-x-10 px-10 py-6 bg-white/90 backdrop-blur-xl border border-[#1E3A8A]/20 rounded-3xl shadow-2xl hover:shadow-[#FF6B35]/10 transition-all duration-300">
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.05 }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <span className="text-[#1E3A8A] font-bold text-lg">Google Partner Certified</span>
            </motion.div>
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.05 }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center">
                <Star className="h-5 w-5 text-white" />
              </div>
              <span className="text-[#1E3A8A] font-bold text-lg">5-Star Rated Agency</span>
            </motion.div>
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.05 }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center">
                <Award className="h-5 w-5 text-white" />
              </div>
              <span className="text-[#1E3A8A] font-bold text-lg">Industry Awards</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
