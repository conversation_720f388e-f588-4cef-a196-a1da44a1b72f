"use client";

// This is a complete, corrected, and self-contained code file.
//
// Changes Made:
// 1. Syntax Error Fixed: The isolated object with 'hidden' and 'visible' properties has been assigned to a constant `fadeInUp`.
// 2. Duplicate Removed: The two `AboutPage` function declarations have been merged into one.
// 3. Completeness: Placeholder components (`Card`, `Badge`, `AppointmentForm`, `ContactForm`) and necessary imports (`react`, `framer-motion`, `lucide-react`) have been added to make the file runnable without external dependencies.
// 4. Structured Data: The JSON-LD script for SEO has been correctly placed inside the main component.
// 5. Next.js Metadata: A comment has been added to clarify that the static `metadata` object cannot be exported from a "use client" component and should be moved to a server-side layout or page file.

import React from 'react';
import { motion } from 'framer-motion';
import AppointmentForm from "@/components/forms/appointment-form";
import ContactForm from "@/components/forms/contact-form";

import {
  Target, Heart, Lightbulb, Shield, Building, Users, TrendingUp, Trophy, CheckCircle, Star, Coffee
} from 'lucide-react';

// Card and Badge placeholders retained for layout wrappers used in this file only
const Card = ({ className, children, ...props }: { className?: string; children: React.ReactNode; [key: string]: unknown }) => (
  <div className={className} {...props}>
    {children}
  </div>
);

const Badge = ({ className, children, ...props }: { className?: string; children: React.ReactNode; variant?: string; [key: string]: unknown }) => (
  <span className={className} {...props}>
    {children}
  </span>
);


// --- Animation Variants & Data Constants ---

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const values = [
  {
    icon: <Target className="h-6 w-6" />,
    title: "Results-Driven",
    description: "Every strategy, every campaign, every decision is made with one goal in mind: delivering measurable results that grow your business."
  },
  {
    icon: <Heart className="h-6 w-6" />,
    title: "Client-Centric",
    description: "Your success is our success. We build long-term partnerships based on trust, transparency, and exceptional service."
  },
  {
    icon: <Lightbulb className="h-6 w-6" />,
    title: "Innovation First",
    description: "We stay ahead of digital trends and algorithm changes to ensure your business always has a competitive edge."
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Ethical Practices",
    description: "We follow white-hat practices and Google's guidelines to build sustainable, long-term growth for your business."
  }
];

const team = [
  {
    name: "Sarah Johnson",
    role: "Founder & CEO",
    bio: "15+ years in digital marketing with a track record of scaling businesses from startup to £10M+ revenue.",
    image: "/team/sarah.jpg",
    specialties: ["Digital Strategy", "Business Growth", "Team Leadership"]
  },
  {
    name: "Michael Chen",
    role: "Head of SEO",
    bio: "Former Google employee with deep expertise in search algorithms and technical SEO optimization.",
    image: "/team/michael.jpg",
    specialties: ["Technical SEO", "Algorithm Updates", "Local SEO"]
  },
  {
    name: "Emma Williams",
    role: "Creative Director",
    bio: "Award-winning designer with a passion for creating websites that are both beautiful and conversion-focused.",
    image: "/team/emma.jpg",
    specialties: ["Web Design", "UX/UI", "Brand Identity"]
  },
  {
    name: "James Thompson",
    role: "PPC Specialist",
    bio: "Google Ads certified expert who has managed over £5M in ad spend with consistently profitable returns.",
    image: "/team/james.jpg",
    specialties: ["Google Ads", "PPC Strategy", "Conversion Optimization"]
  }
];

const stats = [
  { number: "2018", label: "Founded", icon: <Building className="h-6 w-6" /> },
  { number: "500+", label: "Clients Served", icon: <Users className="h-6 w-6" /> },
  { number: "£25M+", label: "Revenue Generated", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "98%", label: "Client Retention", icon: <Trophy className="h-6 w-6" /> }
];

const milestones = [
  {
    year: "2018",
    title: "WebforLeads Founded",
    description: "Started with a mission to help UK businesses dominate their digital markets."
  },
  {
    year: "2019",
    title: "First 100 Clients",
    description: "Reached our first major milestone by helping 100 businesses transform their online presence."
  },
  {
    year: "2021",
    title: "Google Partner Status",
    description: "Achieved Google Partner certification, recognizing our expertise and client success rates."
  },
  {
    year: "2022",
    title: "National Expansion",
    description: "Expanded services across all major UK cities, helping businesses nationwide."
  },
  {
    year: "2023",
    title: "Award Recognition",
    description: "Recognized as one of the UK's top digital marketing agencies by industry publications."
  },
  {
    year: "2024",
    title: "Innovation Hub",
    description: "Launched our innovation hub to stay ahead of AI and emerging digital marketing trends."
  }
];

// --- Main Page Component ---
export default function AboutPage() {
  return (
    <>
      {/* Structured Data (JSON-LD) for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "AboutPage",
            "name": "About WebforLeads",
            "description": "Learn about WebforLeads - a leading web design and digital marketing agency with expert team and proven results.",
            "url": "https://webforleads.uk/about",
            "mainEntity": {
              "@type": "Organization",
              "name": "WebforLeads",
              "url": "https://webforleads.uk",
              "description": "Professional web design and digital marketing agency delivering exceptional results for businesses worldwide.",
              "foundingDate": "2020",
              "numberOfEmployees": "25-50",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "GB",
                "addressRegion": "England"
              },
              "sameAs": [
                "https://www.linkedin.com/company/webforleads",
                "https://twitter.com/webforleads",
                "https://www.facebook.com/webforleads"
              ]
            }
          })
        }}
      />
      
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="relative bg-white overflow-hidden min-h-screen">
          <div className="h-20 lg:h-24"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
            <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">
              <motion.div
                initial="hidden"
                animate="visible"
                variants={staggerContainer}
                className="lg:col-span-7 space-y-10"
              >
                <motion.div variants={fadeInUp} className="space-y-10">
                  <div className="space-y-8">
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8, y: 20 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                    >
                      <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                        <Users className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                        <span className="font-bold text-sm tracking-wide">ABOUT WEBFORLEADS</span>
                      </Badge>
                    </motion.div>
                    <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9] mb-8">
                      <motion.span
                        className="block text-[#1E3A8A] mb-2"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3, duration: 0.8 }}
                      >
                        Your Dedicated
                      </motion.span>
                      <motion.span
                        className="block relative"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.8 }}
                      >
                        <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent">
                          Digital Growth Partner.
                        </span>
                        <motion.div
                          className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                          initial={{ width: 0 }}
                          animate={{ width: "280px" }}
                          transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                        />
                      </motion.span>
                    </h1>
                  </div>
                  <motion.div
                    className="space-y-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.8 }}
                  >
                    <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                      Since 2018, we&apos;ve been helping UK businesses transform their digital presence into{" "}
                      <span className="text-[#FF6B35] relative">
                        powerful revenue engines
                        <motion.div
                          className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ delay: 1.5, duration: 0.8 }}
                        />
                      </span>
                      .
                    </h2>
                    <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                      We&apos;re not just another agency – we&apos;re your strategic partners in growth. Our team combines data-driven strategies with creative excellence to deliver measurable results for businesses across the UK.
                    </p>
                  </motion.div>
                </motion.div>
                <motion.div
                  variants={fadeInUp}
                  className="pt-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                >
                  <div className="flex flex-wrap items-center gap-8">
                    <motion.div
                      className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#4CAF50]/20 hover:shadow-lg transition-all duration-300"
                      whileHover={{ scale: 1.05, y: -2 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <motion.div
                        className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                        whileHover={{ rotate: 10 }}
                      >
                        <Users className="h-4 w-4 text-white" />
                      </motion.div>
                      <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#4CAF50] transition-colors duration-200">
                        Trusted by 500+ UK Businesses
                      </span>
                    </motion.div>
                    <motion.div
                      className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#1E3A8A]/20 hover:shadow-lg transition-all duration-300"
                      whileHover={{ scale: 1.05, y: -2 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <motion.div
                        className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                        whileHover={{ rotate: 10 }}
                      >
                        <CheckCircle className="h-4 w-4 text-white" />
                      </motion.div>
                      <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#1E3A8A] transition-colors duration-200">
                        Google Partner Certified
                      </span>
                    </motion.div>
                    <motion.div
                      className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#FF6B35]/20 hover:shadow-lg transition-all duration-300"
                      whileHover={{ scale: 1.05, y: -2 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <div className="flex space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <motion.div
                            key={i}
                            whileHover={{ scale: 1.2, rotate: 15 }}
                            transition={{ delay: i * 0.1 }}
                          >
                            <Star className="h-4 w-4 fill-[#FF6B35] text-[#FF6B35]" />
                          </motion.div>
                        ))}
                      </div>
                      <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                        5-Star Rated on Clutch
                      </span>
                    </motion.div>
                  </div>
                </motion.div>
              </motion.div>
              <motion.div
                initial="hidden"
                animate="visible"
                variants={fadeInUp}
                className="lg:col-span-5"
              >
                <AppointmentForm />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="text-center mb-16"
            >
              <motion.h2 variants={fadeInUp} className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Proven Track Record
              </motion.h2>
              <motion.p variants={fadeInUp} className="text-xl text-gray-600 max-w-3xl mx-auto">
                Numbers that speak louder than words. Here&apos;s the impact we&apos;ve made since our founding.
              </motion.p>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="grid grid-cols-2 lg:grid-cols-4 gap-8"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUp}
                  whileHover={{ y: -8, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300, damping: 25 }}
                >
                  <Card className="relative p-8 border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-blue-900/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <motion.div
                      className="relative w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white shadow-lg shadow-orange-500/25"
                      whileHover={{ rotate: 5, scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      {stat.icon}
                    </motion.div>
                    <div className="relative text-4xl lg:text-5xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                      {stat.number}
                    </div>
                    <div className="relative text-gray-600 font-semibold text-lg group-hover:text-gray-700 transition-colors duration-300">
                      {stat.label}
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-orange-500 to-blue-900 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-32 bg-white relative overflow-hidden">
          <div className="absolute inset-0">
            <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-blue-900/5 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-20 left-20 w-72 h-72 bg-gradient-to-br from-orange-500/5 to-transparent rounded-full blur-3xl" />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="text-center mb-20"
            >
              <motion.div variants={fadeInUp} className="space-y-6">
                <div className="inline-flex items-center space-x-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-full">
                  <Heart className="h-4 w-4 text-orange-500" />
                  <span className="text-orange-700 font-semibold">Our Core Values</span>
                </div>
                <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  What Drives Us
                  <span className="block text-orange-600">Every Single Day</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  These principles guide everything we do and ensure we deliver exceptional results for every client.
                </p>
              </motion.div>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="grid grid-cols-1 md:grid-cols-2 gap-8"
            >
              {values.map((value, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUp}
                  whileHover={{ y: -8 }}
                  transition={{ type: "spring", stiffness: 300, damping: 25 }}
                >
                  <Card className="relative h-full p-10 border-0 bg-gradient-to-br from-white to-slate-50/50 shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-blue-900/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative">
                      <motion.div
                        className="w-20 h-20 bg-gradient-to-r from-orange-500 to-orange-600 rounded-3xl flex items-center justify-center mb-8 text-white shadow-lg shadow-orange-500/25 group-hover:shadow-xl group-hover:shadow-orange-500/40"
                        whileHover={{ rotate: 5, scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 400, damping: 25 }}
                      >
                        {value.icon}
                      </motion.div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-6 group-hover:text-orange-600 transition-colors duration-300">
                        {value.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed text-lg">
                        {value.description}
                      </p>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-orange-500 to-blue-900 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="text-center mb-20"
            >
              <motion.div variants={fadeInUp} className="space-y-6">
                <div className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-50 border border-blue-200 rounded-full">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span className="text-blue-700 font-semibold">Our Expert Team</span>
                </div>
                <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Meet the Minds Behind
                  <span className="block text-blue-900">Your Success</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  Our diverse team of digital marketing experts brings together decades of experience and proven results.
                </p>
              </motion.div>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            >
              {team.map((member, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUp}
                  whileHover={{ y: -12, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300, damping: 25 }}
                >
                  <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-900/5 via-transparent to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative p-8 text-center">
                      <motion.div
                        className="relative w-28 h-28 mx-auto mb-6"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 25 }}
                      >
                        <div className="w-full h-full bg-gradient-to-r from-blue-900 to-blue-800 rounded-3xl flex items-center justify-center text-white text-2xl font-bold shadow-xl shadow-blue-900/25 group-hover:shadow-2xl group-hover:shadow-blue-900/40 transition-all duration-500">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                        </div>
                      </motion.div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-900 transition-colors duration-300">
                        {member.name}
                      </h3>
                      <p className="text-orange-600 font-semibold mb-4 text-lg">
                        {member.role}
                      </p>
                      <p className="text-gray-600 text-sm leading-relaxed mb-6">
                        {member.bio}
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {member.specialties.map((specialty, idx) => (
                          <Badge
                            key={idx}
                            variant="outline"
                            className="bg-blue-50 border-blue-200 text-blue-700 text-xs font-medium hover:bg-blue-100 transition-colors duration-200 px-2 py-1 rounded-md"
                          >
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-900 to-orange-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="py-24 bg-gradient-to-b from-white to-[#F9FAFB]">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="text-center mb-16"
            >
              <motion.h2
                variants={fadeInUp}
                className="text-3xl lg:text-4xl font-bold text-[#111827] mb-6"
              >
                Our Growth Journey
              </motion.h2>
              <motion.p
                variants={fadeInUp}
                className="text-xl text-[#6B7280] max-w-3xl mx-auto"
              >
                From a small startup to a leading UK digital marketing agency – here&apos;s how we&apos;ve grown alongside our clients.
              </motion.p>
            </motion.div>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="space-y-8"
            >
              {milestones.map((milestone, index) => (
                <motion.div key={index} variants={fadeInUp}>
                  <Card className="p-8 border-0 bg-white shadow-lg shadow-black/5 rounded-2xl">
                    <div className="flex flex-col lg:flex-row items-start lg:items-center space-y-4 lg:space-y-0 lg:space-x-8">
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white font-bold text-lg">
                          {milestone.year}
                        </div>
                      </div>
                      <div className="flex-grow">
                        <h3 className="text-2xl font-bold text-[#111827] mb-2">{milestone.title}</h3>
                        <p className="text-[#6B7280] leading-relaxed">{milestone.description}</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Contact Form Section */}
        <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="text-center mb-16"
            >
              <motion.div variants={fadeInUp} className="space-y-6">
                <div className="inline-flex items-center space-x-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-full">
                  <Coffee className="h-4 w-4 text-orange-500" />
                  <span className="text-orange-700 font-semibold">Get In Touch</span>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                  Ready to Partner
                  <span className="block text-orange-600">with Us?</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  Let&apos;s discuss how our expertise and proven track record can help transform your business.
                  Send us a message and we&apos;ll get back to you within 2 hours.
                </p>
              </motion.div>
            </motion.div>
            <motion.div variants={fadeInUp}>
              <ContactForm />
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
}