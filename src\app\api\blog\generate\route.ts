import { NextRequest, NextResponse } from 'next/server';
import { saveBlogPost, getExistingBlogTitles } from '@/lib/blog-storage';
import { saveBase64Image, getImageUrl } from '@/lib/imageUtils';

const GEMINI_API_KEY = 'AIzaSyA-EVzCpQwPpKCnsUe2CRqdWIo8LTIKVnM';
const GEMINI_TEXT_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
const GEMINI_IMAGE_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent';

// Lightweight logging helpers for large strings/objects
const LOG_FULL_TEXT = process.env.LOG_FULL_TEXT === '1';
function logPreview(label: string, value: string, limit = 800) {
  try {
    const len = value ? value.length : 0;
    const effectiveLimit = LOG_FULL_TEXT ? Number.MAX_SAFE_INTEGER : limit;
    const shown = Math.min(effectiveLimit, len);
    const head = value ? value.slice(0, shown) : '';
    console.log(`${label} (length=${len}, showing ${shown})\n` + head + (len > shown ? '... [truncated]' : ''));
  } catch (e) {
    console.log(`${label}: <unprintable>`);
  }
}

function summarizeHeaders(headers: Headers): Record<string, string> {
  try {
    return Object.fromEntries(headers.entries());
  } catch {
    return {};
  }
}

interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    canonicalUrl: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterTitle: string;
    twitterDescription: string;
    twitterImage: string;
    author: string;
    category: string;
    tags: string[];
    readingTime: number;
    wordCount: number;
  };
}

// Check if two titles are similar (for duplicate detection)
function areTitlesSimilar(title1: string, title2: string): boolean {
  const normalize = (str: string) => str.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
  const words1 = normalize(title1).split(/\s+/);
  const words2 = normalize(title2).split(/\s+/);

  // Check if titles are identical
  if (normalize(title1) === normalize(title2)) {
    return true;
  }

  // Check if one title contains most words from the other
  const commonWords = words1.filter(word =>
    word.length > 3 && words2.includes(word)
  );

  // If more than 60% of significant words are common, consider similar
  const similarity = commonWords.length / Math.min(words1.length, words2.length);
  return similarity > 0.6;
}

// getExistingBlogTitles function moved to @/lib/blog-storage.ts

// Step 1: Generate single unique blog post idea with blacklist
async function generateBlogIdea(): Promise<string> {
  // Get existing blog titles to avoid duplicates
  const existingTitles = getExistingBlogTitles();

  let blacklistSection = '';
  if (existingTitles.length > 0) {
    blacklistSection = `

CRITICAL: DO NOT create content similar to these existing blog posts (BLACKLIST):
${existingTitles.map((title, index) => `${index + 1}. ${title}`).join('\n')}

Your idea must be completely different and cover a fresh topic not already covered.`;
  }

  const prompt = `Generate 1 unique blog post idea (not a title, but a topic/concept) for a web agency that would attract high traffic and engagement.

The idea should be:
- A specific topic or concept to write about
- Actionable and practical for business owners
- SEO-friendly and trending in digital marketing
- Valuable for web agency clients
- COMPLETELY UNIQUE and different from existing content${blacklistSection}

Return only the blog post idea/topic in one clear sentence. Do NOT return a title - return the concept/topic to write about.

Example format: "How to optimize website conversion rates using psychological triggers and user behavior analysis"

Your unique blog post idea:`;

  try {
    console.log('✉️ GEMINI TEXT API REQUEST (idea)');
    console.log('📍 URL:', `${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY.substring(0, 10)}...`);
    console.log('📝 Prompt length:', prompt.length);
    logPreview('📝 Prompt preview', prompt, 600);

    const response = await fetch(`${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.9,
          maxOutputTokens: 500,
        }
      }),
    });

    console.log('📡 Response status:', response.status, response.statusText);
    console.log('📡 Response headers:', summarizeHeaders(response.headers));
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const generatedText = data.candidates[0].content.parts[0].text;

    console.log('✅ Raw idea response length:', generatedText.length);
    logPreview('🧪 Raw idea response preview', generatedText, 600);
    // Clean up the response to get just the idea
    const idea = generatedText.trim()
      .replace(/^(Your unique blog post idea:|Blog post idea:|Idea:)/i, '')
      .replace(/^\d+\.?\s*/, '')
      .trim();

    console.log('Generated blog idea:', idea);

    if (!idea || idea.length < 10) {
      throw new Error('Generated idea is too short or empty');
    }

    return idea;
  } catch (error) {
    console.error('Error generating blog ideas:', error);
    throw error;
  }
}

// Step 2: Generate comprehensive blog post content with SEO metadata
async function generateBlogContent(idea: string): Promise<{
  title: string;
  content: string;
  excerpt: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    ogTitle: string;
    ogDescription: string;
    twitterTitle: string;
    twitterDescription: string;
    author: string;
    category: string;
    tags: string[];
  };
}> {
  const prompt = `Write an extremely comprehensive, SEO-optimized blog post about: "${idea}"

Requirements:
- Write for a web agency audience (business owners, marketers, entrepreneurs)
- Include actionable tips and strategies with real-world data and current statistics
- Use engaging, professional tone
- Include relevant statistics and insights from recent studies and reports
- Structure with clear headings and subheadings
- Add ONE image placeholder per H2 section in this EXACT format: [detailed description of specific image needed for this section]
- Make it 2000+ words
- Include a compelling introduction and conclusion
- Add practical examples and case studies from real companies
- MUST include 5-8 external links to authoritative sources using HTML format: <a href="https://example.com" target="_blank" rel="noopener noreferrer" class="external-link">descriptive text</a>
- MUST include 3-5 internal links using HTML format: <a href="/services/web-design" class="internal-link">service name</a>
- NEVER write URLs in parentheses or markdown format
- ALWAYS embed URLs as proper HTML anchor tags with descriptive text
- Reference current industry trends and recent developments with proper citations
- DO NOT include any HTML code blocks, code snippets, or technical code examples (EXCEPT for links)
- Focus on business strategies, marketing insights, and actionable advice
- Write in a conversational, expert tone without technical jargon
- DO NOT mention specific color codes, hex values, or design color schemes in the content
- IMPORTANT: Links should be the ONLY HTML elements in your content - use HTML anchor tags for all links
- DO NOT write "Internal Link Opportunity:" or "External Link:" - embed links naturally in the text

Structure:
1. Compelling title (H1)
2. Engaging introduction with hook
3. Main content with 5-7 sections (H2 headings)
4. ONE image placeholder after each H2 heading
5. Actionable tips and strategies
6. Real-world examples
7. Conclusion with call-to-action

Include ONE image placeholder per H2 section using the exact format: [detailed description of specific image for this section]

IMAGE PLACEHOLDER REQUIREMENTS:
- Place ONE image placeholder after each H2 heading (not multiple images per section)
- Each image should be specific to that H2 section content, NOT generic blog images
- Total images should match the number of H2 sections (typically 5-7 images)
- Examples of GOOD placeholders:
  * [dashboard showing website analytics and conversion metrics]
  * [before and after comparison of website personalization examples]
  * [flowchart of AI-powered user segmentation process]
  * [screenshot of personalization tools interface]
- Examples of BAD placeholders:
  * [web design image here]
  * [marketing image]
  * [generic business photo]

CRITICAL: Embed these internal links naturally in the content using HTML format with localhost:3000:
- <a href="http://localhost:3000/services/web-design" class="internal-link">web design services</a> - when discussing website creation
- <a href="http://localhost:3000/services/digital-marketing" class="internal-link">digital marketing</a> - when discussing marketing strategies
- <a href="http://localhost:3000/services/seo" class="internal-link">SEO services</a> - when discussing search optimization
- <a href="http://localhost:3000/portfolio" class="internal-link">portfolio</a> - when showing examples or case studies
- <a href="http://localhost:3000/about" class="internal-link">about us</a> - when discussing expertise or company background
- <a href="http://localhost:3000/contact" class="internal-link">contact</a> - in conclusion or call-to-action sections
- <a href="http://localhost:3000/blog" class="internal-link">blog</a> - when referencing other articles or insights

CRITICAL: Use ONLY these verified, working external links (choose relevant ones for your content):

VERIFIED INDUSTRY REPORTS & STATISTICS:
- <a href="https://blog.hubspot.com/marketing/state-of-marketing-report" target="_blank" rel="noopener noreferrer" class="external-link">HubSpot State of Marketing Report</a>
- <a href="https://www.salesforce.com/resources/research-reports/state-of-marketing/" target="_blank" rel="noopener noreferrer" class="external-link">Salesforce State of Marketing</a>
- <a href="https://www.mckinsey.com/capabilities/growth-marketing-and-sales/our-insights" target="_blank" rel="noopener noreferrer" class="external-link">McKinsey Marketing Insights</a>
- <a href="https://www.gartner.com/en/marketing/insights" target="_blank" rel="noopener noreferrer" class="external-link">Gartner Marketing Research</a>

VERIFIED TOOLS & PLATFORMS:
- <a href="https://marketingplatform.google.com/about/analytics/" target="_blank" rel="noopener noreferrer" class="external-link">Google Analytics</a>
- <a href="https://www.semrush.com/" target="_blank" rel="noopener noreferrer" class="external-link">SEMrush</a>
- <a href="https://ahrefs.com/" target="_blank" rel="noopener noreferrer" class="external-link">Ahrefs</a>
- <a href="https://moz.com/" target="_blank" rel="noopener noreferrer" class="external-link">Moz</a>

VERIFIED INDUSTRY PUBLICATIONS:
- <a href="https://searchengineland.com/" target="_blank" rel="noopener noreferrer" class="external-link">Search Engine Land</a>
- <a href="https://contentmarketinginstitute.com/" target="_blank" rel="noopener noreferrer" class="external-link">Content Marketing Institute</a>
- <a href="https://www.marketingland.com/" target="_blank" rel="noopener noreferrer" class="external-link">Marketing Land</a>

IMPORTANT: Only use these verified links. Do NOT create or guess URLs.

CRITICAL: Also generate comprehensive SEO metadata for maximum search engine optimization.

CRITICAL LINK FORMATTING REQUIREMENTS - USE HTML ONLY:
- External links: MUST be formatted as <a href="https://full-url.com" target="_blank" rel="noopener noreferrer" class="external-link">descriptive link text</a>
- Internal links: MUST be formatted as <a href="/page-url" class="internal-link">service/page name</a>
- NEVER use markdown format like [text](url)
- NEVER write standalone URLs like (https://example.com) or (/services/web-design)
- NEVER write text followed by URL like "McKinsey report(https://example.com)"

CORRECT HTML EXAMPLES:
- "According to the <a href="https://www.mckinsey.com/capabilities/growth-marketing-and-sales/our-insights" target="_blank" rel="noopener noreferrer" class="external-link">McKinsey Marketing Insights</a>, companies..."
- "Our <a href="http://localhost:3000/services/seo" class="internal-link">SEO services</a> can help optimize your content"
- "The <a href="https://blog.hubspot.com/marketing/state-of-marketing-report" target="_blank" rel="noopener noreferrer" class="external-link">HubSpot State of Marketing Report</a> shows that 74% of customers..."

WRONG EXAMPLES (DO NOT DO THIS):
- "[McKinsey report](https://example.com)"
- "SEO services(/services/seo)"
- "According to a study(https://example.com)"
- "aMcKinsey report(https://example.com)"
- "OurSEO services(/services/seo)"

CRITICAL INSTRUCTION: Every single URL MUST be embedded as proper HTML anchor tags with descriptive text. NO EXCEPTIONS. Use HTML format only, not markdown.

Return the response in this EXACT format:
TITLE: [Blog post title]
EXCERPT: [2-3 sentence excerpt for SEO]
META_TITLE: [SEO-optimized title 50-60 characters with focus keyword]
META_DESCRIPTION: [SEO meta description 150-160 characters with focus keyword and call-to-action]
KEYWORDS: [Comma-separated list of 15-20 relevant keywords including long-tail keywords]
FOCUS_KEYWORD: [Primary focus keyword for SEO]
OG_TITLE: [OpenGraph title optimized for social sharing]
OG_DESCRIPTION: [OpenGraph description for social media]
TWITTER_TITLE: [Twitter-optimized title]
TWITTER_DESCRIPTION: [Twitter-optimized description]
AUTHOR: [Author name - use "WebforLeads Expert Team"]
CATEGORY: [Main category like "Digital Marketing", "Web Design", "SEO", etc.]
TAGS: [Comma-separated list of 5-8 relevant tags]
CONTENT: [Full blog post content with HTML formatting]`;

  try {
    console.log('✉️ GEMINI TEXT API REQUEST (content)');
    console.log('📍 URL:', `${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY.substring(0, 10)}...`);
    console.log('📝 Prompt length:', prompt.length);
    logPreview('📝 Prompt preview', prompt, 800);

    const response = await fetch(`${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        tools: [{
          google_search: {}
        }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 4000,
        }
      }),
    });

    console.log('📡 Response status:', response.status, response.statusText);
    console.log('📡 Response headers:', summarizeHeaders(response.headers));
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Content API error body (truncated):', errorText.slice(0, 1500));
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const fullResponse = data.candidates[0].content.parts[0].text;

    console.log('🧾 Full text response length:', fullResponse.length);
    logPreview('🧾 Full response preview', fullResponse, 1200);
    // Parse the response (use line-anchored patterns; support multiple content markers)
    const titleMatch = fullResponse.match(/^TITLE:\s*(.+)$/m);
    const excerptMatch = fullResponse.match(/^EXCERPT:\s*(.+)$/m);
    const metaTitleMatch = fullResponse.match(/^META_TITLE:\s*(.+)$/m);
    const metaDescriptionMatch = fullResponse.match(/^META_DESCRIPTION:\s*(.+)$/m);
    const keywordsMatch = fullResponse.match(/^KEYWORDS:\s*(.+)$/m);
    const focusKeywordMatch = fullResponse.match(/^FOCUS_KEYWORD:\s*(.+)$/m);
    const ogTitleMatch = fullResponse.match(/^OG_TITLE:\s*(.+)$/m);
    const ogDescriptionMatch = fullResponse.match(/^OG_DESCRIPTION:\s*(.+)$/m);
    const twitterTitleMatch = fullResponse.match(/^TWITTER_TITLE:\s*(.+)$/m);
    const twitterDescriptionMatch = fullResponse.match(/^TWITTER_DESCRIPTION:\s*(.+)$/m);
    const authorMatch = fullResponse.match(/^AUTHOR:\s*(.+)$/m);
    const categoryMatch = fullResponse.match(/^CATEGORY:\s*(.+)$/m);
    const tagsMatch = fullResponse.match(/^TAGS:\s*(.+)$/m);
    // Capture the entire remainder of the string after the content marker (case-insensitive)
    const contentMatch = fullResponse.match(/(?:^|\n)(?:CONTENT|BODY|ARTICLE|MAIN_CONTENT|BLOG):\s*([\s\S]*)$/i);

    console.log('🔎 Parsing markers present:', {
      title: !!titleMatch, excerpt: !!excerptMatch, content: !!contentMatch,
      metaTitle: !!metaTitleMatch, metaDescription: !!metaDescriptionMatch, keywords: !!keywordsMatch, focusKeyword: !!focusKeywordMatch,
      ogTitle: !!ogTitleMatch, ogDescription: !!ogDescriptionMatch, twitterTitle: !!twitterTitleMatch, twitterDescription: !!twitterDescriptionMatch,
      author: !!authorMatch, category: !!categoryMatch, tags: !!tagsMatch
    });
    // Attempt fallback extraction if content is missing/empty
    let parsedContent = contentMatch && contentMatch[1] ? contentMatch[1].trim() : '';
    if (!parsedContent || parsedContent.length < 50) {
      console.log('⚠ Parsed content empty/short. Attempting alternate extraction…');
      const markers = ['CONTENT:', 'BODY:', 'ARTICLE:', 'MAIN_CONTENT:', 'BLOG:'];
      let idx = -1;
      let markerUsed = '';
      for (const mk of markers) {
        const i = fullResponse.indexOf(mk);
        if (i !== -1) { idx = i; markerUsed = mk; break; }
      }
      if (idx !== -1) {
        const slice = fullResponse.slice(idx + markerUsed.length).trim();
        logPreview('🔎 Alt extraction slice after marker', slice, 600);
        if (slice.length > 50) parsedContent = slice;
      }
    }

    console.log('🧩 Parsed content length:', parsedContent.length);
    if (parsedContent) {
      logPreview('🧩 Parsed content preview', parsedContent, 600);
    }

    // Fallback: if still no content, request CONTENT-only from Gemini to avoid losing a good metadata response
    if (!parsedContent) {
      console.log('🛟 Fallback: requesting CONTENT-only from Gemini');
      const titleText = titleMatch ? titleMatch[1].trim() : idea;
      const fallbackPrompt = `Using the blog idea: "${idea}" and title: "${titleText}", write ONLY the CONTENT section as previously specified.\n\n` +
        `STRICT OUTPUT FORMAT: Return exactly one line starting with \nCONTENT: and then the full article content (no other fields).\n` +
        `REQUIREMENTS:\n` +
        `- 2000+ words, structured with clear H2/H3 sections.\n` +
        `- After each H2, include ONE image placeholder using: [detailed description of specific image for this section].\n` +
        `- Include 5-8 external links and 3-5 internal links using proper HTML <a> tags with descriptive text.\n` +
        `- External link examples (use only if relevant):\n` +
        `  <a href="https://blog.hubspot.com/marketing/state-of-marketing-report" target="_blank" rel="noopener noreferrer" class="external-link">HubSpot State of Marketing Report</a>\n` +
        `  <a href="https://www.mckinsey.com/capabilities/growth-marketing-and-sales/our-insights" target="_blank" rel="noopener noreferrer" class="external-link">McKinsey Marketing Insights</a>\n` +
        `- Internal links (use localhost base when relevant):\n` +
        `  <a href="http://localhost:3000/services/web-design" class="internal-link">web design services</a>,\n` +
        `  <a href="http://localhost:3000/services/seo" class="internal-link">SEO services</a>,\n` +
        `  <a href="http://localhost:3000/portfolio" class="internal-link">portfolio</a>.\n` +
        `- Use only HTML anchor tags for links. Do not use markdown. Do not include code blocks.\n` +
        `Return ONLY the CONTENT field as defined above.`;

      console.log('✉️ GEMINI TEXT API REQUEST (content-fallback)');
      console.log('📍 URL:', `${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY.substring(0, 10)}...`);
      console.log('📝 Prompt length:', fallbackPrompt.length);
      logPreview('📝 Fallback prompt preview', fallbackPrompt, 800);

      const fbResp = await fetch(`${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [{ parts: [{ text: fallbackPrompt }] }],
          generationConfig: { temperature: 0.6, maxOutputTokens: 4000 }
        })
      });
      console.log('📡 Fallback response status:', fbResp.status, fbResp.statusText);
      console.log('📡 Fallback response headers:', summarizeHeaders(fbResp.headers));
      if (!fbResp.ok) {
        const fbErr = await fbResp.text();
        logPreview('❌ Fallback error body', fbErr, 1200);
      } else {
        try {
          const fbData = await fbResp.json();
          const fbText = fbData.candidates?.[0]?.content?.parts?.[0]?.text || '';
          logPreview('🧾 Fallback raw text preview', fbText, 1200);
          const fbContentMatch = fbText.match(/(?:^|\n)CONTENT:\s*([\s\S]*)$/i);
          if (fbContentMatch && fbContentMatch[1] && fbContentMatch[1].trim().length > 50) {
            parsedContent = fbContentMatch[1].trim();
            console.log('✅ Fallback content parsed. Length:', parsedContent.length);
          } else if (fbText.trim().length > 50) {
            // If model ignored prefix, accept full text as content
            parsedContent = fbText.trim();
            console.log('✅ Fallback accepted entire text as content. Length:', parsedContent.length);
          } else {
            console.log('❌ Fallback did not yield valid content');
          }
        } catch (e) {
          console.log('❌ Failed to parse fallback JSON/text', e);
        }
      }
    }

    if (!titleMatch || !excerptMatch || !parsedContent) {
      throw new Error('Failed to parse blog content response');
    }

    return {
      title: titleMatch[1].trim(),
      content: parsedContent,
      excerpt: excerptMatch[1].trim(),
      seo: {
        metaTitle: metaTitleMatch ? metaTitleMatch[1].trim() : titleMatch[1].trim(),
        metaDescription: metaDescriptionMatch ? metaDescriptionMatch[1].trim() : excerptMatch[1].trim(),
        keywords: keywordsMatch ? keywordsMatch[1].trim() : '',
        focusKeyword: focusKeywordMatch ? focusKeywordMatch[1].trim() : '',
        ogTitle: ogTitleMatch ? ogTitleMatch[1].trim() : titleMatch[1].trim(),
        ogDescription: ogDescriptionMatch ? ogDescriptionMatch[1].trim() : excerptMatch[1].trim(),
        twitterTitle: twitterTitleMatch ? twitterTitleMatch[1].trim() : titleMatch[1].trim(),
        twitterDescription: twitterDescriptionMatch ? twitterDescriptionMatch[1].trim() : excerptMatch[1].trim(),
        author: authorMatch ? authorMatch[1].trim() : 'WebforLeads Expert Team',
        category: categoryMatch ? categoryMatch[1].trim() : 'Digital Marketing',
        tags: tagsMatch ? tagsMatch[1].trim().split(',').map((tag: string) => tag.trim()) : []
      }
    };
  } catch (error) {
    console.error('Error generating blog content:', error);
    throw error;
  }
}

// Step 3: Generate placeholder images (fallback approach)
async function generatePlaceholderImage(description: string): Promise<string> {
  // Create a simple SVG placeholder image with the description
  const svgContent = `
    <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:0.1" />
          <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:0.1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad1)" />
      <rect x="50" y="50" width="700" height="300" fill="white" stroke="#1E3A8A" stroke-width="2" rx="10" />
      <text x="400" y="180" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#1E3A8A">
        ${description.substring(0, 60)}${description.length > 60 ? '...' : ''}
      </text>
      <text x="400" y="220" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#FF6B35">
        Professional Image Placeholder
      </text>
      <circle cx="400" cy="120" r="30" fill="#FF6B35" opacity="0.2" />
      <circle cx="400" cy="120" r="20" fill="#1E3A8A" opacity="0.3" />
    </svg>
  `;

  // Convert SVG to base64
  const base64 = Buffer.from(svgContent).toString('base64');
  return base64;
}

// Step 3: Generate images using Gemini's native image generation with detailed logging
async function generateImage(description: string): Promise<string> {
  try {
    // Use Gemini's native image generation focusing on the specific section description
    const prompt = `Create a professional, high-quality image specifically about: ${description}. This is for a web agency blog post. Style: Modern design, business/marketing aesthetic, clean minimalist look, navy blue and orange color accents, high contrast, web-optimized quality. Focus on the specific topic described, not generic blog imagery.`;

    console.log('🖼️ GEMINI IMAGE API REQUEST DETAILS:');
    console.log('📍 URL:', `${GEMINI_IMAGE_API_URL}?key=${GEMINI_API_KEY.substring(0, 10)}...`);
    console.log('📝 Prompt:', prompt);
    console.log('📏 Prompt length:', prompt.length);

    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        responseModalities: ["TEXT", "IMAGE"]
      }
    };

    console.log('📦 Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(`${GEMINI_IMAGE_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response status text:', response.statusText);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      const truncated = errorText.length > 1500 ? errorText.slice(0, 1500) + '... [truncated]' : errorText;
      console.log('❌ Image API error body (truncated):', truncated);

      try {
        const errorJson = JSON.parse(errorText);
        // Summarize parsed error JSON keys to avoid huge logs
        const summary = {
          errorKeys: Object.keys(errorJson || {}),
          status: (errorJson && (errorJson.error?.status || errorJson.status)) || undefined,
          message: (errorJson && (errorJson.error?.message || errorJson.message)) || undefined,
        };
        console.log('❌ Parsed error JSON summary:', summary);
      } catch {
        console.log('❌ Could not parse error as JSON');
      }

      console.log(`❌ Gemini Image API returned ${response.status}, falling back to placeholder`);
      return await generatePlaceholderImage(description);
    }

    const responseText = await response.text();
    console.log('✅ Image API response received (length:', responseText.length, ') — body suppressed');

    let data;
    try {
      data = JSON.parse(responseText);
      const candidateCount = Array.isArray(data?.candidates) ? data.candidates.length : 0;
      const partCounts = candidateCount ? data.candidates.map((c: unknown) => (c as { content?: { parts?: unknown[] } })?.content?.parts?.length ?? 0) : [];
      console.log('📊 Parsed image JSON summary:', { candidateCount, partCounts });
    } catch {
      console.log('❌ Could not parse success response as JSON');
      return await generatePlaceholderImage(description);
    }

    // Find the image in the Gemini response
    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
      for (const part of data.candidates[0].content.parts) {
        // Check for both possible formats: inline_data and inlineData
        if (part.inline_data && part.inline_data.data) {
          console.log('🎉 Image found in response (inline_data)! Length:', part.inline_data.data.length);
          return part.inline_data.data; // Return base64 image data
        }
        if (part.inlineData && part.inlineData.data) {
          console.log('🎉 Image found in response (inlineData)! Length:', part.inlineData.data.length);
          return part.inlineData.data; // Return base64 image data
        }
      }
    }

    console.log('❌ No image found in Gemini response structure:');
    console.log('📊 Response structure:', Object.keys(data));
    if (data.candidates) {
      console.log('📊 Candidates array length:', data.candidates.length);
      if (data.candidates[0]) {
        console.log('📊 First candidate keys:', Object.keys(data.candidates[0]));
        if (data.candidates[0].content) {
          console.log('📊 Content keys:', Object.keys(data.candidates[0].content));
          if (data.candidates[0].content.parts) {
            console.log('📊 Parts array length:', data.candidates[0].content.parts.length);
            data.candidates[0].content.parts.forEach((part: unknown, index: number) => {
              console.log(`📊 Part ${index} keys:`, Object.keys(part as object));
            });
          }
        }
      }
    }

    console.log('🔄 Using placeholder instead');
    return await generatePlaceholderImage(description);
  } catch (error) {
    console.error('💥 Exception in generateImage:', error);
    console.error('💥 Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return await generatePlaceholderImage(description);
  }
}

// Step 4: Process all image placeholders with detailed logging
async function processImages(content: string, blogTitle: string): Promise<{ content: string; images: { [key: string]: string } }> {
  console.log('🔍 STARTING IMAGE PROCESSING');
  console.log('📄 Blog title:', blogTitle);
  console.log('📝 Content length:', content.length);

  const imageRegex = /\[([^\]]+)\]/g;
  const matches = [...content.matchAll(imageRegex)];

  console.log('🖼️ Found', matches.length, 'image placeholders:');
  matches.forEach((match, index) => {
    console.log(`  ${index + 1}. "${match[1]}" (full: "${match[0]}")`);
  });

  const images: { [key: string]: string } = {};
  let processedContent = content;

  for (let i = 0; i < matches.length; i++) {
    const match = matches[i];
    const description = match[1];
    const placeholder = match[0];

    try {
      console.log(`\n🎨 PROCESSING IMAGE ${i + 1}/${matches.length}`);
      console.log('📝 Description:', description);
      console.log('🔍 Placeholder:', placeholder);

      const imageData = await generateImage(description);
      const imageKey = `image_${i + 1}`;

      console.log('💾 Image data received, length:', imageData.length);
      console.log('🔑 Image key:', imageKey);

      // Save image as PNG file and get URL
      let imageUrl: string;

      if (imageData.startsWith('<svg')) {
        // For SVG, save as SVG file
        const svgFilename = saveBase64Image(Buffer.from(imageData).toString('base64'), 'content-svg');
        imageUrl = getImageUrl(svgFilename);
        console.log('🏷️ Saved as SVG:', svgFilename);
      } else {
        // For PNG, save as PNG file
        const pngFilename = saveBase64Image(imageData, 'content');
        imageUrl = getImageUrl(pngFilename);
        console.log('🏷️ Saved as PNG:', pngFilename);
      }

      // Store the image URL instead of base64 data
      images[imageKey] = imageUrl;

      // Replace placeholder with actual image tag using URL
      const imgTag = `<img src="${imageUrl}" alt="${description}" class="w-full h-auto rounded-lg shadow-lg my-8" />`;
      processedContent = processedContent.replace(placeholder, imgTag);

      console.log('✅ Successfully replaced placeholder with image tag');

      // Add delay to avoid rate limiting
      if (i < matches.length - 1) {
        console.log('⏳ Waiting 2 seconds before next image...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`❌ Error generating image for "${description}":`, error);
      // Replace with placeholder div if image generation fails
      const placeholderDiv = `<div class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center my-8"><span class="text-gray-500">${description}</span></div>`;
      processedContent = processedContent.replace(placeholder, placeholderDiv);
      console.log('🔄 Replaced with fallback div');
    }
  }

  console.log('\n✅ IMAGE PROCESSING COMPLETE');
  console.log('📊 Total images processed:', matches.length);
  console.log('💾 Images stored:', Object.keys(images).length);
  console.log('📝 Final content length:', processedContent.length);

  return { content: processedContent, images };
}

// Step 5: Clean content from HTML code blocks
function cleanContent(content: string): string {
  // Remove HTML code blocks that might appear in the content
  const cleanedContent = content
    // Remove ```html blocks
    .replace(/```html[\s\S]*?```/g, '')
    // Remove ``` blocks
    .replace(/```[\s\S]*?```/g, '')
    // Remove <code> blocks
    .replace(/<code[\s\S]*?<\/code>/g, '')
    // Remove <pre> blocks
    .replace(/<pre[\s\S]*?<\/pre>/g, '')
    // Clean up multiple line breaks
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    // Trim whitespace
    .trim();

  return cleanedContent;
}

// Step 6: Create slug from title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// Main API handler with detailed logging
export async function POST(request: NextRequest) {
  try {
    console.log('\n🚀 STARTING BLOG GENERATION PROCESS');
    console.log('⏰ Timestamp:', new Date().toISOString());

    // Step 1: Generate single unique blog idea with blacklist
    console.log('\n📝 STEP 1: GENERATING UNIQUE BLOG IDEA WITH BLACKLIST');
    const existingTitles = getExistingBlogTitles();
    console.log(`🚫 Blacklist: ${existingTitles.length} existing blog titles to avoid`);
    if (existingTitles.length > 0) {
      console.log('📚 Existing titles:', existingTitles.slice(0, 5).join(', ') + (existingTitles.length > 5 ? '...' : ''));
    }

    const selectedIdea = await generateBlogIdea();

    // Validate uniqueness of generated idea using improved similarity check
    const isUnique = !existingTitles.some(existingTitle =>
      areTitlesSimilar(selectedIdea, existingTitle)
    );

    if (!isUnique) {
      console.log(`🚫 Generated idea is too similar to existing content: "${selectedIdea}"`);
      throw new Error('Generated idea is too similar to existing blog posts. Please try again.');
    }

    console.log('💡 Generated unique blog idea:', selectedIdea);
    console.log('✅ Uniqueness validated successfully');

    // Step 2: Generate blog content with SEO
    console.log('\n📄 STEP 2: GENERATING BLOG CONTENT WITH SEO');
    const { title, content, excerpt, seo } = await generateBlogContent(selectedIdea);
    console.log('✅ Generated content for:', title);
    console.log('📊 Content stats:');
    console.log('  - Title length:', title.length);
    console.log('  - Content length:', content.length);
    console.log('  - Excerpt length:', excerpt.length);
    console.log('🔍 SEO stats:');
    console.log('  - Meta title:', seo.metaTitle);
    console.log('  - Meta description length:', seo.metaDescription.length);
    console.log('  - Focus keyword:', seo.focusKeyword);
    console.log('  - Keywords count:', seo.keywords.split(',').length);
    console.log('  - Category:', seo.category);
    console.log('  - Tags count:', seo.tags.length);

    // Step 3: Generate featured image
    console.log('\n🖼️ STEP 3: GENERATING FEATURED IMAGE');
    const featuredImageDescription = `Professional hero image representing the main concept of: ${title}. Show visual elements related to the specific topic, not generic web design. Modern, clean design with navy blue and orange accents, suitable for a web agency blog header.`;
    const featuredImageBase64 = await generateImage(featuredImageDescription);
    console.log('✅ Featured image generated, length:', featuredImageBase64.length);

    // Save featured image as PNG file
    const featuredImageFilename = saveBase64Image(featuredImageBase64, 'featured');
    const featuredImageUrl = getImageUrl(featuredImageFilename);
    console.log('💾 Featured image saved as:', featuredImageFilename);

    // Step 4: Clean content from HTML code blocks
    console.log('\n🧹 STEP 4: CLEANING CONTENT');
    const cleanedContent = cleanContent(content);
    console.log('✅ Content cleaned, length:', cleanedContent.length);

    // Step 5: Process content images
    console.log('\n🖼️ STEP 5: PROCESSING CONTENT IMAGES');
    const { content: finalContent, images } = await processImages(cleanedContent, title);

    // Step 6: Create final blog post with complete SEO
    console.log('\n📦 STEP 6: CREATING FINAL BLOG POST WITH SEO');
    const slug = createSlug(title);
    console.log('🔗 Generated slug:', slug);

    // Calculate reading time and word count
    const wordCount = finalContent.replace(/<[^>]*>/g, '').split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / 200); // 200 words per minute

    const blogPost: BlogPost = {
      title,
      content: finalContent,
      excerpt,
      slug,
      publishedAt: new Date().toISOString(),
      images,
      featuredImageUrl,
      seo: {
        ...seo,
        canonicalUrl: `https://webforleads.uk/blog/${slug}`,
        ogImage: featuredImageUrl,
        twitterImage: featuredImageUrl,
        readingTime,
        wordCount
      }
    };

    console.log('📊 Final blog post stats:');
    console.log('  - Title:', title);
    console.log('  - Slug:', slug);
    console.log('  - Final content length:', finalContent.length);
    console.log('  - Word count:', wordCount);
    console.log('  - Reading time:', readingTime, 'minutes');
    console.log('  - Images count:', Object.keys(images).length);
    console.log('  - Published at:', blogPost.publishedAt);
    console.log('🔍 SEO metadata:');
    console.log('  - Meta title:', blogPost.seo.metaTitle);
    console.log('  - Meta description:', blogPost.seo.metaDescription);
    console.log('  - Focus keyword:', blogPost.seo.focusKeyword);
    console.log('  - Category:', blogPost.seo.category);
    console.log('  - Tags:', blogPost.seo.tags.join(', '));
    console.log('  - Canonical URL:', blogPost.seo.canonicalUrl);

    // Step 7: Final uniqueness check and save
    console.log('\n💾 STEP 7: FINAL UNIQUENESS CHECK & SAVING BLOG POST');

    // Final check to ensure title is unique using improved similarity check
    const finalExistingTitles = getExistingBlogTitles();
    const titleExists = finalExistingTitles.some(existingTitle =>
      areTitlesSimilar(blogPost.title, existingTitle)
    );

    if (titleExists) {
      console.log('⚠️ Warning: Generated title is too similar to existing blog post');
      console.log('🔄 Adding uniqueness suffix to title');
      blogPost.title = `${blogPost.title} - ${new Date().getFullYear()} Edition`;
      blogPost.seo.metaTitle = `${blogPost.seo.metaTitle} - ${new Date().getFullYear()}`;
      blogPost.seo.ogTitle = `${blogPost.seo.ogTitle} - ${new Date().getFullYear()}`;
      blogPost.seo.twitterTitle = `${blogPost.seo.twitterTitle} - ${new Date().getFullYear()}`;

      // Update slug with new title
      blogPost.slug = createSlug(blogPost.title);
      blogPost.seo.canonicalUrl = `https://webforleads.uk/blog/${blogPost.slug}`;
    }

    console.log('✅ Title uniqueness confirmed:', blogPost.title);
    saveBlogPost(blogPost);

    // Revalidate sitemap after new blog post is created
    try {
      console.log('🗺️ Revalidating sitemap after new blog post...');
      await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/revalidate-sitemap`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'blog', slug: blogPost.slug })
      });
      console.log('✅ Sitemap revalidation triggered');
    } catch (sitemapError) {
      console.warn('⚠️ Failed to revalidate sitemap:', sitemapError);
      // Don't fail the blog creation if sitemap revalidation fails
    }

    console.log('\n🎉 BLOG POST GENERATION COMPLETE!');
    console.log('✅ Blog post generated and saved successfully!');

    return NextResponse.json({
      success: true,
      blogPost,
      message: 'Blog post generated and saved successfully with images!'
    });

  } catch (error) {
    console.error('Error in blog generation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate blog post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Blog generation API endpoint. Use POST to generate a new blog post.',
    endpoints: {
      'POST /api/blog/generate': 'Generate a new blog post with AI-generated content and images'
    }
  });
}
