"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users, Star, Target, Heart, Lightbulb, Trophy, Building, Shield, TrendingUp
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";
import ContactForm from "@/components/forms/contact-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const values = [
  {
    icon: <Target className="h-6 w-6" />,
    title: "Results-Driven",
    description: "Every strategy, every campaign, every decision is made with one goal in mind: delivering measurable results that grow your business."
  },
  {
    icon: <Heart className="h-6 w-6" />,
    title: "Client-Centric",
    description: "Your success is our success. We build long-term partnerships based on trust, transparency, and exceptional service."
  },
  {
    icon: <Lightbulb className="h-6 w-6" />,
    title: "Innovation First",
    description: "We stay ahead of digital trends and algorithm changes to ensure your business always has a competitive edge."
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Ethical Practices",
    description: "We follow white-hat practices and Google's guidelines to build sustainable, long-term growth for your business."
  }
];

const stats = [
  { number: "500+", label: "Projects Completed", icon: <Trophy className="h-6 w-6" /> },
  { number: "98%", label: "Client Satisfaction", icon: <Star className="h-6 w-6" /> },
  { number: "£5M+", label: "Revenue Generated", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "5+", label: "Years Experience", icon: <Building className="h-6 w-6" /> }
];

const team = [
  {
    name: "Sarah Johnson",
    role: "Founder & CEO",
    description: "10+ years in digital marketing with expertise in SEO and conversion optimization.",
    image: "/team/sarah.jpg"
  },
  {
    name: "Michael Chen",
    role: "Lead Developer",
    description: "Full-stack developer specializing in React, Node.js, and modern web technologies.",
    image: "/team/michael.jpg"
  },
  {
    name: "Emma Williams",
    role: "Creative Director",
    description: "Award-winning designer with a passion for creating beautiful, functional user experiences.",
    image: "/team/emma.jpg"
  },
  {
    name: "David Brown",
    role: "SEO Specialist",
    description: "Technical SEO expert with a track record of achieving first-page rankings for competitive keywords.",
    image: "/team/david.jpg"
  }
];

export default function AboutClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF] overflow-hidden min-h-screen">
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">
            
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-white/90 text-[#FF6B35] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Users className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">ABOUT OUR AGENCY</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-white mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Meet the Team Behind
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Your Success
                      </span>
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-white leading-relaxed max-w-3xl font-bold">
                    We're a passionate team of{" "}
                    <span className="text-[#FF6B35] relative">
                      digital experts, designers, and strategists
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                    {" "}dedicated to transforming businesses through innovative digital solutions.
                  </h2>

                  <p className="text-xl text-blue-100 leading-relaxed max-w-3xl font-medium">
                    Founded with a mission to help businesses thrive in the digital age, we combine creativity, technical expertise, and strategic thinking to deliver exceptional results for our clients.
                  </p>
                </motion.div>
              </motion.div>
            </motion.div>

            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>

          {/* Stats */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-blue-200 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Our Core
                <span className="block text-[#FF6B35]">Values</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                These principles guide everything we do and ensure we deliver exceptional value to our clients.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {values.map((value, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white flex-shrink-0">
                        {value.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{value.description}</p>
                      </div>
                    </div>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Meet Our
                <span className="block text-[#FF6B35]">Expert Team</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our diverse team of specialists brings together years of experience in web design, development, SEO, and digital marketing.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {team.map((member, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="relative p-6 text-center">
                    <div className="w-24 h-24 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                    <div className="text-[#FF6B35] font-semibold mb-4">{member.role}</div>
                    <p className="text-gray-600 text-sm leading-relaxed">{member.description}</p>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Ready to Work
                <span className="block text-orange-600">With Our Team?</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Let's discuss your project and see how our expertise can help transform your business.
              </p>
            </motion.div>
          </motion.div>

          <motion.div variants={fadeInUp}>
            <ContactForm />
          </motion.div>
        </div>
      </section>
    </div>
  );
}
