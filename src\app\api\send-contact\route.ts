import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ContactEmailTemplate } from '@/components/emails/contact-email-template';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, company, service, message } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Name, email, and message are required' },
        { status: 400 }
      );
    }

    // Format the service name for display
    const formatService = (service: string) => {
      const serviceMap: { [key: string]: string } = {
        'seo': 'Local SEO',
        'ppc': 'Google Ads',
        'web-design': 'Web Design',
        'social-media': 'Social Media Marketing',
        'full-service': 'Full Service Package'
      };
      return serviceMap[service] || service;
    };

    const submittedAt = new Date().toLocaleString('en-GB', {
      timeZone: 'Europe/London',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });

    // Send email to your business
    const { data, error } = await resend.emails.send({
      from: `WebforLeads <${process.env.FROM_EMAIL || '<EMAIL>'}>`,
      to: [process.env.TO_EMAIL || '<EMAIL>'],
      subject: `New Contact Message from ${name}${company ? ` (${company})` : ''}`,
      react: ContactEmailTemplate({
        name,
        email,
        phone,
        company,
        service: service ? formatService(service) : undefined,
        message,
        submittedAt
      }),
    });

    if (error) {
      console.error('Resend error:', error);
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      );
    }

    // Send a professional confirmation email to the client
    try {
      await resend.emails.send({
        from: `WebforLeads <${process.env.BUSINESS_EMAIL || '<EMAIL>'}>`,
        to: [email],
        subject: 'Thank you for contacting WebforLeads',
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Thank You - WebforLeads</title>
          </head>
          <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">

            <!-- Email Container -->
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc;">
              <tr>
                <td style="padding: 20px 0;">

                  <!-- Main Email Card -->
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border: 1px solid #e2e8f0;">

                    <!-- Header -->
                    <tr>
                      <td style="background-color: #1e3a8a; padding: 32px 32px 24px 32px; text-align: center; border-radius: 12px 12px 0 0; border-bottom: 4px solid #ff6b35;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                          <tr>
                            <td style="text-align: center;">
                              <!-- Logo -->
                              <div style="background-color: #ffffff; display: inline-block; padding: 12px 20px; border-radius: 8px; margin-bottom: 16px; border: 1px solid #e2e8f0;">
                                <h1 style="margin: 0; font-size: 20px; font-weight: 700; color: #1e3a8a; letter-spacing: -0.025em;">WebforLeads</h1>
                              </div>

                              <!-- Title -->
                              <h2 style="margin: 0 0 8px 0; font-size: 28px; font-weight: 700; color: #ffffff; letter-spacing: -0.025em;">Thank You, ${name}!</h2>

                              <!-- Subtitle -->
                              <p style="margin: 0; font-size: 14px; font-weight: 600; color: #ff6b35; text-transform: uppercase; letter-spacing: 0.05em;">Message Received Successfully</p>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- Success Alert -->
                    <tr>
                      <td style="padding: 32px 32px 0 32px;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                          <tr>
                            <td style="background-color: #10b981; padding: 16px 20px; border-radius: 8px; text-align: center; border: 1px solid #059669;">
                              <h3 style="margin: 0 0 4px 0; font-size: 16px; font-weight: 700; color: #ffffff; text-transform: uppercase; letter-spacing: 0.025em;">✓ Message Received</h3>
                              <p style="margin: 0; font-size: 14px; font-weight: 500; color: #ffffff;">We'll respond to your inquiry within 2 hours</p>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- Your Message -->
                    <tr>
                      <td style="padding: 24px 32px 0 32px;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">

                          <!-- Message Header -->
                          <tr>
                            <td style="background-color: #ff6b35; padding: 16px 20px; border-radius: 8px 8px 0 0; border-bottom: 3px solid #1e3a8a;">
                              <h3 style="margin: 0; font-size: 16px; font-weight: 700; color: #ffffff;">💬 Your Message</h3>
                            </td>
                          </tr>

                          <!-- Message Content -->
                          <tr>
                            <td style="padding: 20px;">
                              <div style="background-color: #ffffff; border: 1px solid #e2e8f0; border-left: 4px solid #ff6b35; border-radius: 6px; padding: 16px; font-size: 14px; line-height: 1.6; color: #374151; white-space: pre-wrap; font-weight: 400;">
                                ${message}
                              </div>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- What Happens Next -->
                    <tr>
                      <td style="padding: 24px 32px 0 32px;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">

                          <!-- Section Header -->
                          <tr>
                            <td style="background-color: #1e3a8a; padding: 16px 20px; border-radius: 8px 8px 0 0; border-bottom: 3px solid #ff6b35;">
                              <h3 style="margin: 0; font-size: 16px; font-weight: 700; color: #ffffff;">🚀 What Happens Next</h3>
                            </td>
                          </tr>

                          <!-- Steps -->
                          <tr>
                            <td style="padding: 20px;">

                              <!-- Step 1 -->
                              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin-bottom: 16px;">
                                <tr>
                                  <td style="background-color: #ffffff; padding: 16px; border-radius: 6px; border: 1px solid #e2e8f0;">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                      <tr>
                                        <td width="40" style="vertical-align: top; padding-right: 12px;">
                                          <div style="background-color: #ff6b35; color: #ffffff; width: 32px; height: 32px; border-radius: 50%; text-align: center; line-height: 32px; font-size: 14px; font-weight: 700;">1</div>
                                        </td>
                                        <td style="vertical-align: top;">
                                          <h4 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 700; color: #1e3a8a;">Message Review</h4>
                                          <p style="margin: 0; font-size: 13px; color: #64748b; line-height: 1.4;">We'll carefully review your message and prepare a detailed response</p>
                                        </td>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>

                              <!-- Step 2 -->
                              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin-bottom: 16px;">
                                <tr>
                                  <td style="background-color: #ffffff; padding: 16px; border-radius: 6px; border: 1px solid #e2e8f0;">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                      <tr>
                                        <td width="40" style="vertical-align: top; padding-right: 12px;">
                                          <div style="background-color: #1e3a8a; color: #ffffff; width: 32px; height: 32px; border-radius: 50%; text-align: center; line-height: 32px; font-size: 14px; font-weight: 700;">2</div>
                                        </td>
                                        <td style="vertical-align: top;">
                                          <h4 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 700; color: #1e3a8a;">Personal Response</h4>
                                          <p style="margin: 0; font-size: 13px; color: #64748b; line-height: 1.4;">One of our team members will respond personally within 2 hours</p>
                                        </td>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>

                              <!-- Step 3 -->
                              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                  <td style="background-color: #ffffff; padding: 16px; border-radius: 6px; border: 1px solid #e2e8f0;">
                                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                      <tr>
                                        <td width="40" style="vertical-align: top; padding-right: 12px;">
                                          <div style="background-color: #ff6b35; color: #ffffff; width: 32px; height: 32px; border-radius: 50%; text-align: center; line-height: 32px; font-size: 14px; font-weight: 700;">3</div>
                                        </td>
                                        <td style="vertical-align: top;">
                                          <h4 style="margin: 0 0 4px 0; font-size: 14px; font-weight: 700; color: #1e3a8a;">Detailed Solutions</h4>
                                          <p style="margin: 0; font-size: 13px; color: #64748b; line-height: 1.4;">We'll provide comprehensive answers and next steps</p>
                                        </td>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>

                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- Contact CTA -->
                    <tr>
                      <td style="padding: 24px 32px 32px 32px;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">

                          <!-- CTA Header -->
                          <tr>
                            <td style="background-color: #ff6b35; padding: 16px 20px; border-radius: 8px 8px 0 0; border-bottom: 3px solid #1e3a8a;">
                              <h3 style="margin: 0; font-size: 16px; font-weight: 700; color: #ffffff;">📞 Need Immediate Assistance?</h3>
                            </td>
                          </tr>

                          <!-- CTA Content -->
                          <tr>
                            <td style="padding: 20px; text-align: center;">
                              <p style="margin: 0 0 16px 0; font-size: 14px; color: #475569; font-weight: 500;">Can't wait for our response? Contact us directly:</p>

                              <!-- Buttons -->
                              <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                                <tr>
                                  <td style="padding-right: 8px;">
                                    <a href="mailto:<EMAIL>" style="display: inline-block; background-color: #1e3a8a; color: #ffffff; padding: 12px 16px; border-radius: 6px; text-decoration: none; font-weight: 600; font-size: 13px; text-transform: uppercase; letter-spacing: 0.025em;">📧 Email Us</a>
                                  </td>
                                  <td style="padding-left: 8px;">
                                    <a href="tel:+442079460958" style="display: inline-block; background-color: #ff6b35; color: #ffffff; padding: 12px 16px; border-radius: 6px; text-decoration: none; font-weight: 600; font-size: 13px; text-transform: uppercase; letter-spacing: 0.025em;">📞 Call Now</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                      <td style="background-color: #1e3a8a; padding: 32px; text-align: center; border-radius: 0 0 12px 12px; border-top: 4px solid #ff6b35;">

                        <!-- Company Info -->
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto 16px auto; background-color: #ffffff; border-radius: 8px; border: 1px solid #e2e8f0;">
                          <tr>
                            <td style="padding: 16px 20px; text-align: center;">
                              <h4 style="margin: 0 0 4px 0; font-size: 16px; font-weight: 700; color: #1e3a8a;">WebforLeads</h4>
                              <p style="margin: 0 0 12px 0; font-size: 12px; font-weight: 600; color: #ff6b35; text-transform: uppercase; letter-spacing: 0.05em;">Premium Digital Solutions</p>

                              <!-- Contact Links -->
                              <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                                <tr>
                                  <td style="padding-right: 12px;">
                                    <a href="mailto:<EMAIL>" style="color: #ff6b35; text-decoration: none; font-size: 12px; font-weight: 500;">📧 <EMAIL></a>
                                  </td>
                                  <td style="padding-left: 12px;">
                                    <a href="tel:+442079460958" style="color: #ff6b35; text-decoration: none; font-size: 12px; font-weight: 500;">📞 +44 20 7946 0958</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>

                        <!-- Footer Text -->
                        <p style="margin: 0; color: #94a3b8; font-size: 11px; font-weight: 400;">Thank you for contacting WebforLeads. We value your inquiry!</p>
                      </td>
                    </tr>

                  </table>

                </td>
              </tr>
            </table>

          </body>
          </html>
        `,
      });
    } catch (confirmationError) {
      console.error('Failed to send confirmation email:', confirmationError);
      // Don't fail the main request if confirmation email fails
    }

    return NextResponse.json(
      { 
        message: 'Contact message sent successfully',
        id: data?.id 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
