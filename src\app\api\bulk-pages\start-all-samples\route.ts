import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Get the bulk pages storage directory
function getBulkPagesStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'bulk-pages-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// Get all bulk generation jobs
function getAllBulkJobs() {
  const storageDir = getBulkPagesStorageDir();
  
  try {
    const files = fs.readdirSync(storageDir);
    const jobs = [];

    for (const file of files) {
      if (file.startsWith('job-') && file.endsWith('.json')) {
        const filepath = path.join(storageDir, file);
        const content = fs.readFileSync(filepath, 'utf-8');
        const job = JSON.parse(content);
        jobs.push(job);
      }
    }

    return jobs;
  } catch (error) {
    console.error('Error reading bulk jobs:', error);
    return [];
  }
}

export async function POST() {
  try {
    console.log('\n🚀 STARTING ALL SAMPLE JOBS');
    console.log('⏰ Timestamp:', new Date().toISOString());

    // Get all jobs
    const allJobs = getAllBulkJobs();
    
    // Filter sample jobs (jobs with only 1 page)
    const sampleJobs = allJobs.filter(job => 
      job.totalPages === 1 && 
      job.status === 'pending' &&
      job.name.includes('Sample')
    );

    console.log(`📊 Found ${sampleJobs.length} sample jobs to start`);

    const results = [];

    for (const job of sampleJobs) {
      console.log(`\n🔄 Starting job: ${job.name}`);
      
      try {
        // Start the job generation
        const response = await fetch('http://localhost:3002/api/bulk-pages/start-generation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jobId: job.id
          }),
        });

        const data = await response.json();
        
        if (data.success) {
          console.log(`✅ Started job: ${job.name}`);
          results.push({
            jobId: job.id,
            jobName: job.name,
            templateName: job.templateName,
            status: 'started',
            expectedSlug: job.pages[0]?.slug
          });
        } else {
          console.log(`❌ Failed to start job: ${job.name} - ${data.error}`);
          results.push({
            jobId: job.id,
            jobName: job.name,
            templateName: job.templateName,
            status: 'failed',
            error: data.error
          });
        }

        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`❌ Error starting job ${job.name}:`, error);
        results.push({
          jobId: job.id,
          jobName: job.name,
          templateName: job.templateName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log('\n🎉 SAMPLE JOB GENERATION INITIATED');
    console.log(`📊 Total jobs started: ${results.filter(r => r.status === 'started').length}`);
    console.log(`❌ Failed jobs: ${results.filter(r => r.status === 'failed' || r.status === 'error').length}`);

    return NextResponse.json({
      success: true,
      message: `Initiated generation for ${results.filter(r => r.status === 'started').length} sample jobs`,
      results,
      expectedPages: results
        .filter(r => r.status === 'started' && r.expectedSlug)
        .map(r => ({
          templateName: r.templateName,
          url: `/area-pages/${r.expectedSlug}`,
          slug: r.expectedSlug
        })),
      note: 'Pages will be generated in the background. Check /admin/bulk-pages for progress.'
    });

  } catch (error) {
    console.error('💥 START ALL SAMPLES ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to start sample jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get all jobs
    const allJobs = getAllBulkJobs();
    
    // Filter sample jobs
    const sampleJobs = allJobs.filter(job => 
      job.totalPages === 1 && 
      job.name.includes('Sample')
    );

    return NextResponse.json({
      message: 'Start all sample jobs API endpoint. Use POST to start generation for all sample jobs.',
      sampleJobs: sampleJobs.map(job => ({
        id: job.id,
        name: job.name,
        templateName: job.templateName,
        status: job.status,
        expectedSlug: job.pages[0]?.slug
      })),
      endpoints: {
        'POST /api/bulk-pages/start-all-samples': 'Start generation for all sample jobs'
      }
    });
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get sample jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
