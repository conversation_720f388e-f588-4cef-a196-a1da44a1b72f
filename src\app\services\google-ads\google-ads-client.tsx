"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  TrendingUp, ArrowRight, CheckCircle, Users, Award, Star,
  Target, Zap, BarChart3, Globe, Rocket, Shield, MousePointer,
  Monitor, Database, Cloud, Settings, Lock, ChevronRight, Sparkles,
  Trophy, LineChart, Megaphone, Heart, Eye, MessageSquare, Share2,
  Mail, Calendar, Play, Download, ExternalLink, Lightbulb, PoundSterling, Search
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 }
};

// Premium stats for billion-dollar feel
const stats = [
  { number: "847%", label: "Average ROI Increase", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "£2.4M+", label: "Revenue Generated", icon: <PoundSterling className="h-6 w-6" /> },
  { number: "94%", label: "Client Retention Rate", icon: <Users className="h-6 w-6" /> },
  { number: "48hrs", label: "Campaign Launch Time", icon: <Rocket className="h-6 w-6" /> }
];

// Premium Google Ads features
const googleAdsFeatures = [
  {
    icon: <Target className="h-8 w-8" />,
    title: "Strategic Campaign Management",
    description: "Expert Google Ads campaign setup, optimization, and management that maximizes ROI through advanced targeting and continuous performance monitoring.",
    highlight: "ROI-Focused"
  },
  {
    icon: <Search className="h-8 w-8" />,
    title: "Advanced Keyword Research",
    description: "Comprehensive keyword research and analysis to identify high-converting search terms that drive qualified traffic and reduce costs.",
    highlight: "Data-Driven"
  },
  {
    icon: <BarChart3 className="h-8 w-8" />,
    title: "Performance Optimization",
    description: "Continuous campaign optimization including bid management, ad testing, and landing page optimization to maximize conversions and reduce CPA.",
    highlight: "Performance-Driven"
  },
  {
    icon: <Eye className="h-8 w-8" />,
    title: "Transparent Reporting",
    description: "Detailed performance reports and analytics that provide clear insights into campaign performance, ROI, and opportunities for growth.",
    highlight: "Transparent"
  }
];

export default function GoogleAdsClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-white/90 text-[#FF6B35] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <MousePointer className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">GOOGLE ADS MANAGEMENT SERVICES</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Google Ads Management That
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Maximizes ROI & Drives Conversions
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Leading Google Ads Agency Specializing in{" "}
                    <span className="text-[#FF6B35] relative">
                      PPC Campaign Management & Performance Optimization
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Expert Google Ads management services that maximize ROI, reduce costs, and drive qualified traffic. Our certified PPC specialists create and optimize campaigns that deliver measurable results and sustainable growth.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Target className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      ROI Focused
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <BarChart3 className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Data-Driven
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <CheckCircle className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Certified Experts
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>

          {/* Premium Stats */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={scaleIn}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-[#1E3A8A] mb-2">{stat.number}</div>
                <div className="text-lg text-slate-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Google Ads Features Section */}
      <section className="py-32 mt-16 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/5 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-[#1E3A8A]/5 to-transparent rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <div className="inline-flex items-center space-x-2 px-4 py-2 bg-[#FF6B35]/10 border border-[#FF6B35]/20 rounded-full">
                <Sparkles className="h-4 w-4 text-[#FF6B35]" />
                <span className="text-[#FF6B35] font-semibold">Premium Google Ads Management</span>
              </div>

              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Google Ads That
                <span className="block text-[#FF6B35]">Maximize ROI & Drive Conversions</span>
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our expert Google Ads management combines strategic campaign setup with continuous optimization to deliver maximum ROI and sustainable business growth.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {googleAdsFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <CardContent className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white flex-shrink-0">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#1E3A8A]/10 text-[#1E3A8A] text-xs font-semibold rounded-full mb-2">
                          {feature.highlight}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                      </div>
                    </div>

                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </CardContent>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>



      {/* Premium Pricing Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Target className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">GOOGLE ADS PACKAGES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Choose Your
                <span className="block text-[#FF6B35]">Google Ads Package</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From startup campaigns to enterprise advertising, we have the perfect Google Ads package to maximize your ROI.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16"
          >
            {[
              {
                name: "Starter Campaign",
                price: "£1,500",
                period: "/month",
                description: "Perfect for businesses new to Google Ads",
                features: [
                  "Campaign setup & optimization",
                  "Keyword research & selection",
                  "Ad copy creation (5 variants)",
                  "Landing page optimization",
                  "Monthly performance reports",
                  "Basic conversion tracking"
                ],
                highlight: false,
                cta: "Start Campaign"
              },
              {
                name: "Growth Accelerator",
                price: "£3,000",
                period: "/month",
                description: "For businesses ready to scale their ads",
                features: [
                  "Everything in Starter Campaign",
                  "Multiple campaign types",
                  "Advanced audience targeting",
                  "A/B testing & optimization",
                  "Remarketing campaigns",
                  "Weekly optimization calls"
                ],
                highlight: true,
                cta: "Accelerate Growth"
              },
              {
                name: "Enterprise Dominator",
                price: "Custom",
                period: "",
                description: "Comprehensive solutions for large advertisers",
                features: [
                  "Everything in Growth Accelerator",
                  "Multi-account management",
                  "Advanced attribution modeling",
                  "Dedicated account manager",
                  "Custom integrations",
                  "Priority support"
                ],
                highlight: false,
                cta: "Dominate Market"
              }
            ].map((pkg, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
                className={`relative ${pkg.highlight ? 'lg:-mt-8' : ''}`}
              >
                <Card className={`h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group ${pkg.highlight ? 'ring-2 ring-[#FF6B35] ring-opacity-50' : ''}`}>
                  {pkg.highlight && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] text-white px-4 py-1 rounded-full shadow-lg">
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold text-[#1E3A8A] mb-2">{pkg.name}</h3>
                      <div className="mb-4">
                        <span className="text-4xl font-black text-[#FF6B35]">{pkg.price}</span>
                        <span className="text-slate-500">{pkg.period}</span>
                      </div>
                      <p className="text-slate-600">{pkg.description}</p>
                    </div>

                    <ul className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-[#FF6B35] flex-shrink-0" />
                          <span className="text-slate-700">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <Button 
                      onClick={() => {
                        const appointmentSection = document.getElementById('appointment-form');
                        if (appointmentSection) {
                          appointmentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                      }}
                      className={`w-full py-3 rounded-2xl font-bold transition-all duration-300 ${
                        pkg.highlight 
                          ? 'bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF8A65] hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl' 
                          : 'bg-[#1E3A8A] hover:bg-[#1E40AF] text-white'
                      }`}
                    >
                      {pkg.cta}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Process Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Zap className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">GOOGLE ADS PROCESS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Our Proven
                <span className="block text-[#FF6B35]">Google Ads Process</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From campaign setup to optimization, our systematic approach ensures maximum ROI and qualified leads.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                step: "01",
                title: "Research & Strategy",
                description: "We conduct thorough keyword research and competitor analysis to develop a winning Google Ads strategy.",
                icon: <Target className="h-8 w-8" />
              },
              {
                step: "02", 
                title: "Campaign Setup",
                description: "Our experts set up optimized campaigns with proper structure, targeting, and conversion tracking.",
                icon: <Settings className="h-8 w-8" />
              },
              {
                step: "03",
                title: "Launch & Monitor",
                description: "We launch campaigns and monitor performance daily, making real-time adjustments for optimal results.",
                icon: <TrendingUp className="h-8 w-8" />
              },
              {
                step: "04",
                title: "Optimize & Scale",
                description: "Continuous optimization and scaling of successful campaigns to maximize ROI and business growth.",
                icon: <BarChart3 className="h-8 w-8" />
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] flex items-center justify-center text-white mx-auto mb-4">
                        {process.icon}
                      </div>
                      <div className="text-6xl font-black text-[#1E3A8A]/10 mb-4">{process.step}</div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-[#1E3A8A] mb-4 group-hover:text-[#FF6B35] transition-colors">
                      {process.title}
                    </h3>
                    
                    <p className="text-slate-600 leading-relaxed">
                      {process.description}
                    </p>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Testimonials Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Google Ads That
                <span className="block text-[#FF6B35]">Drive Qualified Leads</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                See how our Google Ads expertise has transformed businesses with high-converting campaigns and exceptional ROI.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {[
              {
                quote: "Our Google Ads ROAS improved from 2.1x to 8.7x in just 4 months. Lead quality is exceptional and cost per lead dropped by 65%. Game-changing results!",
                name: "Robert Martinez",
                role: "Marketing Manager",
                company: "LegalTech Pro",
                industry: "Legal Services",
                metric: "8.7x",
                metricLabel: "ROAS Achieved",
                avatar: "RM"
              },
              {
                quote: "Generated £2.3M in revenue from Google Ads with a 6.2x return. Their keyword research and ad optimization strategies are absolutely world-class.",
                name: "Sophie Williams",
                role: "CEO",
                company: "HomeDesign Plus",
                industry: "Home Services",
                metric: "£2.3M",
                metricLabel: "Revenue Generated",
                avatar: "SW"
              },
              {
                quote: "Conversion rate increased from 1.8% to 12.4% while reducing cost per click by 40%. The landing page optimization was incredible.",
                name: "Alex Thompson",
                role: "Growth Director",
                company: "FitnessTech",
                industry: "Health & Fitness",
                metric: "12.4%",
                metricLabel: "Conversion Rate",
                avatar: "AT"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-1 text-[#FF6B35] mb-4">
                      {[...Array(5)].map((_, s) => <Star key={s} className="h-4 w-4 fill-current" />)}
                    </div>
                    
                    <blockquote className="text-slate-700 leading-relaxed mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] flex items-center justify-center text-white font-bold text-sm">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="text-sm font-bold text-[#1E3A8A]">{testimonial.name}</div>
                          <div className="text-xs text-slate-500">{testimonial.role}</div>
                          <div className="text-xs text-[#FF6B35] font-semibold">{testimonial.company}</div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-black text-[#FF6B35]">{testimonial.metric}</div>
                        <div className="text-xs text-slate-500 font-semibold">{testimonial.metricLabel}</div>
                      </div>
                    </div>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium FAQs Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Target className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Google Ads
                <span className="block text-[#FF6B35]">Expert Insights</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Get answers to the most common questions about Google Ads management, optimization strategies, and maximizing your advertising ROI.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {[
              {
                category: "Campaign Strategy",
                icon: <Target className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you structure Google Ads campaigns for maximum performance?",
                    a: "We create tightly themed ad groups with relevant keywords, compelling ad copy, and optimized landing pages. Our structure includes separate campaigns for different products/services, match types, and audience segments for precise control and optimization."
                  },
                  {
                    q: "What's your approach to keyword research and selection?",
                    a: "We conduct comprehensive keyword research using Google Keyword Planner, competitor analysis, and search intent mapping. We focus on high-intent keywords with optimal search volume and manageable competition for your budget."
                  }
                ]
              },
              {
                category: "Budget & Bidding",
                icon: <TrendingUp className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you optimize bidding strategies for better ROI?",
                    a: "We use a combination of automated and manual bidding strategies based on campaign goals. This includes Target CPA for lead generation, Target ROAS for e-commerce, and Smart Bidding with conversion tracking for optimal performance."
                  },
                  {
                    q: "What's the minimum budget needed for effective Google Ads?",
                    a: "While budgets vary by industry, we typically recommend a minimum of £1,000-£2,000 per month for meaningful data collection and optimization. This allows for proper testing and scaling of successful campaigns."
                  }
                ]
              },
              {
                category: "Performance & Tracking",
                icon: <BarChart3 className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you track and measure Google Ads performance?",
                    a: "We implement comprehensive conversion tracking, Google Analytics integration, and custom reporting dashboards. We track metrics like ROAS, CPA, Quality Score, impression share, and attribution across the entire customer journey."
                  },
                  {
                    q: "How quickly can we expect to see results from Google Ads?",
                    a: "Initial results can be seen within 24-48 hours of campaign launch. However, meaningful optimization and peak performance typically occur after 2-4 weeks of data collection and continuous refinement."
                  }
                ]
              },
              {
                category: "Management & Support",
                icon: <Users className="h-6 w-6" />,
                questions: [
                  {
                    q: "What's included in your Google Ads management service?",
                    a: "Our service includes campaign setup, keyword research, ad creation, landing page optimization, bid management, A/B testing, conversion tracking, monthly reporting, and ongoing optimization based on performance data."
                  },
                  {
                    q: "How often do you optimize and monitor campaigns?",
                    a: "We monitor campaigns daily for performance issues and make optimizations weekly. Major strategic reviews happen monthly, with detailed reporting and recommendations for scaling successful campaigns."
                  }
                ]
              }
            ].map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={fadeInUp}
                className="space-y-6"
              >
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF6B35]/80 flex items-center justify-center text-white">
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">{category.category}</h3>
                </div>

                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <motion.div
                      key={faqIndex}
                      variants={fadeInUp}
                      whileHover={{ y: -2 }}
                      transition={{ type: "spring", stiffness: 300, damping: 25 }}
                    >
                      <Card className="border-0 rounded-2xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                        <CardContent className="p-8">
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 rounded-full bg-[#FF6B35]/10 flex items-center justify-center flex-shrink-0 mt-1">
                              <div className="w-2 h-2 rounded-full bg-[#FF6B35]"></div>
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                                {faq.q}
                              </h4>
                              <p className="text-slate-600 leading-relaxed">
                                {faq.a}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                        
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="appointment-form" className="py-24 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <Badge className="px-4 py-1 bg-white/10 text-white border border-white/20 rounded-full">Get Started</Badge>
              <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">Ready to Dominate Google Ads?</h2>
              <p className="text-slate-300 text-lg max-w-xl">Let's create high-converting Google Ads campaigns that deliver qualified leads and maximize your advertising ROI.</p>
            </div>
            <div>
              <AppointmentForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
