import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, slug, jobId } = body;

    console.log('🗺️ Sitemap revalidation triggered:', { type, slug, jobId });

    // Revalidate the sitemap
    revalidatePath('/sitemap.xml');
    
    // Also revalidate any related paths
    if (type === 'blog' && slug) {
      revalidatePath('/blog');
      revalidatePath(`/blog/${slug}`);
      console.log(`✅ Revalidated blog paths: /blog, /blog/${slug}`);
    }
    
    if (type === 'bulk-pages') {
      // Revalidate service pages
      revalidatePath('/services');
      console.log('✅ Revalidated service pages');
    }

    console.log('✅ Sitemap revalidation completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Sitemap revalidated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Error revalidating sitemap:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to revalidate sitemap',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Sitemap revalidation API endpoint. Use POST to trigger revalidation.',
    endpoints: {
      'POST /api/revalidate-sitemap': 'Trigger sitemap revalidation after content changes'
    }
  });
}
