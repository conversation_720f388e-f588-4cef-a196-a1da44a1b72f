"use client";

import React from "react";
import { motion } from "framer-motion";
import { Users, TrendingUp, <PERSON><PERSON><PERSON>, Award, Target } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import AppointmentForm from "@/components/forms/appointment-form";
import Image from "next/image";

export default function Hero() {
  // Clean, minimal animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  return (
    <section className="relative bg-white overflow-hidden min-h-screen">

      {/* Navigation spacing */}
      <div className="h-20 lg:h-24"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
        <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

          {/* Left Column - Premium Content Layout */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="lg:col-span-7 space-y-10"
          >
            {/* Premium Headline Section */}
            <motion.div variants={fadeInUp} className="space-y-10">
              <div className="space-y-8">
                {/* Premium badge */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                >
                  <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                    <Sparkles className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                    <span className="font-bold text-sm tracking-wide">PREMIUM DIGITAL SOLUTIONS</span>
                  </Badge>
                </motion.div>

                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black tracking-tight leading-[1.1] mb-8">
                  <motion.span
                    className="block text-[#1E3A8A] mb-2"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.8 }}
                  >
                    Professional Web Design & Development Company
                  </motion.span>
                  <motion.span
                    className="block relative"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.8 }}
                  >
                    <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                      Custom Websites, SEO & Digital Marketing
                    </span>
                    {/* Enhanced premium underline accent */}
                    <motion.div
                      className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                    />
                  </motion.span>
                </h1>
              </div>

              <motion.div
                className="space-y-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.8 }}
              >
                <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                  Leading Web Development Company Specializing in{" "}
                  <span className="text-[#FF6B35] relative">
                    Custom Websites, Mobile Apps & Software Development
                    <motion.div
                      className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ delay: 1.5, duration: 0.8 }}
                    />
                  </span>
                </h2>

                <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                  Transform your business with our comprehensive digital solutions: responsive web design, mobile app development, custom software development, SEO optimization, social media marketing, e-commerce development, and complete digital marketing services. We create high-performing websites and applications that drive results.
                </p>
              </motion.div>
            </motion.div>

            {/* Compact Benefits List */}
            <motion.div variants={fadeInUp} className="space-y-3">
              <motion.div
                className="flex items-center space-x-3 group cursor-pointer"
                whileHover={{ x: 4 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <motion.div
                  className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <Target className="h-3.5 w-3.5 text-white" />
                </motion.div>
                <div>
                  <span className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200">Custom Web Design & Development</span>
                  <span className="text-gray-600 ml-2">— Responsive websites that convert visitors into customers.</span>
                </div>
              </motion.div>

              <motion.div
                className="flex items-center space-x-3 group cursor-pointer"
                whileHover={{ x: 4 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <motion.div
                  className="flex-shrink-0 w-6 h-6 bg-blue-900 rounded-lg flex items-center justify-center"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <Award className="h-3.5 w-3.5 text-white" />
                </motion.div>
                <div>
                  <span className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">SEO & Digital Marketing Services</span>
                  <span className="text-gray-600 ml-2">— Dominate search results and social media platforms.</span>
                </div>
              </motion.div>

              <motion.div
                className="flex items-center space-x-3 group cursor-pointer"
                whileHover={{ x: 4 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <motion.div
                  className="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-lg flex items-center justify-center"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <TrendingUp className="h-3.5 w-3.5 text-white" />
                </motion.div>
                <div>
                  <span className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors duration-200">Mobile Apps & Software Development</span>
                  <span className="text-gray-600 ml-2">— Custom applications that scale with your business growth.</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Premium Trust Indicators */}
            <motion.div
              variants={fadeInUp}
              className="pt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.8 }}
            >
              <div className="space-y-6">
                {/* Text Indicator */}
                <motion.div
                  className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/70 backdrop-blur-sm border border-[#4CAF50]/20 hover:shadow-lg transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <motion.div
                    className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                    whileHover={{ rotate: 10 }}
                  >
                    <Users className="h-4 w-4 text-white" />
                  </motion.div>
                  <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#4CAF50] transition-colors duration-200">
                    Trusted by 500+ UK Businesses
                  </span>
                </motion.div>

                {/* Partner Badges Section */}
                <div className="flex flex-wrap items-center gap-6">
                  <div className="text-sm text-slate-600 font-medium">Certified Partners:</div>

                  {/* Google Partner Badge */}
                  <motion.div
                    className="group cursor-pointer"
                    whileHover={{ scale: 1.1, y: -3 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="bg-white/90 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-blue-300/50">
                      <Image
                        src="/images/google.png"
                        alt="Google Partner Certified"
                        width={120}
                        height={60}
                        className="h-12 w-auto object-contain filter hover:brightness-110 transition-all duration-300"
                      />
                    </div>
                  </motion.div>

                  {/* Clutch Badge */}
                  <motion.div
                    className="group cursor-pointer"
                    whileHover={{ scale: 1.1, y: -3 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="bg-white/90 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-orange-300/50">
                      <Image
                        src="/images/clutch-badge.png"
                        alt="Clutch 5-Star Rating"
                        width={120}
                        height={60}
                        className="h-12 w-auto object-contain filter hover:brightness-110 transition-all duration-300"
                      />
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Premium Form */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="lg:col-span-5"
          >
            <AppointmentForm />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
