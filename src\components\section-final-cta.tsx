"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Calendar, CheckCircle, Loader2, Star, Award, ArrowRight, Zap, TrendingUp, Users, Shield, Sparkles, Target, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";


export default function SectionFinalCTA() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const handleCTAClick = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    setIsSuccess(true);
    setTimeout(() => {
      setIsSuccess(false);
      window.location.href = "/book-call";
    }, 2000);
  };

  const trustIndicators = [
    { icon: <Users className="h-5 w-5" />, text: "500+ Businesses Scaled" },
    { icon: <Award className="h-5 w-5" />, text: "Google Partner Certified" },
    { icon: <Shield className="h-5 w-5" />, text: "100% Satisfaction Guarantee" }
  ];

  return (
    <section className="relative py-24 lg:py-32 overflow-hidden bg-white">

      <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center space-y-12"
        >
          {/* Premium Badge */}
          <motion.div variants={fadeInUp}>
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm"
            >
              <Zap className="h-6 w-6 animate-pulse text-[#FF6B35]" />
              <span className="tracking-wide">LIMITED TIME OFFER</span>
            </Badge>
          </motion.div>

          {/* Premium Headline */}
          <motion.div variants={fadeInUp} className="space-y-8">
            <h2 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9]">
              <span className="block text-[#1E3A8A] mb-3">Stop Competing.</span>
              <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                Start Dominating.
              </span>
            </h2>

            <p className="text-2xl lg:text-3xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-medium">
              The digital landscape is crowded. A generic website and a sprinkle of SEO won't cut it anymore. Partner with an agency that's as serious about your growth as you are. Let's have a 15-minute, no-pressure chat to see if we're the right fit. We'll give you actionable advice you can use, whether you hire us or not.
            </p>
          </motion.div>

          {/* Premium Value Props */}
          <motion.div variants={fadeInUp} className="grid sm:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 transition-all duration-500 rounded-3xl overflow-hidden hover:scale-[1.02] hover:border-[#FF6B35]/30 group">
              <CardContent className="p-8 text-center">
                <motion.div
                  className="w-20 h-20 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl shadow-[#FF6B35]/25 group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  <TrendingUp className="h-10 w-10 text-white" />
                </motion.div>
                <h3 className="text-2xl font-black text-[#1E3A8A] mb-3">Free Website Audit</h3>
                <p className="text-slate-600 text-lg font-medium mb-4">Comprehensive analysis worth £500</p>
                <Badge className="bg-gradient-to-r from-[#FF6B35]/15 to-[#1E3A8A]/15 text-[#FF6B35] border border-[#FF6B35]/30 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 rounded-full backdrop-blur-sm">
                  <Target className="h-4 w-4 mr-2 fill-[#FF6B35] text-[#FF6B35]" />
                  £500 Value
                </Badge>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#4CAF50]/20 transition-all duration-500 rounded-3xl overflow-hidden hover:scale-[1.02] hover:border-[#4CAF50]/30 group">
              <CardContent className="p-8 text-center">
                <motion.div
                  className="w-20 h-20 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl shadow-[#4CAF50]/25 group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  <Calendar className="h-10 w-10 text-white" />
                </motion.div>
                <h3 className="text-2xl font-black text-[#1E3A8A] mb-3">Strategy Session</h3>
                <p className="text-slate-600 text-lg font-medium mb-4">60-minute consultation with experts</p>
                <Badge className="bg-gradient-to-r from-[#4CAF50]/15 to-[#1E3A8A]/15 text-[#4CAF50] border border-[#4CAF50]/30 hover:shadow-lg hover:shadow-[#4CAF50]/25 transition-all duration-500 rounded-full backdrop-blur-sm">
                  <Clock className="h-4 w-4 mr-2 fill-[#4CAF50] text-[#4CAF50]" />
                  60 Minutes
                </Badge>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#1E3A8A]/20 transition-all duration-500 rounded-3xl overflow-hidden hover:scale-[1.02] hover:border-[#1E3A8A]/30 group">
              <CardContent className="p-8 text-center">
                <motion.div
                  className="w-20 h-20 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl shadow-[#1E3A8A]/25 group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  <CheckCircle className="h-10 w-10 text-white" />
                </motion.div>
                <h3 className="text-2xl font-black text-[#1E3A8A] mb-3">Action Plan</h3>
                <p className="text-slate-600 text-lg font-medium mb-4">Clear roadmap to digital success</p>
                <Badge className="bg-gradient-to-r from-[#1E3A8A]/15 to-[#FF6B35]/15 text-[#1E3A8A] border border-[#1E3A8A]/30 hover:shadow-lg hover:shadow-[#1E3A8A]/25 transition-all duration-500 rounded-full backdrop-blur-sm">
                  <Sparkles className="h-4 w-4 mr-2 fill-[#1E3A8A] text-[#1E3A8A]" />
                  Custom Plan
                </Badge>
              </CardContent>
            </Card>
          </motion.div>

          {/* Premium CTA Button */}
          <motion.div variants={fadeInUp} className="space-y-8">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={handleCTAClick}
                disabled={isLoading || isSuccess}
                size="lg"
                className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white px-16 py-8 text-2xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/40 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 border-2 border-[#FF6B35]/20"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-8 w-8 mr-4 animate-spin" />
                    Securing Your Spot...
                  </>
                ) : isSuccess ? (
                  <>
                    <CheckCircle className="h-8 w-8 mr-4 text-white" />
                    Success! Redirecting...
                  </>
                ) : (
                  <>
                    <Calendar className="h-8 w-8 mr-4" />
                    Book My Free 15-Minute Strategy Call
                    <ArrowRight className="h-8 w-8 ml-4" />
                  </>
                )}
              </Button>
            </motion.div>

            <p className="text-slate-600 text-xl font-bold">
              🔥 <strong className="text-[#FF6B35]">Only 5 spots available this month</strong> - No obligation, 100% free
            </p>
          </motion.div>

          {/* Premium Trust Indicators */}
          <motion.div variants={fadeInUp} className="pt-12">
            <div className="border-t border-slate-200/50 pt-8 mb-8"></div>

            <div className="flex flex-wrap items-center justify-center gap-12 text-[#1E3A8A]">
              {trustIndicators.map((indicator, index) => (
                <motion.div
                  key={index}
                  className="flex items-center space-x-4"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center shadow-2xl shadow-[#1E3A8A]/25">
                    {indicator.icon}
                  </div>
                  <span className="font-black text-lg">{indicator.text}</span>
                </motion.div>
              ))}
            </div>

            <div className="flex items-center justify-center space-x-4 mt-12">
              <div className="flex space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-6 w-6 fill-[#FF6B35] text-[#FF6B35]" />
                ))}
              </div>
              <span className="text-[#1E3A8A] font-black text-xl">5.0 Rating • 200+ Reviews</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
