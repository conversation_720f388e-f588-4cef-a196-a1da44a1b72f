import { Metadata } from 'next';
import AppDevelopmentClient from './app-development-client';

export const metadata: Metadata = {
  title: 'Mobile App Development Services | iOS & Android | Cross-Platform',
  description:
    'End-to-end mobile app development for iOS and Android. MVPs, cross-platform apps, backend APIs, and enterprise-grade solutions with premium UI/UX.',
  keywords:
    ['mobile app development','iOS development','Android development','cross-platform apps','React Native','Flutter','app UI/UX','backend API development','MVP development','push notifications','app maintenance'],
  authors: [{ name: 'WebforLeads' }],
  creator: 'WebforLeads',
  publisher: 'WebforLeads',
  robots: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://webforleads.uk/services/app-development',
    title: 'Mobile App Development Services | iOS & Android | Cross-Platform',
    description:
      'End-to-end mobile app development for iOS and Android. MVPs, cross-platform apps, backend APIs, and enterprise-grade solutions with premium UI/UX.',
    siteName: 'WebforLeads',
    images: [
      {
        url: 'https://webforleads.uk/services/app-development-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Mobile App Development Services - WebforLeads',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Mobile App Development Services | iOS & Android',
    description:
      'Build performant mobile apps with premium UI/UX, robust backends, and measurable outcomes.',
    creator: '@webforleads',
    images: ['https://webforleads.uk/services/app-development-twitter.jpg'],
  },
  alternates: {
    canonical: 'https://webforleads.uk/services/app-development',
  },
};

export default function AppDevelopmentPage() {
  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Service',
            name: 'Mobile App Development Services',
            description:
              'Professional mobile app development for iOS and Android including MVP builds, cross-platform development, backend APIs, and app store deployment.',
            provider: {
              '@type': 'Organization',
              name: 'WebforLeads',
              url: 'https://webforleads.uk',
            },
            areaServed: {
              '@type': 'Country',
              name: 'United States',
            },
            hasOfferCatalog: {
              '@type': 'OfferCatalog',
              name: 'App Development Packages',
              itemListElement: [
                { '@type': 'Offer', itemOffered: { '@type': 'Service', name: 'MVP Launch' }, priceRange: '£8500+' },
                { '@type': 'Offer', itemOffered: { '@type': 'Service', name: 'Cross-Platform Pro' }, priceRange: '£15500+' },
                { '@type': 'Offer', itemOffered: { '@type': 'Service', name: 'Enterprise Scale' }, priceRange: 'Custom' },
              ],
            },
            aggregateRating: { '@type': 'AggregateRating', ratingValue: '4.9', reviewCount: '127' },
          }),
        }}
      />
      <AppDevelopmentClient />
    </>
  );
}
