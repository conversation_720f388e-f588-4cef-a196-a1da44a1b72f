import { NextRequest, NextResponse } from 'next/server';
import { updateBulkJob } from '@/lib/bulk-pages-storage';

export async function POST(request: NextRequest) {
  try {
    console.log('\n⏸️ PAUSING BULK GENERATION JOB');
    
    const { jobId } = await request.json();
    
    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    console.log('🆔 Job ID:', jobId);

    // Update job status to paused
    const job = updateBulkJob(jobId, { 
      status: 'paused'
    });

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('✅ Job paused successfully');
    console.log('📊 Job Status:', job.status);
    console.log('📈 Progress:', job.progress + '%');
    console.log('✅ Completed Pages:', job.completedPages + '/' + job.totalPages);

    return NextResponse.json({
      success: true,
      message: 'Job paused successfully',
      job: {
        id: job.id,
        name: job.name,
        status: job.status,
        progress: job.progress,
        completedPages: job.completedPages,
        totalPages: job.totalPages
      }
    });

  } catch (error) {
    console.error('💥 PAUSE JOB ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to pause job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bulk job pause API endpoint. Use POST to pause a running job.',
    endpoints: {
      'POST /api/bulk-pages/pause-job': 'Pause a running bulk generation job'
    }
  });
}
