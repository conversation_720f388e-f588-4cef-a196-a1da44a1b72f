"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>Right, CheckCircle2, Monitor, Search, MousePointer, Code, Share2, Zap, <PERSON>rkles, Star } from "lucide-react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

interface ServiceCard {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  keyComponents: string[];
  link: {
    text: string;
    href: string;
  };
  gradient: string;
  iconBg: string;
}

const services: ServiceCard[] = [
  {
    id: "web-design",
    icon: <Monitor className="h-8 w-8" />,
    title: "Professional Web Design & Development Services",
    description: "Leading web design company specializing in custom website design, responsive web development, e-commerce solutions, and mobile-first design. We create high-performing websites that convert visitors into customers using modern UI/UX design principles and conversion optimization techniques.",
    keyComponents: [
      "Custom Website Design & Responsive Web Development",
      "E-commerce Development (Shopify, WooCommerce, Magento)",
      "WordPress Development & Custom CMS Solutions",
      "UI/UX Design & User Experience Optimization",
      "Website Redesign & Modernization Services",
      "Progressive Web Apps (PWA) & Mobile App Development"
    ],
    link: {
      text: "Web Design Services",
      href: "/services/web-design"
    },
    gradient: "from-[#1E3A8A] to-[#1E40AF]",
    iconBg: "bg-[#1E3A8A]"
  },
  {
    id: "seo",
    icon: <Search className="h-8 w-8" />,
    title: "SEO Services & Search Engine Optimization",
    description: "Professional SEO services that drive organic traffic and improve search rankings. Our comprehensive SEO strategy includes technical SEO, keyword research, content optimization, link building, and local SEO to dominate Google search results and increase online visibility.",
    keyComponents: [
      "Technical SEO Audits & Website Optimization",
      "Keyword Research & Competitor Analysis",
      "On-Page SEO & Content Optimization",
      "Local SEO & Google My Business Optimization",
      "Link Building & Authority Development",
      "SEO Analytics & Performance Reporting"
    ],
    link: {
      text: "SEO Services",
      href: "/services/seo"
    },
    gradient: "from-[#FF6B35] to-[#FF8A65]",
    iconBg: "bg-[#FF6B35]"
  },
  {
    id: "ppc",
    icon: <MousePointer className="h-8 w-8" />,
    title: "Digital Marketing & Social Media Management",
    description: "Comprehensive digital marketing services including Google Ads management, social media marketing, content marketing, email marketing, and PPC advertising. We create data-driven marketing campaigns that increase brand awareness, drive traffic, and generate qualified leads for your business.",
    keyComponents: [
      "Google Ads & PPC Campaign Management",
      "Social Media Marketing (Facebook, Instagram, LinkedIn)",
      "Content Marketing & Blog Writing Services",
      "Email Marketing & Marketing Automation",
      "Conversion Rate Optimization (CRO)",
      "Digital Marketing Analytics & ROI Tracking"
    ],
    link: {
      text: "Digital Marketing",
      href: "/services/digital-marketing"
    },
    gradient: "from-[#1E3A8A] to-[#1E40AF]",
    iconBg: "bg-[#1E3A8A]"
  },
  {
    id: "software-app",
    icon: <Code className="h-8 w-8" />,
    title: "Mobile App & Software Development Services",
    description: "Custom software development and mobile app development services for iOS and Android. We build native mobile apps, web applications, SaaS platforms, custom software solutions, and enterprise applications using cutting-edge technologies and agile development methodologies.",
    keyComponents: [
      "iOS App Development & Android App Development",
      "React Native & Flutter Cross-Platform Development",
      "Custom Software Development & Web Applications",
      "SaaS Development & Cloud-Based Solutions",
      "API Development & Third-Party Integrations",
      "Enterprise Software & Database Development"
    ],
    link: {
      text: "Mobile App Development",
      href: "/services/mobile-app-development"
    },
    gradient: "from-[#FF6B35] to-[#FF8A65]",
    iconBg: "bg-[#FF6B35]"
  },
  {
    id: "social-media",
    icon: <Share2 className="h-8 w-8" />,
    title: "Social Media Marketing & Management Services",
    description: "Professional social media marketing services to grow your online presence and engage your audience. We manage Facebook, Instagram, LinkedIn, Twitter, and YouTube marketing campaigns, create compelling content, and build communities that drive brand awareness and customer loyalty.",
    keyComponents: [
      "Facebook Marketing & Instagram Advertising",
      "LinkedIn Marketing & Twitter Management",
      "YouTube Marketing & Video Content Creation",
      "Social Media Content Creation & Copywriting",
      "Influencer Marketing & Partnership Management",
      "Social Media Analytics & Community Management"
    ],
    link: {
      text: "Social Media Services",
      href: "/services/social-media"
    },
    gradient: "from-[#1E3A8A] to-[#1E40AF]",
    iconBg: "bg-[#1E3A8A]"
  }
];

export default function SectionServices() {
  // Premium animations
  const fadeInUp = {
    hidden: { opacity: 0, y: 32 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const cardHover = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.02,
      y: -8,
      transition: { duration: 0.3, ease: [0.21, 1, 0.81, 1] as const }
    }
  };

  return (
    <section className="relative py-24 lg:py-32 bg-white overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Header Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="mb-8">
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm"
            >
              <Sparkles className="h-6 w-6 animate-pulse text-[#FF6B35]" />
              <span className="tracking-wide">PREMIUM DIGITAL SOLUTIONS</span>
            </Badge>
          </motion.div>

          <motion.h2
            variants={fadeInUp}
            className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-10"
          >
            <span className="block text-[#1E3A8A] mb-3">Comprehensive Digital Services</span>
            <span className="block text-[#FF6B35]">
              Web Design, SEO, Apps & Marketing
            </span>
          </motion.h2>

          <motion.p
            variants={fadeInUp}
            className="text-xl lg:text-2xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-medium"
          >
            Professional web design company offering custom website development, mobile app development, SEO optimization, digital marketing, and software development services. Transform your business with our comprehensive digital solutions.
          </motion.p>
        </motion.div>

        {/* Premium Services Grid */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 lg:gap-10"
        >
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              variants={fadeInUp}
              initial="rest"
              whileHover="hover"
              className="group"
            >
              <Card className="relative h-full bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 transition-all duration-500 rounded-3xl overflow-hidden group-hover:scale-[1.02] group-hover:border-[#FF6B35]/30">
                {/* Premium gradient accent */}
                <div className={`absolute inset-0 bg-gradient-to-r ${service.gradient} p-px rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-500`}>
                  <div className="w-full h-full bg-white rounded-3xl"></div>
                </div>

                <CardHeader className="relative p-8">
                  {/* Service Icon */}
                  <motion.div
                    className={`inline-flex items-center justify-center w-20 h-20 ${service.iconBg} rounded-3xl text-white shadow-2xl shadow-[#1E3A8A]/25 group-hover:scale-110 transition-transform duration-300 mb-6`}
                    whileHover={{ rotate: 5 }}
                  >
                    {service.icon}
                  </motion.div>

                  <CardTitle className="text-3xl lg:text-4xl font-black text-[#1E3A8A] leading-tight mb-4">
                    {service.title}
                  </CardTitle>

                  <CardDescription className="text-lg text-slate-600 leading-relaxed font-medium">
                    {service.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="relative flex-1 px-8">
                  {/* Key Components */}
                  <div className="space-y-4 mb-8">
                    {service.keyComponents.slice(0, 4).map((component, idx) => (
                      <motion.div
                        key={idx}
                        className="flex items-start space-x-4"
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: idx * 0.1 }}
                      >
                        <motion.div
                          className="w-6 h-6 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md mt-0.5 flex-shrink-0"
                          whileHover={{ rotate: 10 }}
                        >
                          <CheckCircle2 className="h-3 w-3 text-white" />
                        </motion.div>
                        <span className="text-[#1E3A8A] text-base font-semibold">{component}</span>
                      </motion.div>
                    ))}
                  </div>

                  {service.keyComponents.length > 4 && (
                    <Badge className="bg-gradient-to-r from-[#FF6B35]/15 to-[#1E3A8A]/15 text-[#1E3A8A] border border-[#FF6B35]/30 mb-6 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 rounded-full backdrop-blur-sm">
                      <Star className="h-4 w-4 mr-2 fill-[#FF6B35] text-[#FF6B35]" />
                      +{service.keyComponents.length - 4} more features
                    </Badge>
                  )}
                </CardContent>

                <CardFooter className="relative p-8">
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="w-full">
                    <Button
                      className={`w-full h-14 bg-gradient-to-r ${service.gradient} text-white text-lg font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/30 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 border-2 border-[#FF6B35]/20`}
                    >
                      {service.link.text}
                      <ArrowRight className="h-5 w-5 ml-3 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </motion.div>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Premium CTA Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          className="text-center mt-24"
        >
          <Card className="bg-gradient-to-br from-[#1E3A8A] via-[#1E40AF] to-[#1E3A8A] text-white relative overflow-hidden shadow-2xl border-0 rounded-3xl">
            {/* Background accent */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-[#FF6B35]/30 to-transparent rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-radial from-[#4CAF50]/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

            <CardContent className="relative p-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Badge className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#FF6B35]/20 via-white/10 to-[#FF6B35]/20 text-[#FF6B35] border border-[#FF6B35]/30 mb-8 text-lg font-bold rounded-full backdrop-blur-sm">
                  <Sparkles className="h-6 w-6 mr-3 animate-pulse" />
                  PREMIUM SERVICES
                </Badge>
              </motion.div>

              <h3 className="text-5xl lg:text-6xl font-black mb-8 leading-tight">
                Ready to Transform Your Digital Presence?
              </h3>
              <p className="text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed font-medium">
                Let's discuss how our premium services can accelerate your growth and dominate your market.
              </p>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] text-white px-12 py-6 text-xl font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/40 focus:outline-none focus:ring-4 focus:ring-[#FF6B35]/30 transition-all duration-500 border-2 border-[#FF6B35]/20"
                >
                  <Zap className="h-6 w-6 mr-3" />
                  Get Your Strategy Session
                  <ArrowRight className="h-6 w-6 ml-3" />
                </Button>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
