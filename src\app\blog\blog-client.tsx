"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Calendar, Clock, ArrowRight, TrendingUp, AlertCircle, Sparkles, BookOpen, Users, Target
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  publishedAt: string;
  images: { [key: string]: string };
  featuredImageUrl: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    focusKeyword: string;
    canonicalUrl: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterTitle: string;
    twitterDescription: string;
    twitterImage: string;
    author: string;
    category: string;
    tags: string[];
    readingTime: number;
    wordCount: number;
  };
}

export default function BlogClient() {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch blog posts
  const fetchBlogPosts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/blog/list');
      const data = await response.json();

      if (data.success) {
        // Accept both 'blogPosts' (current API) and 'posts' (legacy) keys
        setBlogPosts((data.blogPosts ?? data.posts) || []);
      } else {
        setError(data.error || 'Failed to fetch blog posts');
      }
    } catch (err) {
      setError('Failed to fetch blog posts');
      console.error('Error fetching blog posts:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReadingTime = (post: BlogPost) => {
    return `${post.seo.readingTime} min read`;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-white overflow-hidden py-24">
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center">

            {/* Left Column - Content */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-8 space-y-10"
            >
              <motion.div variants={fadeInUp} className="space-y-8">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                >
                  <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-[#FF6B35]/5 to-[#1E3A8A]/5 text-[#1E3A8A] border border-[#1E3A8A]/20 hover:shadow-lg hover:shadow-[#1E3A8A]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                    <Sparkles className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                    <span className="font-bold text-sm tracking-wide">EXPERT INSIGHTS & TIPS</span>
                  </Badge>
                </motion.div>

                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black tracking-tight leading-[1.1] mb-8">
                  <motion.span
                    className="block text-[#1E3A8A] mb-2"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.8 }}
                  >
                    Digital Marketing
                  </motion.span>
                  <motion.span
                    className="block relative"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.8 }}
                  >
                    <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                      Insights & Tips
                    </span>
                    <motion.div
                      className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                    />
                  </motion.span>
                </h1>

                <motion.p
                  className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  Stay ahead of the curve with expert insights on web design, SEO strategies, digital marketing trends, and industry best practices from our team of professionals.
                </motion.p>

                {/* Key Topics */}
                <motion.div
                  className="flex flex-wrap gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9, duration: 0.8 }}
                >
                  {[
                    { icon: <Target className="h-4 w-4" />, text: "SEO Strategies" },
                    { icon: <TrendingUp className="h-4 w-4" />, text: "Digital Marketing" },
                    { icon: <BookOpen className="h-4 w-4" />, text: "Web Design Tips" },
                    { icon: <Users className="h-4 w-4" />, text: "Industry Insights" }
                  ].map((topic, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-[#1E3A8A]/5 to-[#FF6B35]/5 border border-[#1E3A8A]/10 rounded-full hover:shadow-md transition-all duration-300"
                      whileHover={{ scale: 1.05, y: -2 }}
                    >
                      <div className="text-[#FF6B35]">{topic.icon}</div>
                      <span className="text-sm font-medium text-[#1E3A8A]">{topic.text}</span>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Right Column - Visual Element */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-4"
            >
              <div className="relative">
                <div className="w-full h-80 bg-gradient-to-br from-[#1E3A8A]/10 via-[#FF6B35]/5 to-[#1E3A8A]/10 rounded-3xl border border-[#1E3A8A]/10 flex items-center justify-center">
                  <div className="text-center space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-3xl flex items-center justify-center mx-auto">
                      <BookOpen className="h-10 w-10 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-[#1E3A8A] mb-2">Latest Articles</h3>
                      <p className="text-slate-600">Expert insights delivered weekly</p>
                    </div>
                  </div>
                </div>

                {/* Floating elements */}
                <motion.div
                  className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] rounded-2xl flex items-center justify-center shadow-lg"
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                >
                  <Sparkles className="h-8 w-8 text-white" />
                </motion.div>

                <motion.div
                  className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-xl flex items-center justify-center shadow-lg"
                  animate={{ y: [0, 10, 0] }}
                  transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                >
                  <TrendingUp className="h-6 w-6 text-white" />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Blog Posts Section */}
      <section className="py-24 bg-gradient-to-b from-white to-slate-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="text-center py-20"
            >
              <motion.div variants={fadeInUp}>
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-full flex items-center justify-center mx-auto mb-6 animate-spin">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Loading Latest Insights...</h3>
                <p className="text-gray-600">Fetching the latest digital marketing tips and strategies for you.</p>
              </motion.div>
            </motion.div>
          ) : error ? (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="text-center py-20"
            >
              <motion.div variants={fadeInUp}>
                <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <AlertCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Unable to Load Blog Posts</h3>
                <p className="text-gray-600 mb-6">{error}</p>
                <Button 
                  onClick={fetchBlogPosts}
                  className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
                >
                  Try Again
                </Button>
              </motion.div>
            </motion.div>
          ) : blogPosts.length === 0 ? (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="text-center py-20"
            >
              <motion.div variants={fadeInUp}>
                <div className="w-16 h-16 bg-gradient-to-r from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-6">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Coming Soon!</h3>
                <p className="text-gray-600 mb-6">We're working on bringing you amazing content. Check back soon for expert insights and tips.</p>
                <Link href="/contact">
                  <Button className="bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                    Get Notified
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </motion.div>
            </motion.div>
          ) : (
            <>
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-100px" }}
                variants={staggerContainer}
                className="text-center mb-16"
              >
                <motion.div variants={fadeInUp} className="space-y-6">
                  <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                    Latest
                    <span className="block text-[#FF6B35]">Articles</span>
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Discover actionable strategies and expert insights to grow your business online.
                  </p>
                </motion.div>
              </motion.div>

              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-100px" }}
                variants={staggerContainer}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {blogPosts.map((post, index) => (
                  <motion.div
                    key={post.slug}
                    variants={fadeInUp}
                    whileHover={{ y: -8 }}
                    transition={{ type: "spring", stiffness: 300, damping: 25 }}
                  >
                    <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                      <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                      {post.featuredImageUrl && (
                        <div className="relative h-48 overflow-hidden">
                          <img
                            src={post.featuredImageUrl}
                            alt={post.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                        </div>
                      )}

                      <CardContent className="relative p-6">
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(post.publishedAt)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{getReadingTime(post)}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 mb-4">
                          <Badge variant="outline" className="border-[#FF6B35]/30 text-[#FF6B35] text-xs">
                            {post.seo.category}
                          </Badge>
                          {post.seo.tags.slice(0, 2).map((tag, tagIndex) => (
                            <Badge key={tagIndex} variant="outline" className="border-[#1E3A8A]/30 text-[#1E3A8A] text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-[#1E3A8A] transition-colors duration-300">
                          {post.title}
                        </h3>

                        <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                          {post.excerpt}
                        </p>

                        <Link href={`/blog/${post.slug}`}>
                          <Button className="w-full bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                            Read More
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Button>
                        </Link>
                      </CardContent>

                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </>
          )}
        </div>
      </section>
    </div>
  );
}
