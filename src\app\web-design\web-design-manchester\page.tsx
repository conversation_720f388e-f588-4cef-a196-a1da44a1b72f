import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Design Manchester Service Configuration for web-design-manchester
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Design Manchester",
  "serviceSlug": "web-design-manchester",
  "metaTitle": "Web Design Manchester | WebforLeads",
  "metaDescription": "Top-rated web design agency in Manchester.  Boost your online presence with WebforLeads. Get a free consultation today!",
  "keywords": [
    "web design Manchester",
    "website design Manchester",
    "web developer Manchester",
    "eCommerce website Manchester"
  ],
  "heroTitle": "Stunning Websites That Drive Leads in Manchester",
  "heroSubtitle": "WebforLeads: Your Manchester Web Design Experts",
  "heroDescription": "From the vibrant Northern Quarter to the iconic Trafford Centre, WebforLeads helps Manchester businesses thrive online.  We craft bespoke websites that are not only beautiful but also generate real results.  Let us help you stand out from the competition.",
  "heroBadgeText": "Google & Facebook Marketing Partner",
  "stats": [
    {
      "number": "500%",
      "label": "Average Lead Generation Increase",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Websites Launched Since 2019",
      "icon": <Target className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Satisfaction Rate",
      "icon": <Users className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Average Response Time",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic Design",
      "description": "We create websites aligned with your business goals and target audience.  No generic templates here!",
      "highlight": "User-centered design"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Performance Optimization",
      "description": "Fast loading speeds and SEO best practices ensure your website ranks high on Google.",
      "highlight": "Mobile-first approach"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion-Focused Design",
      "description": "We design websites that convert visitors into customers.  Every click counts.",
      "highlight": "Clear calls-to-action"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Data-Driven Decisions",
      "description": "We track key metrics and continuously optimize your website for maximum impact.",
      "highlight": "Regular performance reports"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "SEO Optimization",
      "description": "Improve your search engine rankings and attract more organic traffic."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Responsive Design",
      "description": "Your website will look great on all devices (desktops, tablets, and smartphones)."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "E-commerce Solutions",
      "description": "We build robust and scalable e-commerce websites for online businesses."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Fast Loading Speeds",
      "description": "Ensure a smooth and enjoyable user experience with optimized website speed."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Custom Website Design",
      "description": "Tailored solutions to perfectly match your brand and business needs."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "User-Friendly Interface",
      "description": "Intuitive navigation and a seamless user experience for your visitors."
    }
  ],
  "packages": [
    {
      "name": "Starter Website",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for small businesses needing a basic, functional website.",
      "features": [
        "Responsive Design",
        "SEO Optimization",
        "5 Pages"
      ],
      "highlight": false,
      "cta": "Learn More"
    },
    {
      "name": "Professional Website",
      "price": "£7,500",
      "period": "one-time",
      "description": "A feature-rich website perfect for established businesses aiming to elevate their online presence.",
      "features": [
        "Responsive Design",
        "SEO Optimization",
        "10 Pages",
        "E-commerce Integration (basic)"
      ],
      "highlight": true,
      "cta": "Get a Quote"
    },
    {
      "name": "Bespoke Website Design",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored website solutions for complex projects and unique requirements.",
      "features": [
        "All features above",
        "Custom Development"
      ],
      "highlight": false,
      "cta": "Contact Us"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Planning",
      "description": "We discuss your needs, goals, and target audience.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "We create wireframes, mockups, and develop your website.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Testing & Refinement",
      "description": "Thorough testing and optimization to ensure a flawless launch.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Support",
      "description": "We launch your website and provide ongoing support and maintenance.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads completely transformed our online presence.  Our website is now a lead generation machine!",
    "name": "Sarah Jones",
    "role": "Marketing Manager",
    "company": "Northern Soul Clothing (Manchester)",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Website Traffic Increase"
  },
  "testimonials": [
    {
      "quote": "Amazing work from WebforLeads! Our new website is stunning and has already increased our sales.",
      "name": "Mark Smith",
      "role": "Owner",
      "company": "Manchester Coffee Roasters",
      "industry": "Food & Beverage",
      "metric": "315%",
      "metricLabel": "Sales Increase",
      "avatar": "MC"
    },
    {
      "quote": "Highly recommend WebforLeads. They are professional, responsive, and delivered exactly what we needed.",
      "name": "Emily Roberts",
      "role": "Director",
      "company": "The Alchemist (Manchester)",
      "industry": "Hospitality",
      "metric": "200%",
      "metricLabel": "Lead Generation Increase",
      "avatar": "ER"
    },
    {
      "quote": "WebforLeads exceeded our expectations. Our new website looks fantastic and is incredibly user-friendly.",
      "name": "David Patel",
      "role": "CEO",
      "company": "Manchester Digital",
      "industry": "Technology",
      "metric": "400%",
      "metricLabel": "Brand Awareness Increase",
      "avatar": "DP"
    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to build a website?",
          "a": "The timeline varies depending on the project's complexity, but typically ranges from 4 to 8 weeks."
        },
        {
          "q": "What's included in your web design packages?",
          "a": "Our packages include design, development, testing, and launch.  See detailed package information above."
        },
        {
          "q": "Do you offer ongoing maintenance?",
          "a": "Yes, we offer ongoing maintenance and support packages to ensure your website stays up-to-date and performs optimally."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you build e-commerce websites?",
          "a": "Yes, we build custom e-commerce solutions tailored to your specific needs."
        },
        {
          "q": "What kind of designs do you specialize in?",
          "a": "We create modern, responsive, and user-friendly websites that reflect your brand identity."
        },
        {
          "q": "Do you handle SEO?",
          "a": "Yes, we offer SEO optimization services to improve your website's search engine rankings."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What is your pricing structure?",
          "a": "Our pricing is project-based, ranging from £3,500 to custom quotes for larger projects.  See our packages for details."
        },
        {
          "q": "What platforms do you use?",
          "a": "We utilize various platforms and technologies to build high-performing websites."
        },
        {
          "q": "What is your payment process?",
          "a": "We offer flexible payment plans to suit your budget."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get a Free Consultation",
    "title": "Ready to Transform Your Online Presence?",
    "description": "Contact WebforLeads today to discuss your web design needs. We serve Manchester and the surrounding areas."
  },
  "structuredData": {
    "serviceName": "Web Design",
    "description": "Professional web design services in Manchester, UK. We create stunning, high-converting websites.",
    "priceRange": "£3000-£15000",
    "areaServed": "Manchester, UK",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebDesignManchesterPage() {
  return <ServiceTemplate config={serviceConfig} />;
}