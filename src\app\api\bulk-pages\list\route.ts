import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Interfaces
interface GeneratedPage {
  id: string;
  keyword: string;
  location: string;
  slug: string;
  title: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  createdAt: string;
  filePath?: string;
  error?: string;
  progress?: number;
}

interface BulkGenerationJob {
  id: string;
  name: string;
  templateId: string;
  templateName: string;
  keywords: string[];
  locations: string[];
  status: 'pending' | 'running' | 'completed' | 'paused' | 'error';
  progress: number;
  totalPages: number;
  completedPages: number;
  createdAt: string;
  estimatedCompletion?: string;
  pages: GeneratedPage[];
}

// Get the bulk pages storage directory
function getBulkPagesStorageDir(): string {
  const storageDir = path.join(process.cwd(), 'bulk-pages-storage');
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
  return storageDir;
}

// saveBulkJob function moved to @/lib/bulk-pages-storage.ts

// Get all bulk generation jobs
function getAllBulkJobs(): BulkGenerationJob[] {
  const storageDir = getBulkPagesStorageDir();
  
  try {
    const files = fs.readdirSync(storageDir);
    const jobs: BulkGenerationJob[] = [];

    for (const file of files) {
      if (file.startsWith('job-') && file.endsWith('.json')) {
        const filepath = path.join(storageDir, file);
        const content = fs.readFileSync(filepath, 'utf-8');
        const job = JSON.parse(content) as BulkGenerationJob;
        jobs.push(job);
      }
    }

    // Sort by created date (newest first)
    return jobs.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  } catch (error) {
    console.error('Error reading bulk jobs:', error);
    return [];
  }
}

// Get single bulk job by ID
function getBulkJobById(jobId: string): BulkGenerationJob | null {
  const storageDir = getBulkPagesStorageDir();
  
  try {
    const filename = `job-${jobId}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      const content = fs.readFileSync(filepath, 'utf-8');
      return JSON.parse(content) as BulkGenerationJob;
    }
    
    return null;
  } catch (error) {
    console.error('Error reading bulk job:', error);
    return null;
  }
}

// updateBulkJob function moved to @/lib/bulk-pages-storage.ts

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('jobId');

  try {
    if (jobId) {
      // Get single bulk job
      const job = getBulkJobById(jobId);
      
      if (!job) {
        return NextResponse.json(
          { success: false, error: 'Bulk job not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        job
      });
    } else {
      // Get all bulk jobs
      const jobs = getAllBulkJobs();
      
      return NextResponse.json({
        success: true,
        jobs,
        count: jobs.length
      });
    }
  } catch (error) {
    console.error('Error in bulk pages list API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch bulk jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Delete bulk job
export async function DELETE(request: NextRequest) {
  const { jobId } = await request.json();

  if (!jobId) {
    return NextResponse.json(
      { success: false, error: 'Job ID is required' },
      { status: 400 }
    );
  }

  try {
    const storageDir = getBulkPagesStorageDir();
    const filename = `job-${jobId}.json`;
    const filepath = path.join(storageDir, filename);
    
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath);
      
      return NextResponse.json({
        success: true,
        message: 'Bulk job deleted successfully'
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Bulk job not found' },
      { status: 404 }
    );
  } catch (error) {
    console.error('Error deleting bulk job:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete bulk job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
