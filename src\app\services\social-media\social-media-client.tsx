"use client";

import React from "react";
import { motion } from "framer-motion";
import <PERSON> from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Share2, ArrowRight, CheckCircle, TrendingUp, Users, Award, Star,
  Target, Zap, BarChart3, Globe, Rocket, Shield, Heart,
  MessageSquare, Camera, Video, Megaphone, ChevronRight, Sparkles,
  Trophy, LineChart, Instagram, Facebook, Twitter, Linkedin,
  Brain, Gauge, Eye, ThumbsUp, UserPlus, Calendar
} from "lucide-react";
import AppointmentForm from "@/components/forms/appointment-form";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 }
};

// Premium stats for billion-dollar feel
const stats = [
  { number: "650%", label: "Average Engagement Increase", icon: <TrendingUp className="h-6 w-6" /> },
  { number: "£1.6M+", label: "Revenue Generated", icon: <Target className="h-6 w-6" /> },
  { number: "89%", label: "Client Retention Rate", icon: <Users className="h-6 w-6" /> },
  { number: "24hrs", label: "Response Time", icon: <Rocket className="h-6 w-6" /> }
];

// Premium social media features
const socialFeatures = [
  {
    icon: <Heart className="h-8 w-8" />,
    title: "Community Building & Engagement",
    description: "Strategic community management that builds loyal audiences, drives meaningful conversations, and converts followers into customers.",
    highlight: "Community-Focused"
  },
  {
    icon: <Camera className="h-8 w-8" />,
    title: "Creative Content Production",
    description: "High-quality visual content, videos, and graphics that capture attention, tell your brand story, and drive engagement across all platforms.",
    highlight: "Creative Excellence"
  },
  {
    icon: <Target className="h-8 w-8" />,
    title: "Targeted Social Advertising",
    description: "Data-driven social media advertising campaigns that reach your ideal audience and deliver measurable ROI across all major platforms.",
    highlight: "Performance-Driven"
  },
  {
    icon: <BarChart3 className="h-8 w-8" />,
    title: "Advanced Analytics & Insights",
    description: "Comprehensive social media analytics and reporting that provide actionable insights to optimize performance and maximize results.",
    highlight: "Data-Driven"
  }
];

export default function SocialMediaClient() {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Hero Section */}
      <section className="relative bg-white overflow-hidden min-h-screen">
        {/* Navigation spacing */}
        <div className="h-20 lg:h-24"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 lg:pb-32">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[85vh]">

            {/* Left Column - Premium Content Layout */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-7 space-y-10"
            >
              {/* Premium Headline Section */}
              <motion.div variants={fadeInUp} className="space-y-10">
                <div className="space-y-8">
                  {/* Premium badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 100 }}
                  >
                    <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-white/90 text-[#FF6B35] border border-[#FF6B35]/20 hover:shadow-lg hover:shadow-[#FF6B35]/25 transition-all duration-500 backdrop-blur-sm rounded-full">
                      <Share2 className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                      <span className="font-bold text-sm tracking-wide">SOCIAL MEDIA MARKETING SERVICES</span>
                    </Badge>
                  </motion.div>

                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-[0.9] mb-8">
                    <motion.span
                      className="block text-[#1E3A8A] mb-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.8 }}
                    >
                      Social Media Marketing That
                    </motion.span>
                    <motion.span
                      className="block relative"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    >
                      <span className="bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                        Builds Communities & Drives Sales
                      </span>
                      {/* Enhanced premium underline accent */}
                      <motion.div
                        className="absolute -bottom-4 left-0 h-2 bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] rounded-full shadow-lg shadow-[#FF6B35]/30"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1, duration: 1.2, ease: "easeOut" }}
                      />
                    </motion.span>
                  </h1>
                </div>

                <motion.div
                  className="space-y-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                >
                  <h2 className="text-2xl lg:text-3xl text-[#1E3A8A] leading-relaxed max-w-3xl font-bold">
                    Leading Social Media Marketing Company Specializing in{" "}
                    <span className="text-[#FF6B35] relative">
                      Facebook, Instagram, LinkedIn & TikTok Marketing
                      <motion.div
                        className="absolute -bottom-1 left-0 h-0.5 bg-[#FF6B35]/50 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                      />
                    </span>
                  </h2>

                  <p className="text-xl text-slate-600 leading-relaxed max-w-3xl font-medium">
                    Professional social media marketing services that build brand awareness, engage audiences, and drive conversions. Our expert team creates compelling content and manages communities that turn followers into loyal customers.
                  </p>
                </motion.div>
              </motion.div>

              {/* Premium Trust Indicators */}
              <motion.div
                variants={fadeInUp}
                className="pt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.8 }}
              >
                <div className="flex flex-wrap items-center gap-8">
                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Heart className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Community Building
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <Camera className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Creative Content
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 group cursor-pointer px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-full flex items-center justify-center shadow-md"
                      whileHover={{ rotate: 10 }}
                    >
                      <TrendingUp className="h-4 w-4 text-white" />
                    </motion.div>
                    <span className="text-[#1E3A8A] font-bold text-base group-hover:text-[#FF6B35] transition-colors duration-200">
                      Proven Results
                    </span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Premium Form */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="lg:col-span-5"
            >
              <AppointmentForm />
            </motion.div>
          </div>

          {/* Premium Stats */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={scaleIn}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-3xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-[#1E3A8A] mb-2">{stat.number}</div>
                <div className="text-lg text-slate-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Social Media Features Section */}
      <section className="py-32 mt-16 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-[#FF6B35]/5 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-[#1E3A8A]/5 to-transparent rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <div className="inline-flex items-center space-x-2 px-4 py-2 bg-[#FF6B35]/10 border border-[#FF6B35]/20 rounded-full">
                <Sparkles className="h-4 w-4 text-[#FF6B35]" />
                <span className="text-[#FF6B35] font-semibold">Premium Social Media Marketing</span>
              </div>

              <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Social Media That
                <span className="block text-[#FF6B35]">Builds Communities & Drives Sales</span>
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our comprehensive social media marketing services combine creative content with strategic community management to build loyal audiences and drive meaningful business results.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {socialFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="relative h-full border-0 bg-white shadow-xl shadow-gray-900/5 rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gray-900/10 transition-all duration-500">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <CardContent className="relative p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/80 rounded-2xl flex items-center justify-center text-white flex-shrink-0">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#1E3A8A]/10 text-[#1E3A8A] text-xs font-semibold rounded-full mb-2">
                          {feature.highlight}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                      </div>
                    </div>

                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </CardContent>

                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>



      {/* Premium Pricing Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Users className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">SOCIAL MEDIA PACKAGES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Choose Your
                <span className="block text-[#FF6B35]">Social Media Package</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From startup presence to enterprise social strategy, we have the perfect package to build your community.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16"
          >
            {[
              {
                name: "Social Starter",
                price: "£800",
                period: "/month",
                description: "Perfect for small businesses",
                features: [
                  "2 social media platforms",
                  "12 posts per month",
                  "Basic content creation",
                  "Community management",
                  "Monthly analytics report",
                  "Hashtag research"
                ],
                highlight: false,
                cta: "Start Social Presence"
              },
              {
                name: "Growth Booster",
                price: "£1,800",
                period: "/month",
                description: "For businesses ready to scale",
                features: [
                  "4 social media platforms",
                  "25 posts per month",
                  "Premium content & graphics",
                  "Story & reel creation",
                  "Influencer outreach",
                  "Weekly performance reports"
                ],
                highlight: true,
                cta: "Boost Growth"
              },
              {
                name: "Brand Authority",
                price: "Custom",
                period: "",
                description: "Enterprise social media solutions",
                features: [
                  "Everything in Growth Booster",
                  "Multi-brand management",
                  "Advanced social advertising",
                  "Dedicated social team",
                  "Crisis management",
                  "Custom reporting dashboard"
                ],
                highlight: false,
                cta: "Build Authority"
              }
            ].map((pkg, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
                className={`relative ${pkg.highlight ? 'lg:-mt-8' : ''}`}
              >
                <Card className={`h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group ${pkg.highlight ? 'ring-2 ring-[#FF6B35] ring-opacity-50' : ''}`}>
                  {pkg.highlight && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] text-white px-4 py-1 rounded-full shadow-lg">
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold text-[#1E3A8A] mb-2">{pkg.name}</h3>
                      <div className="mb-4">
                        <span className="text-4xl font-black text-[#FF6B35]">{pkg.price}</span>
                        <span className="text-slate-500">{pkg.period}</span>
                      </div>
                      <p className="text-slate-600">{pkg.description}</p>
                    </div>

                    <ul className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-[#FF6B35] flex-shrink-0" />
                          <span className="text-slate-700">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <Button 
                      onClick={() => {
                        const el = document.getElementById('appointment-form');
                        if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }}
                      className={`w-full py-3 rounded-2xl font-bold transition-all duration-300 ${
                        pkg.highlight 
                          ? 'bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] hover:from-[#FF8A65] hover:to-[#FF6B35] text-white shadow-lg hover:shadow-xl' 
                          : 'bg-[#1E3A8A] hover:bg-[#1E40AF] text-white'
                      }`}
                    >
                      {pkg.cta}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Process Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Sparkles className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">SOCIAL MEDIA PROCESS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Our Proven
                <span className="block text-[#FF6B35]">Social Media Process</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                From strategy to community building, our systematic approach ensures authentic engagement and brand growth.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                step: "01",
                title: "Strategy & Planning",
                description: "Develop a comprehensive social media strategy based on your brand, audience, and business goals.",
                icon: <Target className="h-8 w-8" />
              },
              {
                step: "02", 
                title: "Content Creation",
                description: "Create engaging, on-brand content including posts, stories, reels, and graphics that resonate with your audience.",
                icon: <Sparkles className="h-8 w-8" />
              },
              {
                step: "03",
                title: "Community Management",
                description: "Actively engage with your audience, respond to comments, and build meaningful relationships with followers.",
                icon: <Users className="h-8 w-8" />
              },
              {
                step: "04",
                title: "Analyze & Optimize",
                description: "Monitor performance, analyze engagement metrics, and continuously optimize strategy for better results.",
                icon: <TrendingUp className="h-8 w-8" />
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF8A65] flex items-center justify-center text-white mx-auto mb-4">
                        {process.icon}
                      </div>
                      <div className="text-6xl font-black text-[#1E3A8A]/10 mb-4">{process.step}</div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-[#1E3A8A] mb-4 group-hover:text-[#FF6B35] transition-colors">
                      {process.title}
                    </h3>
                    
                    <p className="text-slate-600 leading-relaxed">
                      {process.description}
                    </p>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Testimonials Section */}
      <section className="py-32 bg-gradient-to-b from-slate-50/50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B35]/5 via-transparent to-[#1E3A8A]/5"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Trophy className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">CLIENT SUCCESS STORIES</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Social Media That
                <span className="block text-[#FF6B35]">Builds Communities</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                See how our social media strategies have transformed brands with engaged communities, viral content, and measurable business growth.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {[
              {
                quote: "Our Instagram following grew from 2K to 85K in 10 months. Engagement rate increased to 8.4% and social media leads now account for 40% of our new business.",
                name: "Jessica Parker",
                role: "Brand Manager",
                company: "StyleCraft Boutique",
                industry: "Fashion Retail",
                metric: "85K",
                metricLabel: "Instagram Followers",
                avatar: "JP"
              },
              {
                quote: "LinkedIn strategy generated £320K in B2B sales within 6 months. Our thought leadership content positioned us as industry experts with 15x more qualified leads.",
                name: "David Wilson",
                role: "CEO",
                company: "TechConsult Pro",
                industry: "Business Consulting",
                metric: "£320K",
                metricLabel: "B2B Sales",
                avatar: "DW"
              },
              {
                quote: "TikTok campaigns reached 2.3M users with 450K engagements. Brand awareness increased by 280% among our target demographic aged 18-35.",
                name: "Maria Rodriguez",
                role: "Marketing Director",
                company: "FreshFit Nutrition",
                industry: "Health & Wellness",
                metric: "2.3M",
                metricLabel: "TikTok Reach",
                avatar: "MR"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <Card className="h-full border-0 rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-500 group">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-1 text-[#FF6B35] mb-4">
                      {[...Array(5)].map((_, s) => <Star key={s} className="h-4 w-4 fill-current" />)}
                    </div>
                    
                    <blockquote className="text-slate-700 leading-relaxed mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] flex items-center justify-center text-white font-bold text-sm">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="text-sm font-bold text-[#1E3A8A]">{testimonial.name}</div>
                          <div className="text-xs text-slate-500">{testimonial.role}</div>
                          <div className="text-xs text-[#FF6B35] font-semibold">{testimonial.company}</div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-black text-[#FF6B35]">{testimonial.metric}</div>
                        <div className="text-xs text-slate-500 font-semibold">{testimonial.metricLabel}</div>
                      </div>
                    </div>
                  </CardContent>
                  
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium FAQs Section */}
      <section className="py-32 bg-gradient-to-b from-white to-slate-50/50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A8A]/5 via-transparent to-[#FF6B35]/5"></div>
        </div>
        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center mb-20"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/10 via-white/80 to-[#1E3A8A]/10 text-[#1E3A8A] border border-[#FF6B35]/20 rounded-full">
                <Users className="h-5 w-5 mr-2 text-[#FF6B35]" />
                <span className="font-bold text-sm tracking-wide">FREQUENTLY ASKED QUESTIONS</span>
              </Badge>

              <h2 className="text-4xl lg:text-6xl font-bold text-[#1E3A8A] leading-tight">
                Social Media
                <span className="block text-[#FF6B35]">Expert Guidance</span>
              </h2>

              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Get answers to the most common questions about social media strategy, content creation, and building engaged online communities.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {[
              {
                category: "Strategy & Planning",
                icon: <Target className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you develop a social media strategy that works?",
                    a: "We start with audience research, competitor analysis, and platform selection based on your target demographics. Our strategy includes content pillars, posting schedules, engagement tactics, and measurable KPIs aligned with your business goals."
                  },
                  {
                    q: "Which social media platforms should my business focus on?",
                    a: "Platform selection depends on your target audience, industry, and business objectives. We analyze where your customers spend time online and recommend 2-3 primary platforms for focused, high-quality content rather than spreading thin across all channels."
                  }
                ]
              },
              {
                category: "Content Creation",
                icon: <Sparkles className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you create engaging content that gets results?",
                    a: "We create content that educates, entertains, and inspires your audience. Our approach includes storytelling, user-generated content, behind-the-scenes content, and interactive posts that encourage engagement and build authentic connections with your community."
                  },
                  {
                    q: "How often should we post on social media?",
                    a: "Posting frequency varies by platform and audience. Generally, we recommend 1-2 daily posts on Instagram, 3-5 weekly posts on Facebook, daily LinkedIn content for B2B, and 3-5 weekly TikTok videos. Quality always trumps quantity."
                  }
                ]
              },
              {
                category: "Growth & Engagement",
                icon: <TrendingUp className="h-6 w-6" />,
                questions: [
                  {
                    q: "How do you grow followers and increase engagement?",
                    a: "We use organic growth strategies including hashtag research, community engagement, collaborations, user-generated content campaigns, and consistent posting. We also leverage platform-specific features like Stories, Reels, and live streaming for maximum reach."
                  },
                  {
                    q: "How do you measure social media ROI and success?",
                    a: "We track engagement rates, reach, impressions, website traffic, lead generation, and conversions. Our monthly reports include audience growth, top-performing content, engagement analytics, and recommendations for optimization based on data insights."
                  }
                ]
              },
              {
                category: "Management & Support",
                icon: <Users className="h-6 w-6" />,
                questions: [
                  {
                    q: "What's included in your social media management service?",
                    a: "Our comprehensive service includes strategy development, content creation and curation, posting and scheduling, community management, hashtag research, performance analytics, monthly reporting, and ongoing optimization based on results."
                  },
                  {
                    q: "How do you handle negative comments or social media crises?",
                    a: "We monitor your social media 24/7 and respond to comments professionally and promptly. For negative feedback, we follow established protocols to address concerns publicly when appropriate and move sensitive discussions to private channels."
                  }
                ]
              }
            ].map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={fadeInUp}
                className="space-y-6"
              >
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[#FF6B35] to-[#FF6B35]/80 flex items-center justify-center text-white">
                    {category.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">{category.category}</h3>
                </div>

                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <motion.div
                      key={faqIndex}
                      variants={fadeInUp}
                      whileHover={{ y: -2 }}
                      transition={{ type: "spring", stiffness: 300, damping: 25 }}
                    >
                      <Card className="border-0 rounded-2xl shadow-lg bg-white hover:shadow-xl transition-all duration-300 group">
                        <CardContent className="p-8">
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 rounded-full bg-[#FF6B35]/10 flex items-center justify-center flex-shrink-0 mt-1">
                              <div className="w-2 h-2 rounded-full bg-[#FF6B35]"></div>
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-bold text-[#1E3A8A] mb-3 group-hover:text-[#FF6B35] transition-colors">
                                {faq.q}
                              </h4>
                              <p className="text-slate-600 leading-relaxed">
                                {faq.a}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                        
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FF6B35] to-[#1E3A8A] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="appointment-form" className="py-24 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <Badge className="px-4 py-1 bg-white/10 text-white border border-white/20 rounded-full">Get Started</Badge>
              <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">Ready to Build Your Community?</h2>
              <p className="text-slate-300 text-lg max-w-xl">Let's create a social media presence that engages your audience, builds brand loyalty, and drives meaningful business results.</p>
            </div>
            <div>
              <AppointmentForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
