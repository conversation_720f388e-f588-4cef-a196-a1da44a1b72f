"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import ContactForm from "@/components/forms/contact-form";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  CheckCircle,
  Shield,
  Users,
  Rocket,
  Calendar,
  Star
} from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const contactMethods = [
  {
    icon: <Phone className="h-6 w-6" />,
    title: "Call Us",
    description: "Speak directly with our experts",
    value: "+44 20 7946 0958",
    action: "Call Now",
    color: "from-green-500 to-green-600"
  },
  {
    icon: <Mail className="h-6 w-6" />,
    title: "Email Us",
    description: "Get a response within 2 hours",
    value: "<EMAIL>",
    action: "Send Email",
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: <Calendar className="h-6 w-6" />,
    title: "Book a Call",
    description: "Schedule your free consultation",
    value: "30-minute strategy session",
    action: "Book Now",
    color: "from-orange-500 to-orange-600"
  }
];

const benefits = [
  {
    icon: <Shield className="h-5 w-5" />,
    text: "No obligation consultation"
  },
  {
    icon: <Users className="h-5 w-5" />,
    text: "Direct access to senior team"
  },
  {
    icon: <CheckCircle className="h-5 w-5" />,
    text: "Actionable insights guaranteed"
  },
  {
    icon: <Star className="h-5 w-5" />,
    text: "5-star rated service"
  }
];

export default function SectionContact() {

  return (
    <section className="py-32 bg-white relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
          className="text-center mb-20"
        >
          <motion.div variants={fadeInUp} className="space-y-8">
            <Badge
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-[#FF6B35]/15 via-white/90 to-[#1E3A8A]/10 border border-[#FF6B35]/30 text-[#1E3A8A] text-base font-bold rounded-full hover:shadow-xl hover:shadow-[#FF6B35]/20 transition-all duration-500 backdrop-blur-sm"
            >
              <Rocket className="h-6 w-6 animate-pulse text-[#FF6B35]" />
              <span className="tracking-wide">READY TO GET STARTED?</span>
            </Badge>

            <h2 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight leading-[0.9]">
              <span className="block text-[#1E3A8A] mb-3">Let's Grow Your Business</span>
              <span className="block bg-gradient-to-r from-[#FF6B35] via-[#FF8A65] to-[#FF6B35] bg-clip-text text-transparent animate-gradient-premium">
                Together
              </span>
            </h2>

            <p className="text-2xl lg:text-3xl text-slate-600 leading-relaxed max-w-5xl mx-auto font-medium">
              Ready to transform your digital presence? Get in touch for a free consultation and discover how we can help you dominate your local market.
            </p>
          </motion.div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Methods */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="space-y-8"
          >
            <motion.div variants={fadeInUp}>
              <h3 className="text-3xl lg:text-4xl font-black text-[#1E3A8A] mb-8">Get In Touch</h3>
            </motion.div>

            {contactMethods.map((method, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                whileHover={{ scale: 1.02, x: 4 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
              >
                <div className="flex items-center space-x-6 p-6 bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#FF6B35]/20 rounded-3xl hover:border-[#FF6B35]/30 transition-all duration-500">
                  <div className={`w-16 h-16 bg-gradient-to-r ${method.color} rounded-2xl flex items-center justify-center text-white flex-shrink-0 shadow-2xl`}>
                    {method.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-xl font-black text-[#1E3A8A] mb-2">{method.title}</h4>
                    <p className="text-slate-600 text-base mb-2 font-medium">{method.description}</p>
                    <p className="text-[#FF6B35] font-black text-base">{method.value}</p>
                  </div>
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#FF6B35] to-[#FF8A65] text-white border-0 px-6 py-3 text-base font-black rounded-2xl shadow-2xl hover:shadow-[#FF6B35]/30 transition-all duration-500 flex-shrink-0"
                  >
                    {method.action}
                  </Button>
                </div>
              </motion.div>
            ))}

            {/* Office Info */}
            <motion.div variants={fadeInUp}>
              <div className="flex items-start space-x-6 p-6 bg-white/95 backdrop-blur-xl border-2 border-slate-200/50 shadow-2xl hover:shadow-[#1E3A8A]/20 rounded-3xl hover:border-[#1E3A8A]/30 transition-all duration-500">
                <div className="w-16 h-16 bg-gradient-to-br from-[#1E3A8A] to-[#1E40AF] rounded-2xl flex items-center justify-center text-white flex-shrink-0 shadow-2xl shadow-[#1E3A8A]/25">
                  <MapPin className="h-8 w-8" />
                </div>
                <div>
                  <h4 className="text-xl font-black text-[#1E3A8A] mb-3">Our Office</h4>
                  <p className="text-slate-600 text-base mb-3 font-medium">
                    123 Digital Street<br />
                    London, EC1A 1BB<br />
                    United Kingdom
                  </p>
                  <div className="flex items-center space-x-3 text-[#FF6B35]">
                    <Clock className="h-5 w-5" />
                    <span className="text-base font-bold">Mon-Fri: 9AM-6PM GMT</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Benefits */}
            <motion.div variants={fadeInUp} className="space-y-6">
              <h4 className="text-2xl font-black text-[#1E3A8A]">What You Get:</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-[#4CAF50] to-[#45A049] rounded-2xl flex items-center justify-center text-white shadow-lg">
                      {benefit.icon}
                    </div>
                    <span className="text-[#1E3A8A] font-bold text-base">{benefit.text}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
          >
            <ContactForm />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
